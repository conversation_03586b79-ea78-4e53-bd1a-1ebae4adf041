"""
Loop Parameter Resolver - Dynamic parameter resolution for loop configurations.

This module handles the dynamic resolution of loop parameters from various sources:
- Tool parameters from transition schema
- Input data from connected transitions
- Pre-built loop configurations
- Default values and validation
"""

from typing import Dict, List, Any, Optional
from app.utils.enhanced_logger import get_logger

logger = get_logger("LoopParameterResolver")


class LoopParameterResolver:
    """
    Resolves loop configuration parameters dynamically from multiple sources.
    
    This class handles the complexity of merging parameters from tool_params,
    input data mappings, and pre-built configurations while maintaining
    flexibility for different iteration source types.
    """
    
    def __init__(self, workflow_utils, state_manager, logger):
        """
        Initialize the parameter resolver.
        
        Args:
            workflow_utils: Workflow utilities for data resolution
            state_manager: State manager for accessing transition results
            logger: Logger instance
        """
        self.workflow_utils = workflow_utils
        self.state_manager = state_manager
        self.logger = logger
    
    async def resolve_configuration(
        self,
        tool_parameters: Dict[str, Any] = None,
        loop_config: Dict[str, Any] = None,
        input_data: Dict[str, Any] = None,
        input_data_configs: List[Dict[str, Any]] = None,
        transition_id: str = None
    ) -> Dict[str, Any]:
        """
        Resolve complete loop configuration from all available sources.
        
        Args:
            tool_parameters: Parameters from tool_params in transition schema
            loop_config: Pre-built loop configuration
            input_data: Input data for the loop
            input_data_configs: Input data configuration mappings
            transition_id: Current transition ID
            
        Returns:
            Complete resolved loop configuration
        """
        self.logger.info(f"🔧 Resolving loop configuration for transition {transition_id}")
        
        # Start with pre-built config or empty dict
        resolved_config = loop_config.copy() if loop_config else {}
        
        # Step 1: Extract and resolve tool parameters
        if tool_parameters:
            tool_config = await self._resolve_tool_parameters(tool_parameters)
            resolved_config = self._merge_configurations(resolved_config, tool_config)
        
        # Step 2: Resolve input data mappings
        if input_data_configs:
            input_config = await self._resolve_input_data(input_data_configs, input_data)
            resolved_config = self._merge_configurations(resolved_config, input_config)
        
        # Step 3: Apply defaults and validate
        resolved_config = await self._apply_defaults_and_validate(resolved_config)
        
        self.logger.debug(f"📋 Resolved loop configuration: {resolved_config}")
        return resolved_config
    
    async def _resolve_tool_parameters(self, tool_parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Resolve tool parameters from the transition schema.
        
        Tool parameters come as a list of items with field_name, data_type, and field_value.
        This method converts them into a structured configuration.
        """
        config = {}
        
        # Extract items from tool parameters
        items = tool_parameters.get("items", [])
        if not items:
            return config
        
        # Convert items list to key-value pairs
        param_values = {}
        for item in items:
            field_name = item.get("field_name")
            field_value = item.get("field_value")
            data_type = item.get("data_type")
            
            if field_name and field_value is not None:
                # Convert data types as needed
                param_values[field_name] = self._convert_data_type(field_value, data_type)
        
        # Build configuration structure based on source type
        source_type = param_values.get("source_type", "iteration_list")
        
        # Build iteration source configuration
        iteration_source = {"source_type": source_type}
        
        if source_type == "iteration_list":
            iteration_source["iteration_list"] = param_values.get("iteration_list", [])
            iteration_source["batch_size"] = param_values.get("batch_size", 1)
        elif source_type == "number_range":
            iteration_source["number_range"] = {
                "start": param_values.get("start"),
                "end": param_values.get("end")
            }
            iteration_source["step"] = param_values.get("step", 1)
        
        config["iteration_source"] = iteration_source
        
        # Build iteration settings
        config["iteration_settings"] = {
            "parallel_execution": param_values.get("parallel_execution", False),
            "max_concurrent": param_values.get("max_concurrent", 3),
            "preserve_order": param_values.get("preserve_order", True),
            "iteration_timeout": param_values.get("iteration_timeout", 60)
        }
        
        # Build result aggregation
        config["result_aggregation"] = {
            "aggregation_type": param_values.get("aggregation_type", "collect_all"),
            "include_metadata": param_values.get("include_metadata", False)
        }
        
        # Build error handling
        config["error_handling"] = {
            "on_iteration_error": param_values.get("on_iteration_error", "continue"),
            "include_errors": param_values.get("include_errors", True)
        }
        
        return config
    
    async def _resolve_input_data(
        self,
        input_data_configs: List[Dict[str, Any]],
        input_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Resolve input data mappings from connected transitions.
        
        This handles the dynamic resolution of values from previous transitions
        based on handle mappings in the input_data configuration.
        """
        config = {}
        
        for input_config in input_data_configs:
            handle_mappings = input_config.get("handle_mappings", [])
            
            for mapping in handle_mappings:
                source_transition_id = mapping.get("source_transition_id")
                source_handle_id = mapping.get("source_handle_id")
                target_handle_id = mapping.get("target_handle_id")
                
                if not all([source_transition_id, source_handle_id, target_handle_id]):
                    continue
                
                # Get the value from the source transition
                resolved_value = await self._get_transition_result_value(
                    source_transition_id, source_handle_id
                )
                
                if resolved_value is not None:
                    # Map the value to the appropriate configuration location
                    self._apply_input_mapping(config, target_handle_id, resolved_value)
        
        return config
    
    async def _get_transition_result_value(
        self,
        transition_id: str,
        handle_id: str
    ) -> Any:
        """
        Get a specific value from a transition result by handle ID.
        
        This method uses the workflow utilities to resolve values from
        previous transition results.
        """
        try:
            # Get the transition result
            result = self.state_manager.get_transition_result(transition_id)
            if not result:
                self.logger.warning(f"No result found for transition {transition_id}")
                return None
            
            # Use workflow utils to resolve the specific handle value
            if hasattr(self.workflow_utils, 'resolve_input_data'):
                resolved_data = await self.workflow_utils.resolve_input_data(
                    input_data_configs=[{
                        "handle_mappings": [{
                            "source_transition_id": transition_id,
                            "source_handle_id": handle_id,
                            "target_handle_id": handle_id
                        }]
                    }],
                    transitions_by_id=self.state_manager.transitions_by_id if hasattr(self.state_manager, 'transitions_by_id') else {}
                )
                return resolved_data.get(handle_id)
            else:
                # Fallback: try to extract directly from result
                if isinstance(result, dict):
                    return result.get(handle_id)
                return result
                
        except Exception as e:
            self.logger.error(f"Error resolving value from {transition_id}.{handle_id}: {str(e)}")
            return None
    
    def _apply_input_mapping(self, config: Dict[str, Any], target_handle_id: str, value: Any) -> None:
        """
        Apply an input mapping to the configuration structure.
        
        Maps target handle IDs to the appropriate configuration locations.
        """
        if target_handle_id == "iteration_list":
            if "iteration_source" not in config:
                config["iteration_source"] = {}
            config["iteration_source"]["iteration_list"] = value
        elif target_handle_id == "start":
            if "iteration_source" not in config:
                config["iteration_source"] = {}
            if "number_range" not in config["iteration_source"]:
                config["iteration_source"]["number_range"] = {}
            config["iteration_source"]["number_range"]["start"] = value
        elif target_handle_id == "end":
            if "iteration_source" not in config:
                config["iteration_source"] = {}
            if "number_range" not in config["iteration_source"]:
                config["iteration_source"]["number_range"] = {}
            config["iteration_source"]["number_range"]["end"] = value
        elif target_handle_id == "step":
            if "iteration_source" not in config:
                config["iteration_source"] = {}
            config["iteration_source"]["step"] = value
        elif target_handle_id == "batch_size":
            if "iteration_source" not in config:
                config["iteration_source"] = {}
            config["iteration_source"]["batch_size"] = value
    
    def _convert_data_type(self, value: Any, data_type: str) -> Any:
        """Convert value to the specified data type."""
        if data_type == "number" and isinstance(value, str):
            try:
                return int(value) if value.isdigit() else float(value)
            except ValueError:
                return value
        elif data_type == "boolean" and isinstance(value, str):
            return value.lower() in ("true", "1", "yes", "on")
        elif data_type == "array" and isinstance(value, str):
            try:
                import json
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return []
        return value
    
    def _merge_configurations(self, base: Dict[str, Any], overlay: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge two configuration dictionaries, with overlay taking precedence.
        
        Performs deep merge for nested dictionaries.
        """
        result = base.copy()
        
        for key, value in overlay.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configurations(result[key], value)
            else:
                result[key] = value
        
        return result
    
    async def _apply_defaults_and_validate(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply default values and validate the configuration.
        
        Ensures all required fields are present with sensible defaults.
        """
        # Apply defaults for iteration_behavior
        if "iteration_behavior" not in config:
            config["iteration_behavior"] = "independent"
        
        # Apply defaults for exit_condition
        if "exit_condition" not in config:
            config["exit_condition"] = {"condition_type": "all_items_processed"}
        
        # Validate iteration_source
        if "iteration_source" not in config:
            config["iteration_source"] = {
                "source_type": "iteration_list",
                "iteration_list": []
            }
        
        # Ensure iteration_settings has defaults
        iteration_settings = config.get("iteration_settings", {})
        config["iteration_settings"] = {
            "parallel_execution": iteration_settings.get("parallel_execution", False),
            "max_concurrent": iteration_settings.get("max_concurrent", 3),
            "preserve_order": iteration_settings.get("preserve_order", True),
            "iteration_timeout": iteration_settings.get("iteration_timeout", 60)
        }
        
        # Ensure result_aggregation has defaults
        result_aggregation = config.get("result_aggregation", {})
        config["result_aggregation"] = {
            "aggregation_type": result_aggregation.get("aggregation_type", "collect_all"),
            "include_metadata": result_aggregation.get("include_metadata", False)
        }
        
        # Ensure error_handling has defaults
        error_handling = config.get("error_handling", {})
        config["error_handling"] = {
            "on_iteration_error": error_handling.get("on_iteration_error", "continue"),
            "include_errors": error_handling.get("include_errors", True)
        }
        
        return config
