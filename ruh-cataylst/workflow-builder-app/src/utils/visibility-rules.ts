import { InputDefinition, InputVisibilityRule } from "@/types";

/**
 * Evaluates a single visibility rule against the current configuration
 * ENHANCED: Added comprehensive type checking, error handling, and logging
 *
 * @param rule The visibility rule to evaluate
 * @param config The current component configuration
 * @returns True if the rule passes, false otherwise
 */
export function evaluateVisibilityRule(
  rule: InputVisibilityRule,
  config: Record<string, any>,
): boolean {
  try {
    // ENHANCED: Input validation
    if (!rule || typeof rule !== 'object') {
      console.warn('[VISIBILITY] Invalid rule object:', rule);
      return false;
    }

    if (!rule.field_name || typeof rule.field_name !== 'string') {
      console.warn('[VISIBILITY] Invalid field_name in rule:', rule);
      return false;
    }

    // Get the target field value from config
    const targetValue = config?.[rule.field_name];
    const operator = rule.operator || "equals";

    // ENHANCED: Debug logging for complex rules
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[VISIBILITY] Evaluating rule: ${rule.field_name} ${operator} ${rule.field_value}, actual: ${targetValue}`);
    }

    // Simple equality check
    if (operator === "equals") {
      return targetValue === rule.field_value;
    }

    // Not equals
    if (operator === "not_equals") {
      return targetValue !== rule.field_value;
    }

    // Contains (for arrays and strings)
    if (operator === "contains") {
      if (Array.isArray(targetValue)) {
        return targetValue.includes(rule.field_value);
      }
      if (typeof targetValue === "string") {
        return targetValue.includes(String(rule.field_value));
      }
      return false;
    }

    // ENHANCED: Greater than with better type coercion
    if (operator === "greater_than") {
      const numericTarget = Number(targetValue);
      const numericExpected = Number(rule.field_value);

      if (isNaN(numericTarget) || isNaN(numericExpected)) {
        console.warn(`[VISIBILITY] Non-numeric values in greater_than comparison: ${targetValue} > ${rule.field_value}`);
        return false;
      }

      const result = numericTarget > numericExpected;



      return result;
    }

    // ENHANCED: Less than with better type coercion
    if (operator === "less_than") {
      const numericTarget = Number(targetValue);
      const numericExpected = Number(rule.field_value);

      if (isNaN(numericTarget) || isNaN(numericExpected)) {
        console.warn(`[VISIBILITY] Non-numeric values in less_than comparison: ${targetValue} < ${rule.field_value}`);
        return false;
      }

      return numericTarget < numericExpected;
    }

    // Exists (field exists and is not null/undefined)
    if (operator === "exists") {
      return targetValue !== undefined && targetValue !== null;
    }

    // Not exists (field doesn't exist or is null/undefined)
    if (operator === "not_exists") {
      return targetValue === undefined || targetValue === null;
    }

    // ENHANCED: Better error handling for unknown operators
    console.warn(`[VISIBILITY] Unknown operator: ${operator}`);
    return false;

  } catch (error) {
    console.error('[VISIBILITY] Error evaluating visibility rule:', error, rule);
    return false;
  }
}

/**
 * Evaluates a set of visibility rules against the current configuration
 * ENHANCED: Added comprehensive error handling, logging, and validation
 *
 * @param rules The visibility rules to evaluate
 * @param config The current component configuration
 * @param logicOperator The logic operator to use when combining rules (default: 'OR')
 * @returns True if the rules pass, false otherwise
 */
export function evaluateVisibilityRules(
  rules: InputVisibilityRule[],
  config: Record<string, any>,
  logicOperator: "AND" | "OR" = "OR",
): boolean {
  try {
    // ENHANCED: Input validation
    if (!rules || !Array.isArray(rules)) {
      console.warn('[VISIBILITY] Invalid rules array:', rules);
      return true; // Default to visible if rules are invalid
    }

    // If no rules, always show
    if (rules.length === 0) {
      return true;
    }

    // ENHANCED: Validate logic operator
    if (logicOperator !== "AND" && logicOperator !== "OR") {
      console.warn(`[VISIBILITY] Invalid logic operator: ${logicOperator}, defaulting to OR`);
      logicOperator = "OR";
    }

    // Evaluate each rule with error handling
    const results: boolean[] = [];
    for (let i = 0; i < rules.length; i++) {
      try {
        const result = evaluateVisibilityRule(rules[i], config);
        results.push(result);
      } catch (error) {
        console.error(`[VISIBILITY] Error evaluating rule ${i}:`, error, rules[i]);
        // For safety, assume rule fails if there's an error
        results.push(false);
      }
    }

    // ENHANCED: Debug logging for complex rule combinations
    if (process.env.NODE_ENV === 'development' && rules.length > 1) {
      console.debug(`[VISIBILITY] Rule evaluation: [${results.join(', ')}] with ${logicOperator} = ${logicOperator === "AND" ? results.every(r => r) : results.some(r => r)}`);
    }

    // Combine results based on logic operator
    if (logicOperator === "AND") {
      return results.every((result) => result);
    } else {
      return results.some((result) => result);
    }

  } catch (error) {
    console.error('[VISIBILITY] Error evaluating visibility rules:', error);
    return true; // Default to visible if there's an error
  }
}

/**
 * Determines if an input should be visible based on its visibility rules
 * ENHANCED: Added comprehensive validation and conditional node specific logic
 *
 * @param input The input definition
 * @param config The current component configuration
 * @returns True if the input should be visible, false otherwise
 */
export function shouldShowInput(input: InputDefinition, config: Record<string, any>): boolean {
  try {
    // ENHANCED: Input validation
    if (!input || typeof input !== 'object') {
      console.warn('[VISIBILITY] Invalid input object:', input);
      return false;
    }

    // If no visibility rules, always show
    if (!input.visibility_rules || !Array.isArray(input.visibility_rules) || input.visibility_rules.length === 0) {

      return true;
    }

    // ENHANCED: Use the input's visibility logic or default to OR
    const logicOperator = input.visibility_logic === "AND" ? "AND" : "OR";

    const result = evaluateVisibilityRules(
      input.visibility_rules,
      config,
      logicOperator,
    );



    return result;

  } catch (error) {
    console.error('[VISIBILITY] Error in shouldShowInput:', error, input);
    return true; // Default to visible if there's an error
  }
}

/**
 * ENHANCED: Specialized function for conditional node expected_value field visibility
 * Handles the specific case where expected_value should be hidden for exists/is_empty operators
 *
 * @param conditionNumber The condition number (1, 2, 3, etc.)
 * @param config The current component configuration
 * @returns True if the expected_value field should be shown
 */
export function shouldShowConditionalExpectedValue(
  conditionNumber: number,
  config: Record<string, any>,
): boolean {
  try {
    const operatorFieldName = `condition_${conditionNumber}_operator`;
    const operator = config[operatorFieldName];

    // Show expected_value unless operator is exists or is_empty
    const shouldShow = operator !== "exists" && operator !== "is_empty";

    if (process.env.NODE_ENV === 'development') {
      console.debug(`[VISIBILITY] condition_${conditionNumber}_expected_value: ${shouldShow ? 'VISIBLE' : 'HIDDEN'} (operator: ${operator})`);
    }

    return shouldShow;

  } catch (error) {
    console.error(`[VISIBILITY] Error checking expected_value visibility for condition ${conditionNumber}:`, error);
    return true; // Default to visible
  }
}

/**
 * ENHANCED: Specialized function for dynamic conditional node visibility (conditions 3+)
 * Handles the visibility of additional conditions based on num_additional_conditions
 *
 * @param conditionNumber The condition number (3, 4, 5, etc.)
 * @param config The current component configuration
 * @returns True if the condition should be shown
 */
export function shouldShowDynamicCondition(
  conditionNumber: number,
  config: Record<string, any>,
): boolean {
  try {
    if (conditionNumber <= 2) {
      return true; // Conditions 1 and 2 are always visible
    }

    const numAdditionalConditions = Number(config.num_additional_conditions) || 0;
    const minAdditionalNeeded = conditionNumber - 2; // For condition 3: need 1 additional, etc.

    const shouldShow = numAdditionalConditions >= minAdditionalNeeded;

    if (process.env.NODE_ENV === 'development') {
      console.debug(`[VISIBILITY] condition_${conditionNumber}: ${shouldShow ? 'VISIBLE' : 'HIDDEN'} (need ${minAdditionalNeeded}, have ${numAdditionalConditions})`);
    }

    return shouldShow;

  } catch (error) {
    console.error(`[VISIBILITY] Error checking dynamic condition visibility for condition ${conditionNumber}:`, error);
    return false; // Default to hidden for dynamic conditions
  }
}

/**
 * Filters a list of inputs based on visibility rules
 *
 * @param inputs The list of input definitions
 * @param config The current component configuration
 * @returns A filtered list of inputs that should be visible
 */
export function filterVisibleInputs(
  inputs: InputDefinition[],
  config: Record<string, any>,
): InputDefinition[] {
  return inputs.filter((input) => shouldShowInput(input, config));
}
