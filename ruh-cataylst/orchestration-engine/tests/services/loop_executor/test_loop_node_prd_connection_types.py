"""
Test Loop Node Connection Types and Output according to PRD.

This module tests the two distinct output types:
1. Iteration Output - provides current iteration item and context
2. Exit Output - provides aggregated results from all iterations
3. Proper data flow validation
4. Handle type classifications
5. Output routing and data structure validation
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from app.services.loop_executor.loop_executor import LoopExecutor


class TestLoopNodeConnectionTypes:
    """Test loop node connection types and output handling according to PRD."""

    @pytest.fixture
    def loop_executor(self):
        """Create a loop executor instance for testing."""
        mock_state_manager = Mock()
        mock_transition_handler = Mock()
        mock_workflow_utils = Mock()
        mock_transitions_by_id = {}
        
        executor = LoopExecutor(
            state_manager=mock_state_manager,
            transition_handler=mock_transition_handler,
            workflow_utils=mock_workflow_utils,
            transitions_by_id=mock_transitions_by_id,
            user_id="test_user"
        )
        return executor

    # ========================================
    # ITERATION OUTPUT TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_iteration_output_structure(self, loop_executor):
        """Test iteration output contains current_item, iteration_index, and iteration_context."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["apple", "banana", "cherry"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        input_data = {"test": "data"}
        output_routing = {
            "iteration_output": "process_item",
            "exit_output": "final_step"
        }

        # Mock the execute_loop_with_outputs method to capture iteration outputs
        with patch.object(loop_executor, 'execute_loop_with_outputs', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = []
            
            await loop_executor.execute_tool(
                loop_config=loop_config,
                input_data=input_data,
                output_routing=output_routing,
                transition_id="test_transition"
            )

            # Verify the method was called
            mock_execute.assert_called_once()

    def test_iteration_output_data_structure(self, loop_executor):
        """Test that iteration output data has the correct structure."""
        # Set up loop configuration
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2", "item3"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }
        
        parsed_config = loop_executor.parse_loop_config(loop_config)
        loop_executor.current_loop_config = parsed_config
        loop_executor.prepare_iteration_data_from_input()

        # Create iteration output for first item
        iteration_index = 0
        iteration_item = "item1"
        
        iteration_output = loop_executor._create_iteration_output(
            iteration_index, iteration_item
        )

        # Verify structure according to PRD
        assert "current_item" in iteration_output
        assert "iteration_index" in iteration_output
        assert "iteration_context" in iteration_output
        
        assert iteration_output["current_item"] == "item1"
        assert iteration_output["iteration_index"] == 0
        assert isinstance(iteration_output["iteration_context"], dict)

    def test_iteration_output_with_batch_processing(self, loop_executor):
        """Test iteration output structure with batch processing."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3, 4, 5, 6],
                "batch_size": 2
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }
        
        parsed_config = loop_executor.parse_loop_config(loop_config)
        loop_executor.current_loop_config = parsed_config
        loop_executor.prepare_iteration_data_from_input()

        # Create iteration output for first batch
        iteration_index = 0
        iteration_item = [1, 2]  # First batch
        
        iteration_output = loop_executor._create_iteration_output(
            iteration_index, iteration_item
        )

        # Verify batch is properly included
        assert iteration_output["current_item"] == [1, 2]
        assert iteration_output["iteration_index"] == 0
        assert iteration_output["iteration_context"]["is_batch"] is True
        assert iteration_output["iteration_context"]["batch_size"] == 2

    def test_iteration_context_metadata(self, loop_executor):
        """Test iteration context contains proper metadata."""
        loop_config = {
            "iteration_behavior": "sequential",
            "iteration_source": {
                "iteration_list": ["a", "b", "c", "d"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }
        
        parsed_config = loop_executor.parse_loop_config(loop_config)
        loop_executor.current_loop_config = parsed_config
        loop_executor.prepare_iteration_data_from_input()

        # Create iteration output for middle item
        iteration_index = 2
        iteration_item = "c"
        
        iteration_output = loop_executor._create_iteration_output(
            iteration_index, iteration_item
        )

        context = iteration_output["iteration_context"]
        
        # Verify context metadata
        assert context["total_iterations"] == 4
        assert context["is_first"] is False
        assert context["is_last"] is False
        assert context["iteration_behavior"] == "sequential"

    # ========================================
    # EXIT OUTPUT TESTS
    # ========================================

    def test_exit_output_structure(self, loop_executor):
        """Test exit output contains final_results."""
        # Mock aggregated results
        aggregated_results = [
            {"iteration": 0, "result": "processed_apple"},
            {"iteration": 1, "result": "processed_banana"},
            {"iteration": 2, "result": "processed_cherry"}
        ]

        exit_output = loop_executor._create_exit_output(aggregated_results)

        # Verify structure according to PRD
        assert "final_results" in exit_output
        assert exit_output["final_results"] == aggregated_results

    def test_exit_output_with_metadata(self, loop_executor):
        """Test exit output includes metadata when configured."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": [1, 2, 3]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "result_aggregation": {
                "aggregation_type": "collect_all",
                "include_metadata": True
            }
        }
        
        parsed_config = loop_executor.parse_loop_config(loop_config)
        loop_executor.current_loop_config = parsed_config

        aggregated_results = ["result1", "result2", "result3"]
        exit_output = loop_executor._create_exit_output(aggregated_results)

        # Should include metadata when configured
        assert "final_results" in exit_output
        assert "metadata" in exit_output
        
        metadata = exit_output["metadata"]
        assert "total_iterations" in metadata
        assert "successful_iterations" in metadata
        assert "aggregation_type" in metadata

    def test_exit_output_different_aggregation_types(self, loop_executor):
        """Test exit output with different aggregation types."""
        test_cases = [
            ("collect_all", [1, 2, 3], [1, 2, 3]),
            ("collect_successful", [1, None, 3], [1, 3]),
            ("count_only", [1, 2, 3], 3),
            ("latest_only", [1, 2, 3], 3),
            ("first_success", [None, 2, 3], 2),
        ]

        for aggregation_type, input_results, expected_output in test_cases:
            loop_config = {
                "iteration_behavior": "independent",
                "iteration_source": {
                    "iteration_list": [1, 2, 3]
                },
                "exit_condition": {
                    "condition_type": "all_items_processed"
                },
                "result_aggregation": {
                    "aggregation_type": aggregation_type
                }
            }
            
            parsed_config = loop_executor.parse_loop_config(loop_config)
            loop_executor.current_loop_config = parsed_config

            # Mock the aggregation process
            with patch.object(loop_executor, 'aggregate_results', return_value=expected_output):
                exit_output = loop_executor._create_exit_output(input_results)
                
                assert exit_output["final_results"] == expected_output

    # ========================================
    # OUTPUT ROUTING TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_dual_output_routing(self, loop_executor):
        """Test that loop node properly routes to both iteration and exit outputs."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        output_routing = {
            "iteration_output": "process_item_transition",
            "exit_output": "final_aggregation_transition"
        }

        # Mock the output routing methods
        with patch.object(loop_executor, '_send_iteration_output', new_callable=AsyncMock) as mock_iteration:
            with patch.object(loop_executor, '_send_exit_output', new_callable=AsyncMock) as mock_exit:
                with patch.object(loop_executor, 'execute_loop_with_outputs', new_callable=AsyncMock) as mock_execute:
                    mock_execute.return_value = ["result1", "result2"]
                    
                    await loop_executor.execute_tool(
                        loop_config=loop_config,
                        input_data={},
                        output_routing=output_routing,
                        transition_id="test_transition"
                    )

                    # Verify both output types were handled
                    mock_execute.assert_called_once()

    def test_output_routing_validation(self, loop_executor):
        """Test validation of output routing configuration."""
        # Valid routing with both outputs
        valid_routing = {
            "iteration_output": "process_step",
            "exit_output": "final_step"
        }
        
        assert loop_executor._validate_output_routing(valid_routing) is True

        # Invalid routing - missing exit output
        invalid_routing_1 = {
            "iteration_output": "process_step"
        }
        
        assert loop_executor._validate_output_routing(invalid_routing_1) is False

        # Invalid routing - missing iteration output
        invalid_routing_2 = {
            "exit_output": "final_step"
        }
        
        assert loop_executor._validate_output_routing(invalid_routing_2) is False

    # ========================================
    # HANDLE TYPE CLASSIFICATION TESTS
    # ========================================

    def test_handle_type_iteration_output(self, loop_executor):
        """Test handle type classification for iteration output."""
        handle_mapping = {
            "handle_type": "loop_body_entry",
            "result_path": "current_item",
            "to_transition_id": "process_step"
        }

        handle_type = loop_executor._classify_handle_type(handle_mapping)
        assert handle_type == "iteration_output"

    def test_handle_type_exit_output(self, loop_executor):
        """Test handle type classification for exit output."""
        handle_mapping = {
            "handle_type": "loop_exit",
            "result_path": "final_results",
            "to_transition_id": "final_step"
        }

        handle_type = loop_executor._classify_handle_type(handle_mapping)
        assert handle_type == "exit_output"

    def test_handle_type_auto_detection(self, loop_executor):
        """Test auto-detection of handle types based on result_path."""
        # Should detect iteration output from result path
        iteration_mapping = {
            "result_path": "current_item",
            "to_transition_id": "process_step"
        }
        
        handle_type = loop_executor._classify_handle_type(iteration_mapping)
        assert handle_type == "iteration_output"

        # Should detect exit output from result path
        exit_mapping = {
            "result_path": "final_results",
            "to_transition_id": "final_step"
        }
        
        handle_type = loop_executor._classify_handle_type(exit_mapping)
        assert handle_type == "exit_output"

    # ========================================
    # DATA FLOW VALIDATION TESTS
    # ========================================

    def test_iteration_data_flow_validation(self, loop_executor):
        """Test validation of iteration data flow."""
        iteration_data = {
            "current_item": "test_item",
            "iteration_index": 0,
            "iteration_context": {
                "total_iterations": 3,
                "is_first": True,
                "is_last": False
            }
        }

        # Should validate successfully
        assert loop_executor._validate_iteration_data(iteration_data) is True

        # Should fail with missing required fields
        invalid_data = {
            "current_item": "test_item"
            # Missing iteration_index and iteration_context
        }
        
        assert loop_executor._validate_iteration_data(invalid_data) is False

    def test_exit_data_flow_validation(self, loop_executor):
        """Test validation of exit data flow."""
        exit_data = {
            "final_results": ["result1", "result2", "result3"]
        }

        # Should validate successfully
        assert loop_executor._validate_exit_data(exit_data) is True

        # Should fail with missing required fields
        invalid_data = {
            "results": ["result1", "result2"]  # Wrong key name
        }
        
        assert loop_executor._validate_exit_data(invalid_data) is False
