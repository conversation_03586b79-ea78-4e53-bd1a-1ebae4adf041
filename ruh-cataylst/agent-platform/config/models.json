[{"provider": "alibaba", "model": "qwen-max", "geolocation": "global", "input_tokens_price_per_million": "1.6", "caching_tokens_price_per_million": "1.6", "cached_tokens_price_per_million": "1.6", "output_tokens_price_per_million": "6.4", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-02T21:05:42.633169"}, {"provider": "alibaba", "model": "qwen-plus", "geolocation": "global", "input_tokens_price_per_million": "0.4", "caching_tokens_price_per_million": "0.4", "cached_tokens_price_per_million": "0.4", "output_tokens_price_per_million": "1.2", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-02T21:05:42.633169"}, {"provider": "alibaba", "model": "qwen-turbo", "geolocation": "global", "input_tokens_price_per_million": "0.05", "caching_tokens_price_per_million": "0.05", "cached_tokens_price_per_million": "0.05", "output_tokens_price_per_million": "0.2", "max_output_tokens": null, "context_window": 1000000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-02T21:05:42.633169"}, {"provider": "anthropic", "model": "claude-3-5-haiku-20241022", "geolocation": "global", "input_tokens_price_per_million": "0.8", "caching_tokens_price_per_million": "1.0", "cached_tokens_price_per_million": "0.08", "output_tokens_price_per_million": "4.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "Anthrop<PERSON>'s fastest model. Intelligence at blazing speeds.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-30T12:26:59.406474"}, {"provider": "anthropic", "model": "claude-3-5-haiku-latest", "geolocation": "global", "input_tokens_price_per_million": "0.8", "caching_tokens_price_per_million": "1.0", "cached_tokens_price_per_million": "0.08", "output_tokens_price_per_million": "4.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "Anthrop<PERSON>'s fastest model. Intelligence at blazing speeds.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-30T12:26:59.406474"}, {"provider": "anthropic", "model": "claude-3-5-sonnet-20240620", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-30T12:26:59.406474"}, {"provider": "anthropic", "model": "claude-3-5-sonnet-20241022", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-30T12:26:59.406474"}, {"provider": "anthropic", "model": "claude-3-5-sonnet-latest", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-30T12:26:59.406474"}, {"provider": "anthropic", "model": "claude-3-7-sonnet-20250219", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-30T12:26:59.406474"}, {"provider": "anthropic", "model": "claude-3-7-sonnet-latest", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-30T12:26:59.406474"}, {"provider": "anthropic", "model": "claude-3-haiku-20240307", "geolocation": "global", "input_tokens_price_per_million": "0.25", "caching_tokens_price_per_million": "0.3", "cached_tokens_price_per_million": "0.03", "output_tokens_price_per_million": "1.25", "max_output_tokens": 4096, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "Fastest and most compact model for near-instant responsiveness. Quick and accurate targeted performance.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-30T12:26:59.406474"}, {"provider": "anthropic", "model": "claude-3-opus-20240229", "geolocation": "global", "input_tokens_price_per_million": "15.0", "caching_tokens_price_per_million": "18.75", "cached_tokens_price_per_million": "1.5", "output_tokens_price_per_million": "75.0", "max_output_tokens": 4096, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "Powerful model for highly complex tasks. Top-level intelligence, fluency, and understanding.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-30T12:26:59.406474"}, {"provider": "anthropic", "model": "claude-3-opus-latest", "geolocation": "global", "input_tokens_price_per_million": "15.0", "caching_tokens_price_per_million": "18.75", "cached_tokens_price_per_million": "1.5", "output_tokens_price_per_million": "75.0", "max_output_tokens": 4096, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "Powerful model for highly complex tasks. Top-level intelligence, fluency, and understanding.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-30T12:26:59.406474"}, {"provider": "anthropic", "model": "claude-3-sonnet-20240229", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.0", "cached_tokens_price_per_million": "3.0", "output_tokens_price_per_million": "15.0", "max_output_tokens": 4096, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "Balance of intelligence and speed. Strong utility, balanced for scaled deployments", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-30T12:26:59.406474"}, {"provider": "anthropic", "model": "claude-opus-4-20250514", "geolocation": "global", "input_tokens_price_per_million": "15.0", "caching_tokens_price_per_million": "18.75", "cached_tokens_price_per_million": "1.5", "output_tokens_price_per_million": "75.0", "max_output_tokens": 32000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "Claude Opus 4 is Anthropic's most powerful model yet and the best coding model in the world, leading on SWE-bench (72.5%) and Terminal-bench (43.2%). It delivers sustained performance on long-running tasks that require focused effort and thousands of steps, with the ability to work continuously for several hours—dramatically outperforming all Sonnet models and significantly expanding what AI agents can accomplish.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-30T12:26:59.406474"}, {"provider": "anthropic", "model": "claude-sonnet-4-20250514", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Claude Sonnet 4 significantly improves on Sonnet 3.7's industry-leading capabilities, excelling in coding with a state-of-the-art 72.7% on SWE-bench. The model balances performance and efficiency for internal and external use cases, with enhanced steerability for greater control over implementations.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-30T12:26:59.406474"}, {"provider": "cline", "model": "claude-3-5-sonnet", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-28T11:19:06.049396"}, {"provider": "cline", "model": "claude-3-5-sonnet:alpha", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-28T11:19:06.049396"}, {"provider": "cline", "model": "claude-3-5-sonnet:alpha-v2", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-28T11:19:06.049396"}, {"provider": "cline", "model": "claude-3-7-sonnet", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-28T11:19:06.049396"}, {"provider": "cline", "model": "claude-3-7-sonnet:1024", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-28T11:19:06.049396"}, {"provider": "cline", "model": "claude-3-7-sonnet:16384", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-28T11:19:06.049396"}, {"provider": "cline", "model": "claude-3-7-sonnet:64000", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-28T11:19:06.049396"}, {"provider": "cline", "model": "claude-3-7-sonnet:8192", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-28T11:19:06.049396"}, {"provider": "cline", "model": "claude-3-7-sonnet:high", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-28T11:19:06.049396"}, {"provider": "cline", "model": "claude-3-7-sonnet:low", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-28T11:19:06.049396"}, {"provider": "cline", "model": "claude-3-7-sonnet:max", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-28T11:19:06.049396"}, {"provider": "cline", "model": "claude-3-7-sonnet:medium", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-28T11:19:06.049396"}, {"provider": "cline", "model": "deepseek-reasoner:alpha", "geolocation": "global", "input_tokens_price_per_million": "0.55", "caching_tokens_price_per_million": "0.55", "cached_tokens_price_per_million": "0.14", "output_tokens_price_per_million": "2.19", "max_output_tokens": 8000, "context_window": 64000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "Fully open-source model & technical report. Performance on par with OpenAI-o1.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "No online information", "updated_at": "2025-01-20T15:32:51.189563"}, {"provider": "cline", "model": "o3-mini", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "4.4", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "cline", "model": "o3-mini:high", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "4.4", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "cline", "model": "o3-mini:low", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "4.4", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "cline", "model": "o3-mini:medium", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "4.4", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "coding", "model": "claude-3-7-sonnet", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-28T11:19:06.049396"}, {"provider": "coding", "model": "claude-4-sonnet", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Claude Sonnet 4 significantly improves on Sonnet 3.7's industry-leading capabilities, excelling in coding with a state-of-the-art 72.7% on SWE-bench. The model balances performance and efficiency for internal and external use cases, with enhanced steerability for greater control over implementations.", "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-28T11:19:06.049396"}, {"provider": "coding", "model": "gemini-2.5-flash-preview-05-20", "geolocation": "global", "input_tokens_price_per_million": "0.15", "caching_tokens_price_per_million": "0.6", "cached_tokens_price_per_million": "0.15", "output_tokens_price_per_million": "0.6", "max_output_tokens": 65535, "context_window": 1048576, "supports_caching": false, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": true, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "coding", "model": "gemini-2.5-pro-preview-03-25", "geolocation": "global", "input_tokens_price_per_million": "1.25", "caching_tokens_price_per_million": "1.625", "cached_tokens_price_per_million": "0.31", "output_tokens_price_per_million": "10.0", "max_output_tokens": 65535, "context_window": 1048576, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": true, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "coding", "model": "gemini-2.5-pro-preview-05-06", "geolocation": "global", "input_tokens_price_per_million": "1.25", "caching_tokens_price_per_million": "1.625", "cached_tokens_price_per_million": "0.31", "output_tokens_price_per_million": "10.0", "max_output_tokens": 65535, "context_window": 1048576, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": true, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "deepinfra", "model": "deepseek-ai/DeepSeek-R1", "geolocation": "global", "input_tokens_price_per_million": "0.85", "caching_tokens_price_per_million": "0.85", "cached_tokens_price_per_million": "0.85", "output_tokens_price_per_million": "2.5", "max_output_tokens": 8192, "context_window": 64000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "deepinfra", "model": "deepseek-ai/DeepSeek-R1-Distill-Llama-70B", "geolocation": "global", "input_tokens_price_per_million": "0.23", "caching_tokens_price_per_million": "0.23", "cached_tokens_price_per_million": "0.23", "output_tokens_price_per_million": "0.69", "max_output_tokens": 8192, "context_window": 64000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "deepinfra", "model": "deepseek-ai/DeepSeek-V3", "geolocation": "global", "input_tokens_price_per_million": "0.85", "caching_tokens_price_per_million": "0.85", "cached_tokens_price_per_million": "0.85", "output_tokens_price_per_million": "0.9", "max_output_tokens": 8192, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "deepinfra", "model": "meta-llama/Llama-3.2-11B-Vision-Instruct", "geolocation": "global", "input_tokens_price_per_million": "0.055", "caching_tokens_price_per_million": "0.055", "cached_tokens_price_per_million": "0.055", "output_tokens_price_per_million": "0.055", "max_output_tokens": 4096, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepinfra", "model": "meta-llama/Llama-3.2-90B-Vision-Instruct", "geolocation": "global", "input_tokens_price_per_million": "0.35", "caching_tokens_price_per_million": "0.35", "cached_tokens_price_per_million": "0.35", "output_tokens_price_per_million": "0.4", "max_output_tokens": 4096, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepinfra", "model": "meta-llama/Llama-3.3-70B-Instruct", "geolocation": "global", "input_tokens_price_per_million": "0.23", "caching_tokens_price_per_million": "0.23", "cached_tokens_price_per_million": "0.23", "output_tokens_price_per_million": "0.4", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepinfra", "model": "meta-llama/Llama-3.3-70B-Instruct-Turbo", "geolocation": "global", "input_tokens_price_per_million": "0.12", "caching_tokens_price_per_million": "0.12", "cached_tokens_price_per_million": "0.12", "output_tokens_price_per_million": "0.3", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepinfra", "model": "meta-llama/Meta-Llama-3.1-405B-Instruct", "geolocation": "global", "input_tokens_price_per_million": "0.8", "caching_tokens_price_per_million": "0.8", "cached_tokens_price_per_million": "0.8", "output_tokens_price_per_million": "0.8", "max_output_tokens": null, "context_window": 130815, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepinfra", "model": "meta-llama/Meta-Llama-3.1-70B-Instruct", "geolocation": "global", "input_tokens_price_per_million": "0.23", "caching_tokens_price_per_million": "0.23", "cached_tokens_price_per_million": "0.23", "output_tokens_price_per_million": "0.4", "max_output_tokens": null, "context_window": 130815, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepinfra", "model": "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo", "geolocation": "global", "input_tokens_price_per_million": "0.02", "caching_tokens_price_per_million": "0.02", "cached_tokens_price_per_million": "0.02", "output_tokens_price_per_million": "0.05", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepinfra", "model": "microsoft/phi-4", "geolocation": "global", "input_tokens_price_per_million": "0.07", "caching_tokens_price_per_million": "0.07", "cached_tokens_price_per_million": "0.07", "output_tokens_price_per_million": "0.14", "max_output_tokens": null, "context_window": 16384, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepinfra", "model": "microsoft/WizardLM-2-8x22B", "geolocation": "global", "input_tokens_price_per_million": "0.5", "caching_tokens_price_per_million": "0.5", "cached_tokens_price_per_million": "0.5", "output_tokens_price_per_million": "0.5", "max_output_tokens": 4096, "context_window": 65536, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepinfra", "model": "nvidia/Llama-3.1-Nemotron-70B-Instruct", "geolocation": "global", "input_tokens_price_per_million": "0.12", "caching_tokens_price_per_million": "0.12", "cached_tokens_price_per_million": "0.12", "output_tokens_price_per_million": "0.3", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepinfra", "model": "Qwen/Qwen2.5-72B-Instruct", "geolocation": "global", "input_tokens_price_per_million": "0.23", "caching_tokens_price_per_million": "0.23", "cached_tokens_price_per_million": "0.23", "output_tokens_price_per_million": "0.4", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepinfra", "model": "Qwen/Qwen2.5-Coder-32B-Instruct", "geolocation": "global", "input_tokens_price_per_million": "0.07", "caching_tokens_price_per_million": "0.07", "cached_tokens_price_per_million": "0.07", "output_tokens_price_per_million": "0.16", "max_output_tokens": null, "context_window": 16384, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepinfra", "model": "Qwen/Qwen3-235B-A22B", "geolocation": "global", "input_tokens_price_per_million": "0.2", "caching_tokens_price_per_million": "0.2", "cached_tokens_price_per_million": "0.2", "output_tokens_price_per_million": "0.6", "max_output_tokens": 4096, "context_window": 40000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepinfra", "model": "Qwen/Qwen3-32B", "geolocation": "global", "input_tokens_price_per_million": "0.1", "caching_tokens_price_per_million": "0.1", "cached_tokens_price_per_million": "0.1", "output_tokens_price_per_million": "0.3", "max_output_tokens": null, "context_window": 40000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepinfra", "model": "Qwen/QwQ-32B", "geolocation": "global", "input_tokens_price_per_million": "0.12", "caching_tokens_price_per_million": "0.12", "cached_tokens_price_per_million": "0.12", "output_tokens_price_per_million": "0.18", "max_output_tokens": null, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepseek", "model": "deepseek-chat", "geolocation": "global", "input_tokens_price_per_million": "0.27", "caching_tokens_price_per_million": "0.27", "cached_tokens_price_per_million": "0.07", "output_tokens_price_per_million": "1.1", "max_output_tokens": 8000, "context_window": 64000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "DeepSeek-V3 achieves a significant breakthrough in inference speed over previous models.\n\nIt tops the leaderboard among open-source models and rivals the most advanced closed-source models globally.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "No online information", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "deepseek", "model": "deepseek-reasoner", "geolocation": "global", "input_tokens_price_per_million": "0.55", "caching_tokens_price_per_million": "0.55", "cached_tokens_price_per_million": "0.14", "output_tokens_price_per_million": "2.19", "max_output_tokens": 8000, "context_window": 64000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "Fully open-source model & technical report. Performance on par with OpenAI-o1.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "No online information", "updated_at": "2025-01-20T15:32:51.189563"}, {"provider": "google", "model": "gemini-1.5-flash", "geolocation": "global", "input_tokens_price_per_million": "0.075", "caching_tokens_price_per_million": "0.075", "cached_tokens_price_per_million": "0.075", "output_tokens_price_per_million": "0.3", "max_output_tokens": 8192, "context_window": 1048576, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "google", "model": "gemini-1.5-flash-8b", "geolocation": "global", "input_tokens_price_per_million": "0.075", "caching_tokens_price_per_million": "0.075", "cached_tokens_price_per_million": "0.075", "output_tokens_price_per_million": "0.3", "max_output_tokens": 8192, "context_window": 1048576, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "google", "model": "gemini-1.5-flash-8b-latest", "geolocation": "global", "input_tokens_price_per_million": "0.0375", "caching_tokens_price_per_million": "0.0375", "cached_tokens_price_per_million": "0.0375", "output_tokens_price_per_million": "0.15", "max_output_tokens": 8192, "context_window": 1048576, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-11T20:43:40.650774"}, {"provider": "google", "model": "gemini-1.5-flash-latest", "geolocation": "global", "input_tokens_price_per_million": "0.075", "caching_tokens_price_per_million": "0.075", "cached_tokens_price_per_million": "0.075", "output_tokens_price_per_million": "0.3", "max_output_tokens": 8192, "context_window": 1048576, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-11T20:43:40.650774"}, {"provider": "google", "model": "gemini-1.5-pro", "geolocation": "global", "input_tokens_price_per_million": "1.25", "caching_tokens_price_per_million": "1.25", "cached_tokens_price_per_million": "1.25", "output_tokens_price_per_million": "5.0", "max_output_tokens": 8192, "context_window": 2097152, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "google", "model": "gemini-1.5-pro-latest", "geolocation": "global", "input_tokens_price_per_million": "1.25", "caching_tokens_price_per_million": "1.25", "cached_tokens_price_per_million": "1.25", "output_tokens_price_per_million": "5.0", "max_output_tokens": 8192, "context_window": 2097152, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-11T20:43:40.650774"}, {"provider": "google", "model": "gemini-2.0-flash-001", "geolocation": "global", "input_tokens_price_per_million": "0.1", "caching_tokens_price_per_million": "0.1", "cached_tokens_price_per_million": "0.1", "output_tokens_price_per_million": "0.4", "max_output_tokens": 8192, "context_window": 1048576, "supports_caching": false, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-05T18:03:44.801708"}, {"provider": "google", "model": "gemini-2.5-flash-preview-04-17", "geolocation": "global", "input_tokens_price_per_million": "0.15", "caching_tokens_price_per_million": "0.6", "cached_tokens_price_per_million": "0.15", "output_tokens_price_per_million": "0.6", "max_output_tokens": 65535, "context_window": 1048576, "supports_caching": false, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "google", "model": "gemini-2.5-flash-preview-05-20", "geolocation": "global", "input_tokens_price_per_million": "0.15", "caching_tokens_price_per_million": "0.6", "cached_tokens_price_per_million": "0.15", "output_tokens_price_per_million": "0.6", "max_output_tokens": 65535, "context_window": 1048576, "supports_caching": false, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "google", "model": "gemini-2.5-pro-preview-03-25", "geolocation": "global", "input_tokens_price_per_million": "1.25", "caching_tokens_price_per_million": "1.625", "cached_tokens_price_per_million": "0.31", "output_tokens_price_per_million": "10.0", "max_output_tokens": 65535, "context_window": 1048576, "supports_caching": false, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "google", "model": "gemini-2.5-pro-preview-05-06", "geolocation": "global", "input_tokens_price_per_million": "1.25", "caching_tokens_price_per_million": "1.625", "cached_tokens_price_per_million": "0.31", "output_tokens_price_per_million": "10.0", "max_output_tokens": 65535, "context_window": 1048576, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "google", "model": "gemini-2.5-pro-preview-06-05", "geolocation": "global", "input_tokens_price_per_million": "1.25", "caching_tokens_price_per_million": "1.625", "cached_tokens_price_per_million": "0.31", "output_tokens_price_per_million": "10.0", "max_output_tokens": 65535, "context_window": 1048576, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "groq", "model": "qwen-qwq-32b", "geolocation": "global", "input_tokens_price_per_million": "0.29", "caching_tokens_price_per_million": "0.29", "cached_tokens_price_per_million": "0.29", "output_tokens_price_per_million": "0.39", "max_output_tokens": 131072, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Qwen/QwQ-32B is a breakthrough 32-billion parameter reasoning model delivering performance comparable to state-of-the-art (SOTA) models 20x larger like DeepSeek-R1 (671B parameters) on complex reasoning and coding tasks. Deployed on Groq's hardware, it provides the world's fastest and cost-efficient reasoning, producing chains and results in seconds. Along with native tool use support, the 128K context window enables processing extensive information while maintaining comprehensive context.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-03-07T11:33:47.830192"}, {"provider": "minimaxi", "model": "DeepSeek-R1", "geolocation": "global", "input_tokens_price_per_million": "0.55", "caching_tokens_price_per_million": "2.19", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "2.19", "max_output_tokens": null, "context_window": 640000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "No online information", "updated_at": "2025-01-15T20:36:08.145736"}, {"provider": "minimaxi", "model": "MiniMax-Text-01", "geolocation": "global", "input_tokens_price_per_million": "0.2", "caching_tokens_price_per_million": "0.2", "cached_tokens_price_per_million": "0.2", "output_tokens_price_per_million": "1.1", "max_output_tokens": null, "context_window": 1000192, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "No online information", "updated_at": "2025-01-15T20:36:08.145736"}, {"provider": "mistral", "model": "devstral-small-latest", "geolocation": "eu", "input_tokens_price_per_million": "0.1", "caching_tokens_price_per_million": "0.1", "cached_tokens_price_per_million": "0.1", "output_tokens_price_per_million": "0.3", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "mistral", "model": "mistral-large-latest", "geolocation": "eu", "input_tokens_price_per_million": "2.0", "caching_tokens_price_per_million": "2.0", "cached_tokens_price_per_million": "2.0", "output_tokens_price_per_million": "6.0", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "mistral", "model": "mistral-small-latest", "geolocation": "eu", "input_tokens_price_per_million": "0.1", "caching_tokens_price_per_million": "0.1", "cached_tokens_price_per_million": "0.1", "output_tokens_price_per_million": "0.3", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "mistral", "model": "open-mistral-7b", "geolocation": "eu", "input_tokens_price_per_million": "0.25", "caching_tokens_price_per_million": "0.25", "cached_tokens_price_per_million": "0.25", "output_tokens_price_per_million": "0.25", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "nebius", "model": "deepseek-ai/DeepSeek-R1", "geolocation": "global", "input_tokens_price_per_million": "0.8", "caching_tokens_price_per_million": "0.8", "cached_tokens_price_per_million": "0.8", "output_tokens_price_per_million": "2.4", "max_output_tokens": null, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "nebius", "model": "deepseek-ai/DeepSeek-R1-0528", "geolocation": "global", "input_tokens_price_per_million": "0.8", "caching_tokens_price_per_million": "0.8", "cached_tokens_price_per_million": "0.8", "output_tokens_price_per_million": "2.4", "max_output_tokens": null, "context_window": 164000, "supports_caching": false, "supports_vision": false, "supports_computer_use": true, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "nebius", "model": "deepseek-ai/DeepSeek-R1-fast", "geolocation": "global", "input_tokens_price_per_million": "2.0", "caching_tokens_price_per_million": "2.0", "cached_tokens_price_per_million": "2.0", "output_tokens_price_per_million": "6.0", "max_output_tokens": null, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "nebius", "model": "deepseek-ai/DeepSeek-V3", "geolocation": "global", "input_tokens_price_per_million": "0.5", "caching_tokens_price_per_million": "0.5", "cached_tokens_price_per_million": "0.5", "output_tokens_price_per_million": "1.5", "max_output_tokens": null, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "nebius", "model": "deepseek-ai/DeepSeek-V3-0324", "geolocation": "global", "input_tokens_price_per_million": "0.5", "caching_tokens_price_per_million": "0.5", "cached_tokens_price_per_million": "0.5", "output_tokens_price_per_million": "1.5", "max_output_tokens": null, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "nebius", "model": "deepseek-ai/DeepSeek-V3-0324-fast", "geolocation": "global", "input_tokens_price_per_million": "2.0", "caching_tokens_price_per_million": "6.0", "cached_tokens_price_per_million": "2.0", "output_tokens_price_per_million": "6.0", "max_output_tokens": null, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "nebius", "model": "meta-llama/Llama-3.3-70B-Instruct", "geolocation": "global", "input_tokens_price_per_million": "0.13", "caching_tokens_price_per_million": "0.13", "cached_tokens_price_per_million": "0.13", "output_tokens_price_per_million": "0.4", "max_output_tokens": null, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "nebius", "model": "Qwen/QwQ-32B", "geolocation": "global", "input_tokens_price_per_million": "0.15", "caching_tokens_price_per_million": "0.15", "cached_tokens_price_per_million": "0.15", "output_tokens_price_per_million": "0.45", "max_output_tokens": null, "context_window": 32000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "nebius", "model": "Qwen/QwQ-32B-fast", "geolocation": "global", "input_tokens_price_per_million": "0.5", "caching_tokens_price_per_million": "0.5", "cached_tokens_price_per_million": "0.5", "output_tokens_price_per_million": "1.5", "max_output_tokens": null, "context_window": 32000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "netmind", "model": "deepseek-ai/DeepSeek-R1-0528", "geolocation": "global", "input_tokens_price_per_million": "0.5", "caching_tokens_price_per_million": "0.5", "cached_tokens_price_per_million": "0.5", "output_tokens_price_per_million": "1.0", "max_output_tokens": null, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": true, "description": "DeepSeek R1 is the latest open-source model released by the DeepSeek team, featuring impressive reasoning capabilities, particularly achieving performance comparable to OpenAI's o1 model in mathematics, coding, and reasoning tasks.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2025-01-26T07:29:18"}, {"provider": "novita", "model": "deepseek/deepseek-prover-v2-671b", "geolocation": "global", "input_tokens_price_per_million": "0.7", "caching_tokens_price_per_million": "0.7", "cached_tokens_price_per_million": "0.7", "output_tokens_price_per_million": "2.5", "max_output_tokens": null, "context_window": 160000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2025-04-30T13:44:31"}, {"provider": "novita", "model": "deepseek/deepseek-r1", "geolocation": "global", "input_tokens_price_per_million": "4.0", "caching_tokens_price_per_million": "4.0", "cached_tokens_price_per_million": "4.0", "output_tokens_price_per_million": "4.0", "max_output_tokens": null, "context_window": 64000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "DeepSeek R1 is the latest open-source model released by the DeepSeek team, featuring impressive reasoning capabilities, particularly achieving performance comparable to OpenAI's o1 model in mathematics, coding, and reasoning tasks.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2025-01-26T07:29:18"}, {"provider": "novita", "model": "deepseek/deepseek-r1-distill-llama-70b", "geolocation": "global", "input_tokens_price_per_million": "0.8", "caching_tokens_price_per_million": "0.8", "cached_tokens_price_per_million": "0.8", "output_tokens_price_per_million": "0.8", "max_output_tokens": null, "context_window": 32000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "DeepSeek R1 Distill LLama 70B", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2025-01-27T05:53:10"}, {"provider": "novita", "model": "deepseek/deepseek-r1-distill-qwen-14b", "geolocation": "global", "input_tokens_price_per_million": "0.15", "caching_tokens_price_per_million": "0.15", "cached_tokens_price_per_million": "0.15", "output_tokens_price_per_million": "0.15", "max_output_tokens": null, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "DeepSeek R1 Distill Qwen 14B is a distilled large language model based on Qwen 2.5 14B, using outputs from DeepSeek R1. It outperforms OpenAI's o1-mini across various benchmarks, achieving new state-of-the-art results for dense models.\n\nOther benchmark results include:\n\nAIME 2024 pass@1: 69.7\nMATH-500 pass@1: 93.9\nCodeForces Rating: 1481\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2025-02-02T12:12:51"}, {"provider": "novita", "model": "deepseek/deepseek-r1-distill-qwen-32b", "geolocation": "global", "input_tokens_price_per_million": "0.3", "caching_tokens_price_per_million": "0.3", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "0.3", "max_output_tokens": null, "context_window": 12800, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "DeepSeek R1 Distill Qwen 32B is a distilled large language model based on Qwen 2.5 32B, using outputs from DeepSeek R1. It outperforms OpenAI's o1-mini across various benchmarks, achieving new state-of-the-art results for dense models.\n\nOther benchmark results include:\nAIME 2024 pass@1: 72.6\nMATH-500 pass@1: 94.3\nCodeForces Rating: 1691\nThe model leverages fine-tuning from DeepSeek R1's outputs, enabling competitive performance comparable to larger frontier models.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2025-02-02T12:11:33"}, {"provider": "novita", "model": "deepseek/deepseek-r1-turbo", "geolocation": "global", "input_tokens_price_per_million": "0.7", "caching_tokens_price_per_million": "2.5", "cached_tokens_price_per_million": "0.7", "output_tokens_price_per_million": "2.5", "max_output_tokens": null, "context_window": 64000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "DeepSeek R1 is the latest open-source model released by the DeepSeek team, featuring impressive reasoning capabilities, particularly achieving performance comparable to OpenAI's o1 model in mathematics, coding, and reasoning tasks.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2025-01-26T07:29:18"}, {"provider": "novita", "model": "deepseek/deepseek_v3", "geolocation": "global", "input_tokens_price_per_million": "0.89", "caching_tokens_price_per_million": "0.89", "cached_tokens_price_per_million": "0.89", "output_tokens_price_per_million": "0.89", "max_output_tokens": null, "context_window": 64000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "DeepSeek-V3 is the latest model from the DeepSeek team, building upon the instruction following and coding abilities of the previous versions. Pre-trained on nearly 15 trillion tokens, the reported evaluations reveal that the model outperforms other open-source models and rivals leading closed-source models.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2025-01-15T11:54:32"}, {"provider": "novita", "model": "deepseek/deepseek-v3-0324", "geolocation": "global", "input_tokens_price_per_million": "0.4", "caching_tokens_price_per_million": "0.4", "cached_tokens_price_per_million": "0.4", "output_tokens_price_per_million": "1.3", "max_output_tokens": null, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "DeepSeek R1 is the latest open-source model released by the DeepSeek team, featuring impressive reasoning capabilities, particularly achieving performance comparable to OpenAI's o1 model in mathematics, coding, and reasoning tasks.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2025-01-26T07:29:18"}, {"provider": "novita", "model": "deepseek/deepseek-v3-turbo", "geolocation": "global", "input_tokens_price_per_million": "0.4", "caching_tokens_price_per_million": "0.4", "cached_tokens_price_per_million": "0.4", "output_tokens_price_per_million": "1.3", "max_output_tokens": null, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "DeepSeek R1 is the latest open-source model released by the DeepSeek team, featuring impressive reasoning capabilities, particularly achieving performance comparable to OpenAI's o1 model in mathematics, coding, and reasoning tasks.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2025-01-26T07:29:18"}, {"provider": "novita", "model": "google/gemma-2-9b-it", "geolocation": "global", "input_tokens_price_per_million": "0.08", "caching_tokens_price_per_million": "0.08", "cached_tokens_price_per_million": "0.08", "output_tokens_price_per_million": "0.08", "max_output_tokens": null, "context_window": 8192, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Gemma 2 9B by Google is an advanced, open-source language model that sets a new standard for efficiency and performance in its size class.\nDesigned for a wide variety of tasks, it empowers developers and researchers to build innovative applications, while maintaining accessibility, safety, and cost-effectiveness.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-07-18T15:03:44"}, {"provider": "novita", "model": "gryphe/mythomax-l2-13b", "geolocation": "global", "input_tokens_price_per_million": "0.09", "caching_tokens_price_per_million": "0.09", "cached_tokens_price_per_million": "0.09", "output_tokens_price_per_million": "0.09", "max_output_tokens": null, "context_window": 4096, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "The idea behind this merge is that each layer is composed of several tensors, which are in turn responsible for specific functions. Using MythoLogic-L2's robust understanding as its input and Huginn's extensive writing capability as its output seems to have resulted in a model that exceeds at both, confirming my theory. (More details to be released at a later time).", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-04-25T07:01:13"}, {"provider": "novita", "model": "jondurbin/airoboros-l2-70b", "geolocation": "global", "input_tokens_price_per_million": "0.5", "caching_tokens_price_per_million": "0.5", "cached_tokens_price_per_million": "0.5", "output_tokens_price_per_million": "0.5", "max_output_tokens": null, "context_window": 4096, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "This is a fine-tuned Llama-2 model designed to support longer and more detailed writing prompts, as well as next-chapter generation. It also includes an experimental role-playing instruction set with multi-round dialogues, character interactions, and varying numbers of participants", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-07-17T12:27:57"}, {"provider": "novita", "model": "meta-llama/llama-3.1-70b-instruct", "geolocation": "global", "input_tokens_price_per_million": "0.34", "caching_tokens_price_per_million": "0.34", "cached_tokens_price_per_million": "0.34", "output_tokens_price_per_million": "0.39", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Meta's latest class of models, Llama 3.1, has launched with a variety of sizes and configurations. The 70B instruct-tuned version is optimized for high-quality dialogue use cases. It has demonstrated strong performance in human evaluations compared to leading closed-source models.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-07-24T07:04:24"}, {"provider": "novita", "model": "meta-llama/llama-3.1-8b-instruct", "geolocation": "global", "input_tokens_price_per_million": "0.05", "caching_tokens_price_per_million": "0.05", "cached_tokens_price_per_million": "0.05", "output_tokens_price_per_million": "0.05", "max_output_tokens": null, "context_window": 16384, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Meta's latest class of models, Llama 3.1, launched with a variety of sizes and configurations. The 8B instruct-tuned version is particularly fast and efficient. It has demonstrated strong performance in human evaluations, outperforming several leading closed-source models.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-07-24T07:17:47"}, {"provider": "novita", "model": "meta-llama/llama-3.1-8b-instruct-bf16", "geolocation": "global", "input_tokens_price_per_million": "0.06", "caching_tokens_price_per_million": "0.06", "cached_tokens_price_per_million": "0.06", "output_tokens_price_per_million": "0.06", "max_output_tokens": null, "context_window": 8192, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Meta's latest class of models, Llama 3.1, launched with a variety of sizes and configurations. The 8B instruct-tuned version is particularly fast and efficient. It has demonstrated strong performance in human evaluations, \n                   outperforming several leading closed-source models.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-10-21T09:57:35"}, {"provider": "novita", "model": "meta-llama/llama-3.1-8b-instruct-max", "geolocation": "global", "input_tokens_price_per_million": "0.05", "caching_tokens_price_per_million": "0.05", "cached_tokens_price_per_million": "0.05", "output_tokens_price_per_million": "0.05", "max_output_tokens": null, "context_window": 16384, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Meta's latest class of models, Llama 3.1, launched with a variety of sizes and configurations. The 8B instruct-tuned version is particularly fast and efficient. It has demonstrated strong performance in human evaluations, outperforming several leading closed-source models.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-12-03T08:44:57"}, {"provider": "novita", "model": "meta-llama/llama-3.2-11b-vision-instruct", "geolocation": "global", "input_tokens_price_per_million": "0.06", "caching_tokens_price_per_million": "0.06", "cached_tokens_price_per_million": "0.06", "output_tokens_price_per_million": "0.06", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Llama 3.2 11B Vision is a multimodal model with 11 billion parameters, designed to handle tasks combining visual and textual data. It excels in tasks such as image captioning and visual question answering, bridging the gap between language generation and visual reasoning. Pre-trained on a massive dataset of image-text pairs, it performs well in complex, high-accuracy image analysis. Its ability to integrate visual understanding with language processing makes it an ideal solution for industries requiring comprehensive visual-linguistic AI applications, such as content creation, AI-driven customer service, and research.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-11-26T08:23:40"}, {"provider": "novita", "model": "meta-llama/llama-3.2-1b-instruct", "geolocation": "global", "input_tokens_price_per_million": "0.02", "caching_tokens_price_per_million": "0.02", "cached_tokens_price_per_million": "0.02", "output_tokens_price_per_million": "0.02", "max_output_tokens": null, "context_window": 131000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "The Meta Llama 3.2 collection of multilingual large language models (LLMs) is a collection of pretrained and instruction-tuned generative models in 1B and 3B sizes (text in/text out).", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-11-26T08:25:40"}, {"provider": "novita", "model": "meta-llama/llama-3.2-3b-instruct", "geolocation": "global", "input_tokens_price_per_million": "0.03", "caching_tokens_price_per_million": "0.03", "cached_tokens_price_per_million": "0.03", "output_tokens_price_per_million": "0.05", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "The Meta Llama 3.2 collection of multilingual large language models (LLMs) is a collection of pretrained and instruction-tuned generative models in 1B and 3B sizes (text in/text out)", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-11-26T07:55:48"}, {"provider": "novita", "model": "meta-llama/llama-3.3-70b-instruct", "geolocation": "global", "input_tokens_price_per_million": "0.39", "caching_tokens_price_per_million": "0.39", "cached_tokens_price_per_million": "0.39", "output_tokens_price_per_million": "0.39", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "The Meta Llama 3.3 multilingual large language model (LLM) is a pretrained and instruction tuned generative model in 70B (text in/text out). The Llama 3.3 instruction tuned text only model is optimized for multilingual dialogue use cases and outperforms many of the available open source and closed chat models on common industry benchmarks.\n\nSupported languages: English, German, French, Italian, Portuguese, Hindi, Spanish, and Thai.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-12-07T08:28:29"}, {"provider": "novita", "model": "meta-llama/llama-3-70b-instruct", "geolocation": "global", "input_tokens_price_per_million": "0.51", "caching_tokens_price_per_million": "0.51", "cached_tokens_price_per_million": "0.51", "output_tokens_price_per_million": "0.74", "max_output_tokens": null, "context_window": 8192, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Meta's latest class of model (Llama 3) launched with a variety of sizes & flavors. This 70B instruct-tuned version was optimized for high quality dialogue usecases. It has demonstrated strong performance compared to leading closed-source models in human evaluations.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-04-25T07:00:15"}, {"provider": "novita", "model": "meta-llama/llama-3-8b-instruct", "geolocation": "global", "input_tokens_price_per_million": "0.04", "caching_tokens_price_per_million": "0.04", "cached_tokens_price_per_million": "0.04", "output_tokens_price_per_million": "0.04", "max_output_tokens": null, "context_window": 8192, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Meta's latest class of model (Llama 3) launched with a variety of sizes & flavors. This 8B instruct-tuned version was optimized for high quality dialogue usecases. It has demonstrated strong performance compared to leading closed-source models in human evaluations.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-04-25T07:01:14"}, {"provider": "novita", "model": "meta-llama/llama-4-maverick-17b-128e-instruct-fp8", "geolocation": "global", "input_tokens_price_per_million": "0.2", "caching_tokens_price_per_million": "0.2", "cached_tokens_price_per_million": "0.2", "output_tokens_price_per_million": "0.85", "max_output_tokens": 1048576, "context_window": 1048576, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "novita", "model": "microsoft/wizardlm-2-8x22b", "geolocation": "global", "input_tokens_price_per_million": "0.62", "caching_tokens_price_per_million": "0.62", "cached_tokens_price_per_million": "0.62", "output_tokens_price_per_million": "0.62", "max_output_tokens": null, "context_window": 65535, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "WizardLM-2 8x22B is Microsoft AI's most advanced Wizard model. It demonstrates highly competitive performance compared to leading proprietary models, and it consistently outperforms all existing state-of-the-art opensource models.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-04-24T07:01:12"}, {"provider": "novita", "model": "mistral<PERSON>/mistral-7b-instruct", "geolocation": "global", "input_tokens_price_per_million": "0.059", "caching_tokens_price_per_million": "0.059", "cached_tokens_price_per_million": "0.059", "output_tokens_price_per_million": "0.059", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "A high-performing, industry-standard 7.3B parameter model, with optimizations for speed and context length.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-06-27T13:55:13"}, {"provider": "novita", "model": "mistralai/mistral-nemo", "geolocation": "global", "input_tokens_price_per_million": "0.17", "caching_tokens_price_per_million": "0.17", "cached_tokens_price_per_million": "0.17", "output_tokens_price_per_million": "0.17", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "A 12B parameter model with a 128k token context length built by Mistral in collaboration with NVIDIA. The model is multilingual, supporting English, French, German, Spanish, Italian, Portuguese, Chinese, Japanese, Korean, Arabic, and Hindi. It supports function calling and is released under the Apache 2.0 license.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-07-30T12:10:58"}, {"provider": "novita", "model": "nousresearch/hermes-2-pro-llama-3-8b", "geolocation": "global", "input_tokens_price_per_million": "0.14", "caching_tokens_price_per_million": "0.14", "cached_tokens_price_per_million": "0.14", "output_tokens_price_per_million": "0.14", "max_output_tokens": null, "context_window": 8192, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Hermes 2 Pro is an upgraded, retrained version of Nous Hermes 2, consisting of an updated and cleaned version of the OpenHermes 2.5 Dataset, as well as a newly introduced Function Calling and JSON Mode dataset developed in-house.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-06-27T13:56:52"}, {"provider": "novita", "model": "nousresearch/nous-hermes-llama2-13b", "geolocation": "global", "input_tokens_price_per_million": "0.17", "caching_tokens_price_per_million": "0.17", "cached_tokens_price_per_million": "0.17", "output_tokens_price_per_million": "0.17", "max_output_tokens": null, "context_window": 4096, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Nous-Hermes-Llama2-13b is a state-of-the-art language model fine-tuned on over 300,000 instructions. This model was fine-tuned by Nous Research, with Teknium and Emozilla leading the fine tuning process and dataset curation, Redmond AI sponsoring the compute, and several other contributors.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-04-25T07:01:13"}, {"provider": "novita", "model": "openchat/openchat-7b", "geolocation": "global", "input_tokens_price_per_million": "0.06", "caching_tokens_price_per_million": "0.06", "cached_tokens_price_per_million": "0.06", "output_tokens_price_per_million": "0.06", "max_output_tokens": null, "context_window": 4096, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "OpenChat 7B is a library of open-source language models, fine-tuned with \"C-RLFT (Conditioned Reinforcement Learning Fine-Tuning)\" - a strategy inspired by offline reinforcement learning. It has been trained on mixed-quality data without preference labels.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-07-26T12:09:32"}, {"provider": "novita", "model": "qwen/qwen-2.5-72b-instruct", "geolocation": "global", "input_tokens_price_per_million": "0.38", "caching_tokens_price_per_million": "0.38", "cached_tokens_price_per_million": "0.38", "output_tokens_price_per_million": "0.4", "max_output_tokens": null, "context_window": 32000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Qwen2.5 is the latest series of Qwen large language models. For Qwen2.5, we release a number of base language models and instruction-tuned language models ranging from 0.5 to 72 billion parameters.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-10-15T04:17:38"}, {"provider": "novita", "model": "qwen/qwen2.5-vl-72b-instruct", "geolocation": "global", "input_tokens_price_per_million": "0.8", "caching_tokens_price_per_million": "0.8", "cached_tokens_price_per_million": "0.8", "output_tokens_price_per_million": "0.8", "max_output_tokens": null, "context_window": 96000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Qwen2 VL 72B is a multimodal LLM from the Qwen Team with the following key enhancements:\n\nSoTA understanding of images of various resolution & ratio: Qwen2-VL achieves state-of-the-art performance on visual understanding benchmarks, including MathVista, DocVQA, RealWorldQA, MTVQA, etc.\n\nUnderstanding videos of 20min+: Qwen2-<PERSON><PERSON> can understand videos over 20 minutes for high-quality video-based question answering, dialog, content creation, etc.\n\nAgent that can operate your mobiles, robots, etc.: with the abilities of complex reasoning and decision making, Qwen2-VL can be integrated with devices like mobile phones, robots, etc., for automatic operation based on visual environment and text instructions.\n\nMultilingual Support: to serve global users, besides English and Chinese, Qwen2-VL now supports the understanding of texts in different languages inside images, including most European languages, Japanese, Korean, Arabic, Vietnamese, etc.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-12-19T09:22:00"}, {"provider": "novita", "model": "qwen/qwen-2-7b-instruct", "geolocation": "global", "input_tokens_price_per_million": "0.054", "caching_tokens_price_per_million": "0.054", "cached_tokens_price_per_million": "0.054", "output_tokens_price_per_million": "0.054", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Qwen2 is the newest series in the Qwen large language model family. Qwen2 7B is a transformer-based model that demonstrates exceptional performance in language understanding, multilingual capabilities, programming, mathematics, and reasoning.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-09-18T13:08:27"}, {"provider": "novita", "model": "qwen/qwen-2-vl-72b-instruct", "geolocation": "global", "input_tokens_price_per_million": "0.45", "caching_tokens_price_per_million": "0.45", "cached_tokens_price_per_million": "0.45", "output_tokens_price_per_million": "0.45", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Qwen2 VL 72B is a multimodal LLM from the Qwen Team with the following key enhancements:\n\nSoTA understanding of images of various resolution & ratio: Qwen2-VL achieves state-of-the-art performance on visual understanding benchmarks, including MathVista, DocVQA, RealWorldQA, MTVQA, etc.\n\nUnderstanding videos of 20min+: Qwen2-<PERSON><PERSON> can understand videos over 20 minutes for high-quality video-based question answering, dialog, content creation, etc.\n\nAgent that can operate your mobiles, robots, etc.: with the abilities of complex reasoning and decision making, Qwen2-VL can be integrated with devices like mobile phones, robots, etc., for automatic operation based on visual environment and text instructions.\n\nMultilingual Support: to serve global users, besides English and Chinese, Qwen2-VL now supports the understanding of texts in different languages inside images, including most European languages, Japanese, Korean, Arabic, Vietnamese, etc.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-12-19T09:22:00"}, {"provider": "novita", "model": "qwen/qwen3-235b-a22b-fp8", "geolocation": "global", "input_tokens_price_per_million": "0.2", "caching_tokens_price_per_million": "0.2", "cached_tokens_price_per_million": "0.2", "output_tokens_price_per_million": "0.8", "max_output_tokens": null, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2025-04-30T13:44:31"}, {"provider": "novita", "model": "qwen/qwq-32b", "geolocation": "global", "input_tokens_price_per_million": "0.18", "caching_tokens_price_per_million": "0.18", "cached_tokens_price_per_million": "0.18", "output_tokens_price_per_million": "0.2", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2025-01-15T11:54:32"}, {"provider": "novita", "model": "sao10k/l31-70b-euryale-v2.2", "geolocation": "global", "input_tokens_price_per_million": "1.48", "caching_tokens_price_per_million": "1.48", "cached_tokens_price_per_million": "1.48", "output_tokens_price_per_million": "1.48", "max_output_tokens": null, "context_window": 16000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Euryale L3.1 70B v2.2 is a model focused on creative roleplay from Sao10k. It is the successor of Euryale L3 70B v2.1.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-09-19T11:35:41"}, {"provider": "novita", "model": "sao10k/l3-70b-euryale-v2.1", "geolocation": "global", "input_tokens_price_per_million": "1.48", "caching_tokens_price_per_million": "1.48", "cached_tokens_price_per_million": "1.48", "output_tokens_price_per_million": "1.48", "max_output_tokens": null, "context_window": 16000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "The uncensored llama3 model is a powerhouse of creativity, excelling in both roleplay and story writing. It offers a liberating experience during roleplays, free from any restrictions. This model stands out for its immense creativity, boasting a vast array of unique ideas and plots, truly a treasure trove for those seeking originality. Its unrestricted nature during roleplays allows for the full breadth of imagination to unfold, akin to an enhanced, big-brained version of Stheno. Perfect for creative minds seeking a boundless platform for their imaginative expressions, the uncensored llama3 model is an ideal choice", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-06-18T09:25:28"}, {"provider": "novita", "model": "sao10k/l3-8b-lunaris", "geolocation": "global", "input_tokens_price_per_million": "0.05", "caching_tokens_price_per_million": "0.05", "cached_tokens_price_per_million": "0.05", "output_tokens_price_per_million": "0.05", "max_output_tokens": null, "context_window": 8192, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "A generalist / roleplaying model merge based on Llama 3.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-11-28T11:01:54"}, {"provider": "novita", "model": "Sao10K/L3-8B-Stheno-v3.2", "geolocation": "global", "input_tokens_price_per_million": "0.05", "caching_tokens_price_per_million": "0.05", "cached_tokens_price_per_million": "0.05", "output_tokens_price_per_million": "0.05", "max_output_tokens": null, "context_window": 8192, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Sao10K/L3-8B-Stheno-v3.2 is a highly skilled actor that excels at fully immersing itself in any role assigned.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-11-29T10:25:17"}, {"provider": "novita", "model": "sophosympatheia/midnight-rose-70b", "geolocation": "global", "input_tokens_price_per_million": "0.8", "caching_tokens_price_per_million": "0.8", "cached_tokens_price_per_million": "0.8", "output_tokens_price_per_million": "0.8", "max_output_tokens": null, "context_window": 4096, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "A merge with a complex family tree, this model was crafted for roleplaying and storytelling. <PERSON> Rose is a successor to Rogue Rose and Aurora Nights and improves upon them both. It wants to produce lengthy output by default and is the best creative writing merge produced so far by sophosympatheia.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-06-17T13:14:12"}, {"provider": "novita", "model": "teknium/openhermes-2.5-mistral-7b", "geolocation": "global", "input_tokens_price_per_million": "0.17", "caching_tokens_price_per_million": "0.17", "cached_tokens_price_per_million": "0.17", "output_tokens_price_per_million": "0.17", "max_output_tokens": null, "context_window": 4096, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "OpenHermes 2.5 Mistral 7B is a state of the art Mistral Fine-tune, a continuation of OpenHermes 2 model, which trained on additional code datasets.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "NaN", "updated_at": "2024-04-24T07:01:13"}, {"provider": "openai", "model": "chatgpt-4o-latest", "geolocation": "global", "input_tokens_price_per_million": "5.0", "caching_tokens_price_per_million": "5.0", "cached_tokens_price_per_million": "5.0", "output_tokens_price_per_million": "15.0", "max_output_tokens": 16000, "context_window": 128000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "openai", "model": "gpt-4.1", "geolocation": "global", "input_tokens_price_per_million": "2.0", "caching_tokens_price_per_million": "2.0", "cached_tokens_price_per_million": "0.5", "output_tokens_price_per_million": "8.0", "max_output_tokens": 32768, "context_window": 1047576, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "openai", "model": "gpt-4.1-2025-04-14", "geolocation": "global", "input_tokens_price_per_million": "2.0", "caching_tokens_price_per_million": "2.0", "cached_tokens_price_per_million": "0.5", "output_tokens_price_per_million": "8.0", "max_output_tokens": 32768, "context_window": 1047576, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "openai", "model": "gpt-4.1-mini", "geolocation": "global", "input_tokens_price_per_million": "0.4", "caching_tokens_price_per_million": "0.4", "cached_tokens_price_per_million": "0.1", "output_tokens_price_per_million": "1.6", "max_output_tokens": 32768, "context_window": 1047576, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "openai", "model": "gpt-4.1-mini-2025-04-14", "geolocation": "global", "input_tokens_price_per_million": "0.4", "caching_tokens_price_per_million": "0.4", "cached_tokens_price_per_million": "0.1", "output_tokens_price_per_million": "1.6", "max_output_tokens": 32768, "context_window": 1047576, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "openai", "model": "gpt-4.1-nano", "geolocation": "global", "input_tokens_price_per_million": "0.1", "caching_tokens_price_per_million": "0.1", "cached_tokens_price_per_million": "0.025", "output_tokens_price_per_million": "0.4", "max_output_tokens": 32768, "context_window": 1047576, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "openai", "model": "gpt-4.1-nano-2025-04-14", "geolocation": "global", "input_tokens_price_per_million": "0.1", "caching_tokens_price_per_million": "0.1", "cached_tokens_price_per_million": "0.025", "output_tokens_price_per_million": "0.4", "max_output_tokens": 32768, "context_window": 1047576, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "openai", "model": "gpt-4.5-preview", "geolocation": "global", "input_tokens_price_per_million": "75.0", "caching_tokens_price_per_million": "75.0", "cached_tokens_price_per_million": "37.5", "output_tokens_price_per_million": "150.0", "max_output_tokens": 4096, "context_window": 128000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-27T20:04:10.847740"}, {"provider": "openai", "model": "gpt-4o", "geolocation": "global", "input_tokens_price_per_million": "2.5", "caching_tokens_price_per_million": "2.5", "cached_tokens_price_per_million": "1.25", "output_tokens_price_per_million": "10.0", "max_output_tokens": 4096, "context_window": 128000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "openai", "model": "gpt-4o-2024-05-13", "geolocation": "global", "input_tokens_price_per_million": "2.5", "caching_tokens_price_per_million": "2.5", "cached_tokens_price_per_million": "2.5", "output_tokens_price_per_million": "10.0", "max_output_tokens": 4096, "context_window": 128000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "openai", "model": "gpt-4o-2024-08-06", "geolocation": "global", "input_tokens_price_per_million": "2.5", "caching_tokens_price_per_million": "2.5", "cached_tokens_price_per_million": "1.25", "output_tokens_price_per_million": "10.0", "max_output_tokens": 4096, "context_window": 128000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "openai", "model": "gpt-4o-2024-11-20", "geolocation": "global", "input_tokens_price_per_million": "2.5", "caching_tokens_price_per_million": "2.5", "cached_tokens_price_per_million": "1.25", "output_tokens_price_per_million": "10.0", "max_output_tokens": 4096, "context_window": 128000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "openai", "model": "gpt-4o-mini", "geolocation": "global", "input_tokens_price_per_million": "0.15", "caching_tokens_price_per_million": "0.15", "cached_tokens_price_per_million": "0.075", "output_tokens_price_per_million": "0.6", "max_output_tokens": 4096, "context_window": 128000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "openai", "model": "gpt-4o-mini-2024-07-18", "geolocation": "global", "input_tokens_price_per_million": "0.15", "caching_tokens_price_per_million": "0.15", "cached_tokens_price_per_million": "0.075", "output_tokens_price_per_million": "0.6", "max_output_tokens": 4096, "context_window": 128000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "openai", "model": "o1", "geolocation": "global", "input_tokens_price_per_million": "15.0", "caching_tokens_price_per_million": "15.0", "cached_tokens_price_per_million": "7.5", "output_tokens_price_per_million": "60.0", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "The o1 series of models are trained with reinforcement learning to perform complex reasoning. o1 models think before they answer, producing a long internal chain of thought before responding to the user. The o1 reasoning model is designed to solve hard problems across domains. The knowledge cutoff for o1 and o1-mini models is October, 2023.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "openai", "model": "o1-2024-12-17", "geolocation": "global", "input_tokens_price_per_million": "15.0", "caching_tokens_price_per_million": "15.0", "cached_tokens_price_per_million": "7.5", "output_tokens_price_per_million": "60.0", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "The o1 series of models are trained with reinforcement learning to perform complex reasoning. o1 models think before they answer, producing a long internal chain of thought before responding to the user. The o1 reasoning model is designed to solve hard problems across domains. The knowledge cutoff for o1 and o1-mini models is October, 2023.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "openai", "model": "o1:high", "geolocation": "global", "input_tokens_price_per_million": "15.0", "caching_tokens_price_per_million": "15.0", "cached_tokens_price_per_million": "7.5", "output_tokens_price_per_million": "60.0", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "The o1 series of models are trained with reinforcement learning to perform complex reasoning. o1 models think before they answer, producing a long internal chain of thought before responding to the user. The o1 reasoning model is designed to solve hard problems across domains. The knowledge cutoff for o1 and o1-mini models is October, 2023.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "openai", "model": "o1:low", "geolocation": "global", "input_tokens_price_per_million": "15.0", "caching_tokens_price_per_million": "15.0", "cached_tokens_price_per_million": "7.5", "output_tokens_price_per_million": "60.0", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "The o1 series of models are trained with reinforcement learning to perform complex reasoning. o1 models think before they answer, producing a long internal chain of thought before responding to the user. The o1 reasoning model is designed to solve hard problems across domains. The knowledge cutoff for o1 and o1-mini models is October, 2023.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "openai", "model": "o1:medium", "geolocation": "global", "input_tokens_price_per_million": "15.0", "caching_tokens_price_per_million": "15.0", "cached_tokens_price_per_million": "7.5", "output_tokens_price_per_million": "60.0", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "The o1 series of models are trained with reinforcement learning to perform complex reasoning. o1 models think before they answer, producing a long internal chain of thought before responding to the user. The o1 reasoning model is designed to solve hard problems across domains. The knowledge cutoff for o1 and o1-mini models is October, 2023.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "openai", "model": "o1-mini", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "4.4", "max_output_tokens": 65536, "context_window": 128000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "The o1 series of models are trained with reinforcement learning to perform complex reasoning. o1 models think before they answer, producing a long internal chain of thought before responding to the user. The o1 reasoning model is designed to solve hard problems across domains. The knowledge cutoff for o1 and o1-mini models is October, 2023. o1-mini is a faster and more affordable reasoning model, but OpenAI recommends using the newer o3-mini model that features higher intelligence at the same latency and price as o1-mini.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "openai", "model": "o1-mini-2024-09-12", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "4.4", "max_output_tokens": 65536, "context_window": 128000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "The o1 series of models are trained with reinforcement learning to perform complex reasoning. o1 models think before they answer, producing a long internal chain of thought before responding to the user. The o1 reasoning model is designed to solve hard problems across domains. The knowledge cutoff for o1 and o1-mini models is October, 2023. o1-mini is a faster and more affordable reasoning model, but OpenAI recommends using the newer o3-mini model that features higher intelligence at the same latency and price as o1-mini.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "openai", "model": "o1-mini:high", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "4.4", "max_output_tokens": 65536, "context_window": 128000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "The o1 series of models are trained with reinforcement learning to perform complex reasoning. o1 models think before they answer, producing a long internal chain of thought before responding to the user. The o1 reasoning model is designed to solve hard problems across domains. The knowledge cutoff for o1 and o1-mini models is October, 2023. o1-mini is a faster and more affordable reasoning model, but OpenAI recommends using the newer o3-mini model that features higher intelligence at the same latency and price as o1-mini.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "openai", "model": "o1-mini:low", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "4.4", "max_output_tokens": 65536, "context_window": 128000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "The o1 series of models are trained with reinforcement learning to perform complex reasoning. o1 models think before they answer, producing a long internal chain of thought before responding to the user. The o1 reasoning model is designed to solve hard problems across domains. The knowledge cutoff for o1 and o1-mini models is October, 2023. o1-mini is a faster and more affordable reasoning model, but OpenAI recommends using the newer o3-mini model that features higher intelligence at the same latency and price as o1-mini.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "openai", "model": "o1-mini:medium", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "4.4", "max_output_tokens": 65536, "context_window": 128000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "The o1 series of models are trained with reinforcement learning to perform complex reasoning. o1 models think before they answer, producing a long internal chain of thought before responding to the user. The o1 reasoning model is designed to solve hard problems across domains. The knowledge cutoff for o1 and o1-mini models is October, 2023. o1-mini is a faster and more affordable reasoning model, but OpenAI recommends using the newer o3-mini model that features higher intelligence at the same latency and price as o1-mini.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "openai", "model": "o3", "geolocation": "global", "input_tokens_price_per_million": "10.0", "caching_tokens_price_per_million": "40.0", "cached_tokens_price_per_million": "2.5", "output_tokens_price_per_million": "40.0", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "The o1 series of models are trained with reinforcement learning to perform complex reasoning. o1 models think before they answer, producing a long internal chain of thought before responding to the user. The o1 reasoning model is designed to solve hard problems across domains. The knowledge cutoff for o1 and o1-mini models is October, 2023.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "openai", "model": "o3-2025-04-16", "geolocation": "global", "input_tokens_price_per_million": "10.0", "caching_tokens_price_per_million": "40.0", "cached_tokens_price_per_million": "2.5", "output_tokens_price_per_million": "40.0", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "The o1 series of models are trained with reinforcement learning to perform complex reasoning. o1 models think before they answer, producing a long internal chain of thought before responding to the user. The o1 reasoning model is designed to solve hard problems across domains. The knowledge cutoff for o1 and o1-mini models is October, 2023.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "openai", "model": "o3:flex", "geolocation": "global", "input_tokens_price_per_million": "5.0", "caching_tokens_price_per_million": "20.0", "cached_tokens_price_per_million": "1.25", "output_tokens_price_per_million": "20.0", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": false, "description": "The o1 series of models are trained with reinforcement learning to perform complex reasoning. o1 models think before they answer, producing a long internal chain of thought before responding to the user. The o1 reasoning model is designed to solve hard problems across domains. The knowledge cutoff for o1 and o1-mini models is October, 2023.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "openai", "model": "o3-mini", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "4.4", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "o3-mini is OpenAI's most recent small reasoning model, providing high intelligence at the same cost and latency targets of o1-mini. o3-mini also supports key developer features, like Structured Outputs, function calling, Batch API, and more. Like other models in the o-series, it is designed to excel at science, math, and coding tasks.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "openai", "model": "o3-mini-2025-01-31", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "4.4", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "o3-mini is OpenAI's most recent small reasoning model, providing high intelligence at the same cost and latency targets of o1-mini. o3-mini also supports key developer features, like Structured Outputs, function calling, Batch API, and more. Like other models in the o-series, it is designed to excel at science, math, and coding tasks.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "openai", "model": "o3-mini:high", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "4.4", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "o3-mini is OpenAI's most recent small reasoning model, providing high intelligence at the same cost and latency targets of o1-mini. o3-mini also supports key developer features, like Structured Outputs, function calling, Batch API, and more. Like other models in the o-series, it is designed to excel at science, math, and coding tasks.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "openai", "model": "o3-mini:low", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "4.4", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "o3-mini is OpenAI's most recent small reasoning model, providing high intelligence at the same cost and latency targets of o1-mini. o3-mini also supports key developer features, like Structured Outputs, function calling, Batch API, and more. Like other models in the o-series, it is designed to excel at science, math, and coding tasks.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "openai", "model": "o3-mini:medium", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.55", "output_tokens_price_per_million": "4.4", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "o3-mini is OpenAI's most recent small reasoning model, providing high intelligence at the same cost and latency targets of o1-mini. o3-mini also supports key developer features, like Structured Outputs, function calling, Batch API, and more. Like other models in the o-series, it is designed to excel at science, math, and coding tasks.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "openai", "model": "o4-mini", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.275", "output_tokens_price_per_million": "4.4", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "o3-mini is OpenAI's most recent small reasoning model, providing high intelligence at the same cost and latency targets of o1-mini. o3-mini also supports key developer features, like Structured Outputs, function calling, Batch API, and more. Like other models in the o-series, it is designed to excel at science, math, and coding tasks.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "openai", "model": "o4-mini-2025-04-16", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.275", "output_tokens_price_per_million": "4.4", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "o3-mini is OpenAI's most recent small reasoning model, providing high intelligence at the same cost and latency targets of o1-mini. o3-mini also supports key developer features, like Structured Outputs, function calling, Batch API, and more. Like other models in the o-series, it is designed to excel at science, math, and coding tasks.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "openai", "model": "o4-mini:flex", "geolocation": "global", "input_tokens_price_per_million": "0.55", "caching_tokens_price_per_million": "0.55", "cached_tokens_price_per_million": "0.1375", "output_tokens_price_per_million": "2.2", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "o3-mini is OpenAI's most recent small reasoning model, providing high intelligence at the same cost and latency targets of o1-mini. o3-mini also supports key developer features, like Structured Outputs, function calling, Batch API, and more. Like other models in the o-series, it is designed to excel at science, math, and coding tasks.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "openai", "model": "o4-mini:high", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.275", "output_tokens_price_per_million": "4.4", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "o3-mini is OpenAI's most recent small reasoning model, providing high intelligence at the same cost and latency targets of o1-mini. o3-mini also supports key developer features, like Structured Outputs, function calling, Batch API, and more. Like other models in the o-series, it is designed to excel at science, math, and coding tasks.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "openai", "model": "o4-mini:low", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.275", "output_tokens_price_per_million": "4.4", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "o3-mini is OpenAI's most recent small reasoning model, providing high intelligence at the same cost and latency targets of o1-mini. o3-mini also supports key developer features, like Structured Outputs, function calling, Batch API, and more. Like other models in the o-series, it is designed to excel at science, math, and coding tasks.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "openai", "model": "o4-mini:medium", "geolocation": "global", "input_tokens_price_per_million": "1.1", "caching_tokens_price_per_million": "1.1", "cached_tokens_price_per_million": "0.275", "output_tokens_price_per_million": "4.4", "max_output_tokens": 100000, "context_window": 200000, "supports_caching": true, "supports_vision": false, "supports_computer_use": false, "description": "o3-mini is OpenAI's most recent small reasoning model, providing high intelligence at the same cost and latency targets of o1-mini. o3-mini also supports key developer features, like Structured Outputs, function calling, Batch API, and more. Like other models in the o-series, it is designed to excel at science, math, and coding tasks.", "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-01T09:28:29.031290"}, {"provider": "parasail", "model": "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8", "geolocation": "global", "input_tokens_price_per_million": "0.21", "caching_tokens_price_per_million": "0.85", "cached_tokens_price_per_million": "0.21", "output_tokens_price_per_million": "0.85", "max_output_tokens": 1048576, "context_window": 1048576, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "parasail", "model": "meta-llama/Llama-4-Scout-17B-16E-Instruct", "geolocation": "global", "input_tokens_price_per_million": "0.14", "caching_tokens_price_per_million": "0.58", "cached_tokens_price_per_million": "0.14", "output_tokens_price_per_million": "0.58", "max_output_tokens": 1048576, "context_window": 1048576, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "parasail", "model": "parasail-anubis-pro", "geolocation": "global", "input_tokens_price_per_million": "0.8", "caching_tokens_price_per_million": "0.8", "cached_tokens_price_per_million": "0.8", "output_tokens_price_per_million": "0.8", "max_output_tokens": 8192, "context_window": 64000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "parasail", "model": "parasail-deepseek-r1", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.0", "cached_tokens_price_per_million": "3.0", "output_tokens_price_per_million": "3.0", "max_output_tokens": 8192, "context_window": 64000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "parasail", "model": "parasail-eva-25-72b-v02-fp8", "geolocation": "global", "input_tokens_price_per_million": "0.7", "caching_tokens_price_per_million": "0.7", "cached_tokens_price_per_million": "0.7", "output_tokens_price_per_million": "0.7", "max_output_tokens": 8192, "context_window": 32000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-03-06T16:23:40.613121"}, {"provider": "parasail", "model": "parasail-gemma3-27b-it", "geolocation": "global", "input_tokens_price_per_million": "0.3", "caching_tokens_price_per_million": "0.5", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "0.5", "max_output_tokens": 8192, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "parasail", "model": "parasail-mistral-7b-instruct-03", "geolocation": "global", "input_tokens_price_per_million": "0.11", "caching_tokens_price_per_million": "0.11", "cached_tokens_price_per_million": "0.11", "output_tokens_price_per_million": "0.11", "max_output_tokens": 8192, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-03-06T16:23:40.613121"}, {"provider": "parasail", "model": "parasail-mistral-nemo", "geolocation": "global", "input_tokens_price_per_million": "0.11", "caching_tokens_price_per_million": "0.11", "cached_tokens_price_per_million": "0.11", "output_tokens_price_per_million": "0.11", "max_output_tokens": 8192, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-03-06T16:23:40.613121"}, {"provider": "parasail", "model": "parasail-mythomax-13b", "geolocation": "global", "input_tokens_price_per_million": "0.11", "caching_tokens_price_per_million": "0.11", "cached_tokens_price_per_million": "0.11", "output_tokens_price_per_million": "0.11", "max_output_tokens": 8192, "context_window": 4000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-03-06T16:23:40.613121"}, {"provider": "parasail", "model": "parasail-qwen25-vl-72b-instruct", "geolocation": "global", "input_tokens_price_per_million": "0.7", "caching_tokens_price_per_million": "0.7", "cached_tokens_price_per_million": "0.7", "output_tokens_price_per_million": "0.7", "max_output_tokens": 8192, "context_window": 32768, "supports_caching": false, "supports_vision": true, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "parasail", "model": "parasail-qwen-coder32b-longcontext-128", "geolocation": "global", "input_tokens_price_per_million": "0.5", "caching_tokens_price_per_million": "0.5", "cached_tokens_price_per_million": "0.5", "output_tokens_price_per_million": "0.5", "max_output_tokens": 8192, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "parasail", "model": "parasail-skyfall-36b-v2-fp8", "geolocation": "global", "input_tokens_price_per_million": "0.5", "caching_tokens_price_per_million": "0.5", "cached_tokens_price_per_million": "0.5", "output_tokens_price_per_million": "0.5", "max_output_tokens": 8192, "context_window": 64000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "parasail", "model": "parasail-wayfarer-70b-llama33-fp8", "geolocation": "global", "input_tokens_price_per_million": "0.7", "caching_tokens_price_per_million": "0.7", "cached_tokens_price_per_million": "0.7", "output_tokens_price_per_million": "0.7", "max_output_tokens": 8192, "context_window": 128000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-03-06T16:23:40.613121"}, {"provider": "perplexity", "model": "sonar", "geolocation": "global", "input_tokens_price_per_million": "1.0", "caching_tokens_price_per_million": "1.0", "cached_tokens_price_per_million": "1.0", "output_tokens_price_per_million": "1.0", "max_output_tokens": 8192, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Lightweight offering with search grounding, quicker and cheaper than Sonar Pro.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-03-13T11:22:41.876032"}, {"provider": "perplexity", "model": "sonar-pro", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.0", "cached_tokens_price_per_million": "3.0", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 204800, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Premier search offering with search grounding, supporting advanced queries and follow-ups.", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-03-13T11:22:41.876032"}, {"provider": "perplexity", "model": "sonar-reasoning-pro", "geolocation": "global", "input_tokens_price_per_million": "2.0", "caching_tokens_price_per_million": "2.0", "cached_tokens_price_per_million": "2.0", "output_tokens_price_per_million": "8.0", "max_output_tokens": 8192, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": "Premier reasoning offering powered by DeepSeek R1 with Chain of Thought (CoT).", "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-03-13T11:22:41.876032"}, {"provider": "together", "model": "deepseek-ai/DeepSeek-R1", "geolocation": "global", "input_tokens_price_per_million": "7.0", "caching_tokens_price_per_million": "7.0", "cached_tokens_price_per_million": "7.0", "output_tokens_price_per_million": "7.0", "max_output_tokens": 8192, "context_window": 64000, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "together", "model": "deepseek-ai/DeepSeek-V3", "geolocation": "global", "input_tokens_price_per_million": "1.25", "caching_tokens_price_per_million": "1.25", "cached_tokens_price_per_million": "1.25", "output_tokens_price_per_million": "1.25", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "together", "model": "deepseek-llm-67b-chat", "geolocation": "global", "input_tokens_price_per_million": "0.9", "caching_tokens_price_per_million": "0.9", "cached_tokens_price_per_million": "0.9", "output_tokens_price_per_million": "0.9", "max_output_tokens": null, "context_window": 4096, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-27T20:55:27.171683"}, {"provider": "together", "model": "meta-llama/Llama-2-13b-chat-hf", "geolocation": "global", "input_tokens_price_per_million": "0.22", "caching_tokens_price_per_million": "0.22", "cached_tokens_price_per_million": "0.22", "output_tokens_price_per_million": "0.22", "max_output_tokens": null, "context_window": 4096, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "meta-llama/Llama-2-70b-hf", "geolocation": "global", "input_tokens_price_per_million": "0.9", "caching_tokens_price_per_million": "0.9", "cached_tokens_price_per_million": "0.9", "output_tokens_price_per_million": "0.9", "max_output_tokens": null, "context_window": 4096, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "meta-llama/Llama-2-7b-chat-hf", "geolocation": "global", "input_tokens_price_per_million": "0.2", "caching_tokens_price_per_million": "0.2", "cached_tokens_price_per_million": "0.2", "output_tokens_price_per_million": "0.2", "max_output_tokens": null, "context_window": 4096, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "meta-llama/Llama-3.2-3B-Instruct-Turbo", "geolocation": "global", "input_tokens_price_per_million": "0.06", "caching_tokens_price_per_million": "0.06", "cached_tokens_price_per_million": "0.06", "output_tokens_price_per_million": "0.06", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "meta-llama/Llama-3.3-70B-Instruct-Turbo", "geolocation": "global", "input_tokens_price_per_million": "0.88", "caching_tokens_price_per_million": "0.88", "cached_tokens_price_per_million": "0.88", "output_tokens_price_per_million": "0.88", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "meta-llama/Llama-3-70b-chat-hf", "geolocation": "global", "input_tokens_price_per_million": "0.88", "caching_tokens_price_per_million": "0.88", "cached_tokens_price_per_million": "0.88", "output_tokens_price_per_million": "0.88", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "meta-llama/Llama-3-8b-chat-hf", "geolocation": "global", "input_tokens_price_per_million": "0.2", "caching_tokens_price_per_million": "0.2", "cached_tokens_price_per_million": "0.2", "output_tokens_price_per_million": "0.2", "max_output_tokens": null, "context_window": 8192, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "meta-llama/LlamaGuard-2-8b", "geolocation": "global", "input_tokens_price_per_million": "0.2", "caching_tokens_price_per_million": "0.2", "cached_tokens_price_per_million": "0.2", "output_tokens_price_per_million": "0.2", "max_output_tokens": null, "context_window": 8192, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo", "geolocation": "global", "input_tokens_price_per_million": "3.5", "caching_tokens_price_per_million": "3.5", "cached_tokens_price_per_million": "3.5", "output_tokens_price_per_million": "3.5", "max_output_tokens": null, "context_window": 130815, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo", "geolocation": "global", "input_tokens_price_per_million": "0.88", "caching_tokens_price_per_million": "0.88", "cached_tokens_price_per_million": "0.88", "output_tokens_price_per_million": "0.88", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo", "geolocation": "global", "input_tokens_price_per_million": "0.18", "caching_tokens_price_per_million": "0.18", "cached_tokens_price_per_million": "0.18", "output_tokens_price_per_million": "0.18", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "meta-llama/Meta-Llama-3-70B-Instruct-Lite", "geolocation": "global", "input_tokens_price_per_million": "0.54", "caching_tokens_price_per_million": "0.54", "cached_tokens_price_per_million": "0.54", "output_tokens_price_per_million": "0.54", "max_output_tokens": null, "context_window": 8192, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "meta-llama/Meta-Llama-3-70B-Instruct-Turbo", "geolocation": "global", "input_tokens_price_per_million": "0.88", "caching_tokens_price_per_million": "0.88", "cached_tokens_price_per_million": "0.88", "output_tokens_price_per_million": "0.88", "max_output_tokens": null, "context_window": 8192, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "meta-llama/Meta-Llama-3-8B-Instruct-Lite", "geolocation": "global", "input_tokens_price_per_million": "0.1", "caching_tokens_price_per_million": "0.1", "cached_tokens_price_per_million": "0.1", "output_tokens_price_per_million": "0.1", "max_output_tokens": null, "context_window": 8192, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "meta-llama/Meta-Llama-Guard-3-8B", "geolocation": "global", "input_tokens_price_per_million": "0.2", "caching_tokens_price_per_million": "0.2", "cached_tokens_price_per_million": "0.2", "output_tokens_price_per_million": "0.2", "max_output_tokens": null, "context_window": 8192, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO", "geolocation": "global", "input_tokens_price_per_million": "0.6", "caching_tokens_price_per_million": "0.6", "cached_tokens_price_per_million": "0.6", "output_tokens_price_per_million": "0.6", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF", "geolocation": "global", "input_tokens_price_per_million": "0.88", "caching_tokens_price_per_million": "0.88", "cached_tokens_price_per_million": "0.88", "output_tokens_price_per_million": "0.88", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "Qwen/Qwen2.5-72B-Instruct-Turbo", "geolocation": "global", "input_tokens_price_per_million": "1.2", "caching_tokens_price_per_million": "1.2", "cached_tokens_price_per_million": "1.2", "output_tokens_price_per_million": "1.2", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "Qwen/Qwen2.5-7B-Instruct-Turbo", "geolocation": "global", "input_tokens_price_per_million": "0.3", "caching_tokens_price_per_million": "0.3", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "0.3", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "Qwen/Qwen2.5-Coder-32B-Instruct", "geolocation": "global", "input_tokens_price_per_million": "0.8", "caching_tokens_price_per_million": "0.8", "cached_tokens_price_per_million": "0.8", "output_tokens_price_per_million": "0.8", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "Qwen/Qwen2-72B-Instruct", "geolocation": "global", "input_tokens_price_per_million": "0.9", "caching_tokens_price_per_million": "0.9", "cached_tokens_price_per_million": "0.9", "output_tokens_price_per_million": "0.9", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "Qwen/QwQ-32B-Preview", "geolocation": "global", "input_tokens_price_per_million": "1.2", "caching_tokens_price_per_million": "1.2", "cached_tokens_price_per_million": "1.2", "output_tokens_price_per_million": "1.2", "max_output_tokens": null, "context_window": 32768, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "together", "model": "upstage/SOLAR-10.7B-Instruct-v1.0", "geolocation": "global", "input_tokens_price_per_million": "0.3", "caching_tokens_price_per_million": "0.3", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "0.3", "max_output_tokens": null, "context_window": 4096, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": false, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": "Don't store prompts and responses", "updated_at": "2025-01-15T18:03:44.801708"}, {"provider": "vertex", "model": "anthropic/claude-3-5-sonnet", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-5-sonnet-20241022", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-5-sonnet-20241022@europe-west1", "geolocation": "eu", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-5-sonnet-20241022@us-east5", "geolocation": "us", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-5-sonnet@europe-west1", "geolocation": "eu", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-5-sonnet-latest", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-5-sonnet-latest@europe-west1", "geolocation": "eu", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-5-sonnet-latest@us-east5", "geolocation": "us", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-5-sonnet@us-east5", "geolocation": "us", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 8192, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "<PERSON><PERSON><PERSON>'s previous most intelligent model. High level of intelligence and capability. Excells in coding.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-7-sonnet", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-7-sonnet-20250219", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-7-sonnet-20250219@europe-west1", "geolocation": "eu", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-7-sonnet-20250219@us-east5", "geolocation": "us", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-7-sonnet@europe-west1", "geolocation": "eu", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-7-sonnet-latest", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-7-sonnet-latest@europe-west1", "geolocation": "eu", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-7-sonnet-latest@us-east5", "geolocation": "us", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-3-7-sonnet@us-east5", "geolocation": "us", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-4-sonnet", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "vertex", "model": "anthropic/claude-4-sonnet-latest", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.75", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "15.0", "max_output_tokens": 64000, "context_window": 200000, "supports_caching": true, "supports_vision": true, "supports_computer_use": true, "description": "Anthropic's most intelligent model. The first hybrid reasoning model on the market with the highest level of intelligence and capability with toggleable extended thinking. Top-tier results in reasoning, coding, multilingual tasks, long-context handling, honesty, and image processing.", "data_retention": false, "data_retention_days": 30, "data_used_for_training": false, "privacy_comments": "https://cloud.google.com/vertex-ai/generative-ai/docs/data-governance#customer_data_retention_and_achieving_zero_data_retention", "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "xai", "model": "grok-2-1212", "geolocation": "global", "input_tokens_price_per_million": "2.0", "caching_tokens_price_per_million": "2.0", "cached_tokens_price_per_million": "2.0", "output_tokens_price_per_million": "10.0", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "xai", "model": "grok-2-latest", "geolocation": "global", "input_tokens_price_per_million": "2.0", "caching_tokens_price_per_million": "2.0", "cached_tokens_price_per_million": "2.0", "output_tokens_price_per_million": "10.0", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "xai", "model": "grok-3-beta", "geolocation": "global", "input_tokens_price_per_million": "3.0", "caching_tokens_price_per_million": "3.0", "cached_tokens_price_per_million": "3.0", "output_tokens_price_per_million": "15.0", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "xai", "model": "grok-3-fast-beta", "geolocation": "global", "input_tokens_price_per_million": "5.0", "caching_tokens_price_per_million": "5.0", "cached_tokens_price_per_million": "5.0", "output_tokens_price_per_million": "25.0", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "xai", "model": "grok-3-mini-beta", "geolocation": "global", "input_tokens_price_per_million": "0.3", "caching_tokens_price_per_million": "0.3", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "0.5", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "xai", "model": "grok-3-mini-beta:high", "geolocation": "global", "input_tokens_price_per_million": "0.3", "caching_tokens_price_per_million": "0.3", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "0.5", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "xai", "model": "grok-3-mini-beta:low", "geolocation": "global", "input_tokens_price_per_million": "0.3", "caching_tokens_price_per_million": "0.3", "cached_tokens_price_per_million": "0.3", "output_tokens_price_per_million": "0.5", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "xai", "model": "grok-3-mini-fast-beta", "geolocation": "global", "input_tokens_price_per_million": "0.6", "caching_tokens_price_per_million": "0.6", "cached_tokens_price_per_million": "0.6", "output_tokens_price_per_million": "4.0", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "xai", "model": "grok-3-mini-fast-beta:high", "geolocation": "global", "input_tokens_price_per_million": "0.6", "caching_tokens_price_per_million": "0.6", "cached_tokens_price_per_million": "0.6", "output_tokens_price_per_million": "4.0", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}, {"provider": "xai", "model": "grok-3-mini-fast-beta:low", "geolocation": "global", "input_tokens_price_per_million": "0.6", "caching_tokens_price_per_million": "0.6", "cached_tokens_price_per_million": "0.6", "output_tokens_price_per_million": "4.0", "max_output_tokens": null, "context_window": 131072, "supports_caching": false, "supports_vision": false, "supports_computer_use": false, "description": null, "data_retention": true, "data_retention_days": null, "data_used_for_training": false, "privacy_comments": null, "updated_at": "2025-02-06T18:18:28.805550"}]