/**
 * Credential Error Handling Utilities
 * 
 * This module provides comprehensive error handling for credential operations,
 * including error classification, user-friendly messages, and retry logic.
 */

import { CredentialError, CredentialErrorType } from '../types/credentials';

/**
 * Handle and classify errors from credential operations
 * Converts various error formats into standardized CredentialError objects
 */
export const handleCredentialError = (error: any): CredentialError => {
  // Handle axios errors with response
  if (error.response) {
    const status = error.response.status;
    const data = error.response.data || {};
    
    switch (status) {
      case 401:
        return createCredentialError(
          CredentialErrorType.AUTHENTICATION_ERROR,
          'Authentication required. Please log in again.',
          undefined,
          '401',
          error
        );
        
      case 403:
        return createCredentialError(
          CredentialErrorType.PERMISSION_ERROR,
          'You do not have permission to perform this action.',
          undefined,
          '403',
          error
        );
        
      case 404:
        return createCredentialError(
          CredentialErrorType.NOT_FOUND_ERROR,
          'Credential not found.',
          undefined,
          '404',
          error
        );
        
      case 400:
      case 422:
        return createCredentialError(
          CredentialErrorType.VALIDATION_ERROR,
          data.message || 'Invalid credential data.',
          data.field,
          status.toString(),
          error
        );
        
      case 500:
      case 502:
      case 503:
      case 504:
        return createCredentialError(
          CredentialErrorType.SERVER_ERROR,
          'Server error occurred. Please try again later.',
          undefined,
          status.toString(),
          error
        );
        
      default:
        return createCredentialError(
          CredentialErrorType.UNKNOWN_ERROR,
          data.message || 'An unexpected error occurred.',
          undefined,
          status.toString(),
          error
        );
    }
  }
  
  // Handle network errors (no response)
  if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
    return createCredentialError(
      CredentialErrorType.NETWORK_ERROR,
      'Network error. Please check your connection and try again.',
      undefined,
      'NETWORK_ERROR',
      error
    );
  }
  
  // Handle timeout errors
  if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
    return createCredentialError(
      CredentialErrorType.NETWORK_ERROR,
      'Request timeout. Please try again.',
      undefined,
      'ECONNABORTED',
      error
    );
  }
  
  // Handle connection refused
  if (error.code === 'ECONNREFUSED') {
    return createCredentialError(
      CredentialErrorType.NETWORK_ERROR,
      'Unable to connect to server. Please try again later.',
      undefined,
      'ECONNREFUSED',
      error
    );
  }
  
  // Handle string errors
  if (typeof error === 'string') {
    return createCredentialError(
      CredentialErrorType.UNKNOWN_ERROR,
      error,
      undefined,
      undefined,
      error
    );
  }
  
  // Handle errors with message property
  if (error.message) {
    return createCredentialError(
      CredentialErrorType.UNKNOWN_ERROR,
      error.message,
      undefined,
      error.code,
      error
    );
  }
  
  // Fallback for unknown error structures
  return createCredentialError(
    CredentialErrorType.UNKNOWN_ERROR,
    'An unexpected error occurred. Please try again.',
    undefined,
    undefined,
    error
  );
};

/**
 * Get user-friendly error message from CredentialError
 * Provides consistent error messaging across the application
 */
export const getErrorMessage = (error: CredentialError): string => {
  if (!error || !error.message) {
    return 'An unexpected error occurred';
  }
  
  // Return the error message directly
  // Additional formatting can be added here if needed
  return error.message;
};

/**
 * Determine if an error should trigger a retry
 * Used for implementing retry logic in API calls
 */
export const shouldRetry = (error: CredentialError): boolean => {
  return isRetryableError(error.type);
};

/**
 * Check if an error type is retryable
 * Separates transient errors from permanent failures
 */
export const isRetryableError = (errorType: CredentialErrorType): boolean => {
  switch (errorType) {
    case CredentialErrorType.NETWORK_ERROR:
    case CredentialErrorType.SERVER_ERROR:
      return true;
      
    case CredentialErrorType.AUTHENTICATION_ERROR:
    case CredentialErrorType.PERMISSION_ERROR:
    case CredentialErrorType.VALIDATION_ERROR:
    case CredentialErrorType.NOT_FOUND_ERROR:
    case CredentialErrorType.UNKNOWN_ERROR:
      return false;
      
    default:
      return false;
  }
};

/**
 * Create a standardized CredentialError object
 * Factory function for consistent error creation
 */
export const createCredentialError = (
  type: CredentialErrorType,
  message: string,
  field?: string,
  code?: string,
  originalError?: any
): CredentialError => {
  return {
    type,
    message,
    field,
    code,
    originalError,
  };
};

/**
 * Log credential errors with appropriate detail level
 * Sanitizes sensitive information before logging
 */
export const logCredentialError = (
  operation: string,
  error: CredentialError,
  context?: Record<string, any>
): void => {
  const logData = {
    operation,
    errorType: error.type,
    message: error.message,
    code: error.code,
    field: error.field,
    context: sanitizeLogContext(context),
    timestamp: new Date().toISOString(),
  };
  
  // Use appropriate log level based on error type
  switch (error.type) {
    case CredentialErrorType.AUTHENTICATION_ERROR:
    case CredentialErrorType.PERMISSION_ERROR:
      console.warn('Credential operation failed:', logData);
      break;
      
    case CredentialErrorType.VALIDATION_ERROR:
    case CredentialErrorType.NOT_FOUND_ERROR:
      console.info('Credential operation failed:', logData);
      break;
      
    case CredentialErrorType.NETWORK_ERROR:
    case CredentialErrorType.SERVER_ERROR:
    case CredentialErrorType.UNKNOWN_ERROR:
      console.error('Credential operation failed:', logData);
      break;
      
    default:
      console.error('Credential operation failed:', logData);
  }
};

/**
 * Sanitize log context to remove sensitive information
 * Prevents credential values from appearing in logs
 */
const sanitizeLogContext = (context?: Record<string, any>): Record<string, any> | undefined => {
  if (!context) return undefined;
  
  const sanitized = { ...context };
  
  // Remove sensitive fields
  const sensitiveFields = ['value', 'password', 'token', 'secret', 'key'];
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });
  
  return sanitized;
};

/**
 * Get retry delay based on attempt number
 * Implements exponential backoff with jitter
 */
export const getRetryDelay = (attempt: number, baseDelay: number = 1000): number => {
  // Exponential backoff: baseDelay * 2^attempt
  const exponentialDelay = baseDelay * Math.pow(2, attempt);
  
  // Add jitter (±25% of the delay)
  const jitter = exponentialDelay * 0.25 * (Math.random() - 0.5);
  
  // Cap at 30 seconds
  const maxDelay = 30000;
  
  return Math.min(exponentialDelay + jitter, maxDelay);
};

/**
 * Create a retry wrapper for credential operations
 * Automatically retries operations based on error type
 */
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: CredentialError;
  
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = handleCredentialError(error);
      
      // Don't retry if it's not a retryable error
      if (!shouldRetry(lastError)) {
        throw lastError;
      }
      
      // Don't retry on the last attempt
      if (attempt === maxAttempts - 1) {
        throw lastError;
      }
      
      // Wait before retrying
      const delay = getRetryDelay(attempt, baseDelay);
      await new Promise(resolve => setTimeout(resolve, delay));
      
      logCredentialError('retry', lastError, { attempt: attempt + 1, maxAttempts });
    }
  }
  
  // This should never be reached, but TypeScript requires it
  throw lastError!;
};

/**
 * Validate error response structure
 * Ensures error objects conform to expected format
 */
export const isValidCredentialError = (error: any): error is CredentialError => {
  return (
    error &&
    typeof error === 'object' &&
    'type' in error &&
    'message' in error &&
    Object.values(CredentialErrorType).includes(error.type)
  );
};
