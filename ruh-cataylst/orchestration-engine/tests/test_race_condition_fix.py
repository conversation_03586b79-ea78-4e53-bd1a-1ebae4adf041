#!/usr/bin/env python3
"""
Test script to validate the race condition fix for loop parameter resolution.

This script tests the enhanced parameter resolution system that can handle
loop iteration data retrieval from multiple storage locations.
"""

import asyncio
import sys
import os
import json
from unittest.mock import Mock, MagicMock

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core_.workflow_utils import WorkflowUtils
from app.core_.state_manager import WorkflowStateManager


class TestRaceConditionFix:
    """Test class for validating the race condition fix."""
    
    def __init__(self):
        self.workflow_utils = WorkflowUtils("test-workflow-123")
        self.state_manager = self.workflow_utils.state_manager
        
    def setup_test_data(self):
        """Set up test data that simulates the race condition scenario."""
        
        # Simulate loop transition ID
        self.loop_transition_id = "transition-LoopNode-1751192233407"
        
        # Simulate iteration data that should be available for parameter resolution
        self.iteration_data = {
            "current_item": 6,
            "iteration_index": 6,
            "iteration_metadata": {
                "timestamp": 2205750.251196996,
                "loop_id": self.loop_transition_id,
                "total_iterations": 7
            }
        }
        
        # Simulate final loop results (what causes the race condition)
        self.final_loop_results = {"final_results": []}
        
        # Simulate handle mappings that expect current_item
        self.handle_mappings = {
            self.loop_transition_id: [
                {
                    "source_handle_id": "current_item",
                    "target_handle_id": "main_input",
                    "edge_id": "edge-123"
                }
            ]
        }
        
        # Simulate all_previous_results with only final results (race condition scenario)
        self.all_previous_results = {
            self.loop_transition_id: self.final_loop_results
        }
        
    def test_scenario_1_memory_storage(self):
        """Test scenario where iteration data is available in memory."""
        print("\n🧪 Test Scenario 1: Iteration data in memory")
        
        # Store iteration data in memory (simulating loop executor storage)
        self.state_manager.transition_results[self.loop_transition_id] = self.iteration_data
        
        # Test parameter resolution
        resolved_params = self.workflow_utils._resolve_handle_data(
            current_tool_params={},
            all_previous_results=self.all_previous_results,
            handle_mappings=self.handle_mappings
        )
        
        print(f"✅ Resolved parameters: {resolved_params}")
        return "main_input" in resolved_params and resolved_params["main_input"] == 6
        
    def test_scenario_2_backup_key_storage(self):
        """Test scenario where iteration data is available via backup key."""
        print("\n🧪 Test Scenario 2: Iteration data in backup key")
        
        # Clear memory storage
        self.state_manager.transition_results.clear()
        
        # Store iteration data with backup key (simulating enhanced loop executor)
        backup_key = f"backup_{self.loop_transition_id}_iteration_6"
        self.state_manager.transition_results[backup_key] = self.iteration_data
        
        # Test parameter resolution
        resolved_params = self.workflow_utils._resolve_handle_data(
            current_tool_params={},
            all_previous_results=self.all_previous_results,
            handle_mappings=self.handle_mappings
        )
        
        print(f"✅ Resolved parameters: {resolved_params}")
        return "main_input" in resolved_params and resolved_params["main_input"] == 6
        
    def test_scenario_3_redis_storage(self):
        """Test scenario where iteration data is available in Redis."""
        print("\n🧪 Test Scenario 3: Iteration data in Redis")
        
        # Clear memory storage
        self.state_manager.transition_results.clear()
        
        # Store iteration data in Redis with iteration key
        iteration_key = f"loop_iteration_{self.loop_transition_id}_6"
        self.state_manager.mark_transition_completed(iteration_key, self.iteration_data)
        
        # Test parameter resolution
        resolved_params = self.workflow_utils._resolve_handle_data(
            current_tool_params={},
            all_previous_results=self.all_previous_results,
            handle_mappings=self.handle_mappings
        )
        
        print(f"✅ Resolved parameters: {resolved_params}")
        return "main_input" in resolved_params and resolved_params["main_input"] == 6
        
    def test_scenario_4_race_condition_failure(self):
        """Test scenario that simulates the original race condition failure."""
        print("\n🧪 Test Scenario 4: Original race condition (should fail gracefully)")

        # Create a fresh workflow utils and state manager to simulate clean state
        fresh_workflow_utils = WorkflowUtils("test-workflow-clean")
        fresh_state_manager = fresh_workflow_utils.state_manager

        # Only have final results available (race condition scenario)
        # Test parameter resolution with clean state
        resolved_params = fresh_workflow_utils._resolve_handle_data(
            current_tool_params={},
            all_previous_results=self.all_previous_results,
            handle_mappings=self.handle_mappings
        )

        print(f"⚠️ Resolved parameters (should be empty): {resolved_params}")
        print(f"🔍 Debug - Fresh memory storage keys: {list(fresh_state_manager.transition_results.keys())}")
        print(f"🔍 Debug - All previous results: {self.all_previous_results}")
        return "main_input" not in resolved_params  # Should fail gracefully
        
    def run_all_tests(self):
        """Run all test scenarios."""
        print("🚀 Starting Race Condition Fix Tests")
        print("=" * 50)
        
        self.setup_test_data()
        
        test_results = []
        
        # Run all test scenarios
        test_results.append(("Memory Storage", self.test_scenario_1_memory_storage()))
        test_results.append(("Backup Key Storage", self.test_scenario_2_backup_key_storage()))
        test_results.append(("Redis Storage", self.test_scenario_3_redis_storage()))
        test_results.append(("Race Condition Failure", self.test_scenario_4_race_condition_failure()))
        
        # Print results
        print("\n" + "=" * 50)
        print("🏁 Test Results Summary")
        print("=" * 50)
        
        all_passed = True
        for test_name, result in test_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False
                
        print("\n" + "=" * 50)
        if all_passed:
            print("🎉 All tests passed! Race condition fix is working.")
        else:
            print("⚠️ Some tests failed. Race condition fix needs adjustment.")
        print("=" * 50)
        
        return all_passed


def main():
    """Main function to run the race condition fix tests."""
    try:
        tester = TestRaceConditionFix()
        success = tester.run_all_tests()
        
        if success:
            print("\n✅ Race condition fix validation completed successfully!")
            return 0
        else:
            print("\n❌ Race condition fix validation failed!")
            return 1
            
    except Exception as e:
        print(f"\n💥 Test execution failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
