/**
 * Credential Management Type Definitions
 * 
 * This file contains all type definitions for the credential management system,
 * including frontend types, backend API types, and transformation utilities.
 */

// ============================================================================
// Frontend Types (Used in UI Components)
// ============================================================================

export interface Credential {
  id: string;
  name: string;           // Mapped from backend key_name
  description?: string;
  createdAt: string;      // Mapped from backend created_at
  updatedAt: string;      // Mapped from backend updated_at
  lastUsedAt: string;     // Mapped from backend last_used_at
}

export interface CredentialCreate {
  name: string;           // Will be mapped to key_name for backend
  value: string;
  description?: string;
}

export interface CredentialUpdate {
  name?: string;          // Will be mapped to key_name for backend
  value?: string;
  description?: string;
}

export interface CredentialListResponse {
  credentials: Credential[];
}

export interface CredentialDeleteResponse {
  success: boolean;
  message?: string;
}

// ============================================================================
// Backend API Types (Matching api-gateway schemas)
// ============================================================================

export interface BackendCredentialCreate {
  key_name: string;
  value: string;
  description?: string;
}

export interface BackendCredentialUpdate {
  key_name?: string;
  value?: string;
  description?: string;
}

export interface BackendCredentialInfo {
  id: string;
  key_name: string;
  description?: string;
  value: string;          // Note: Should be excluded from list responses for security
  created_at: string;
  updated_at: string;
  last_used_at: string;
}

export interface BackendCredentialResponse {
  success: boolean;
  message: string;
  id?: string;
  key_name?: string;
}

export interface BackendCredentialListResponse {
  success: boolean;
  message: string;
  credentials: BackendCredentialInfo[];
}

export interface BackendCredentialDetailsResponse {
  success: boolean;
  message: string;
  credential?: BackendCredentialInfo;
}

export interface BackendCredentialDeleteResponse {
  success: boolean;
  message: string;
}

// ============================================================================
// Error Types
// ============================================================================

export enum CredentialErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface CredentialError {
  type: CredentialErrorType;
  message: string;
  field?: string;
  code?: string;
  originalError?: any;
}

// ============================================================================
// Workflow Integration Types
// ============================================================================

export interface CredentialInputValue {
  use_credential_id: boolean;
  credential_id?: string;
  value?: string;
  credential_type?: string;
}

export interface CredentialSelectorProps {
  value?: string;
  onChange: (credentialId: string) => void;
  credentialType?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  onCreateNew?: () => void;
}

// ============================================================================
// Cache Types
// ============================================================================

export interface CachedCredential {
  data: Credential[];
  expiry: number;
}

export interface CredentialCacheConfig {
  ttl: number;            // Time to live in milliseconds
  maxSize: number;        // Maximum cache entries
}

// ============================================================================
// Service Types
// ============================================================================

export interface CredentialServiceInterface {
  fetchCredentials(): Promise<CredentialListResponse>;
  createCredential(data: CredentialCreate): Promise<Credential>;
  getCredential(id: string): Promise<Credential>;
  updateCredential(id: string, data: CredentialUpdate): Promise<Credential>;
  deleteCredential(id: string): Promise<void>;
  getCredentialValueForExecution(id: string): Promise<string>;
}

// ============================================================================
// Validation Types
// ============================================================================

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

// ============================================================================
// Migration Types
// ============================================================================

export interface LocalStorageCredential {
  id: string;
  name: string;
  type: string;
}

export interface MigrationResult {
  success: boolean;
  migratedCount: number;
  failedCount: number;
  errors: string[];
}

// ============================================================================
// Performance Monitoring Types
// ============================================================================

export interface PerformanceMetrics {
  operation: string;
  duration: number;
  success: boolean;
  timestamp: number;
}

// ============================================================================
// Feature Flag Types
// ============================================================================

export interface CredentialFeatureFlags {
  useRealAPI: boolean;
  enableCaching: boolean;
  enableMigration: boolean;
  rolloutPercentage: number;
}

// ============================================================================
// Type Guards
// ============================================================================

export const isCredentialError = (error: any): error is CredentialError => {
  return error && typeof error === 'object' && 'type' in error && 'message' in error;
};

export const isBackendCredentialResponse = (response: any): response is BackendCredentialResponse => {
  return response && typeof response === 'object' && 'success' in response && 'message' in response;
};

export const isBackendCredentialListResponse = (response: any): response is BackendCredentialListResponse => {
  return response && 
         typeof response === 'object' && 
         'success' in response && 
         'message' in response && 
         'credentials' in response &&
         Array.isArray(response.credentials);
};

// ============================================================================
// Constants
// ============================================================================

export const CREDENTIAL_TYPES = {
  API_KEY: 'api_key',
  OAUTH_TOKEN: 'oauth_token',
  PASSWORD: 'password',
  CONNECTION_STRING: 'connection_string',
  OTHER: 'other'
} as const;

export type CredentialType = typeof CREDENTIAL_TYPES[keyof typeof CREDENTIAL_TYPES];

export const CREDENTIAL_CACHE_CONFIG: CredentialCacheConfig = {
  ttl: 5 * 60 * 1000,     // 5 minutes
  maxSize: 100            // Maximum 100 cached entries
};

export const PERFORMANCE_THRESHOLDS = {
  API_RESPONSE: 100,      // 100ms
  UI_RENDERING: 50,       // 50ms
  CREDENTIAL_LOADING: 200 // 200ms
} as const;
