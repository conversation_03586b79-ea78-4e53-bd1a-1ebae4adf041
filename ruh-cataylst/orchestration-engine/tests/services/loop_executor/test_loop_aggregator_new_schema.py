"""
Tests for LoopAggregator new schema aggregation types.

This module tests the new aggregation types introduced for the new loop node schema.
"""

import pytest
from app.services.loop_executor.loop_aggregator import LoopAggregator


class TestLoopAggregatorNewSchema:
    """Test the new schema aggregation types in LoopAggregator."""

    @pytest.fixture
    def aggregator(self):
        """Create a LoopAggregator instance for testing."""
        return LoopAggregator()

    @pytest.fixture
    def sample_results(self):
        """Create sample iteration results for testing."""
        return {
            0: {"status": "success", "result": "data1"},
            1: {"status": "failed", "error": "error1"},
            2: {"status": "success", "result": "data2"},
            3: {"status": "success", "result": "data3"},
        }

    def test_collect_all_aggregation(self, aggregator, sample_results):
        """Test collect_all aggregation type."""
        config = {"type": "collect_all"}
        result = aggregator.aggregate_results(sample_results, config)
        
        # Should return all results as a list
        assert isinstance(result, list)
        assert len(result) == 4
        assert result[0]["status"] == "success"
        assert result[1]["status"] == "failed"

    def test_collect_all_with_metadata(self, aggregator, sample_results):
        """Test collect_all aggregation with metadata."""
        config = {"type": "collect_all", "include_metadata": True}
        result = aggregator.aggregate_results(sample_results, config)
        
        # Should return dict with results and metadata
        assert isinstance(result, dict)
        assert "results" in result
        assert "metadata" in result
        assert result["metadata"]["total_count"] == 4
        assert result["metadata"]["aggregation_type"] == "collect_all"

    def test_collect_successful_aggregation(self, aggregator, sample_results):
        """Test collect_successful aggregation type."""
        config = {"type": "collect_successful"}
        result = aggregator.aggregate_results(sample_results, config)
        
        # Should return only successful results
        assert isinstance(result, list)
        assert len(result) == 3  # 3 successful results
        for item in result:
            assert item["status"] == "success"

    def test_collect_successful_with_metadata(self, aggregator, sample_results):
        """Test collect_successful aggregation with metadata."""
        config = {"type": "collect_successful", "include_metadata": True}
        result = aggregator.aggregate_results(sample_results, config)
        
        # Should return dict with successful results and metadata
        assert isinstance(result, dict)
        assert "results" in result
        assert "metadata" in result
        assert len(result["results"]) == 3
        assert result["metadata"]["successful_count"] == 3
        assert result["metadata"]["total_count"] == 4
        assert result["metadata"]["success_rate"] == 0.75

    def test_count_only_aggregation(self, aggregator, sample_results):
        """Test count_only aggregation type."""
        config = {"type": "count_only"}
        result = aggregator.aggregate_results(sample_results, config)
        
        # Should return count information
        assert isinstance(result, dict)
        assert result["total_count"] == 4
        assert result["successful_count"] == 3
        assert result["failed_count"] == 1

    def test_count_only_with_metadata(self, aggregator, sample_results):
        """Test count_only aggregation with metadata."""
        config = {"type": "count_only", "include_metadata": True}
        result = aggregator.aggregate_results(sample_results, config)
        
        # Should include metadata
        assert "metadata" in result
        assert result["metadata"]["aggregation_type"] == "count_only"

    def test_latest_only_aggregation(self, aggregator, sample_results):
        """Test latest_only aggregation type."""
        config = {"type": "latest_only"}
        result = aggregator.aggregate_results(sample_results, config)
        
        # Should return the last result
        assert isinstance(result, dict)
        assert result["status"] == "success"
        assert result["result"] == "data3"

    def test_latest_only_with_metadata(self, aggregator, sample_results):
        """Test latest_only aggregation with metadata."""
        config = {"type": "latest_only", "include_metadata": True}
        result = aggregator.aggregate_results(sample_results, config)
        
        # Should return dict with result and metadata
        assert isinstance(result, dict)
        assert "result" in result
        assert "metadata" in result
        assert result["metadata"]["total_count"] == 4
        assert result["metadata"]["aggregation_type"] == "latest_only"

    def test_first_success_aggregation(self, aggregator, sample_results):
        """Test first_success aggregation type."""
        config = {"type": "first_success"}
        result = aggregator.aggregate_results(sample_results, config)
        
        # Should return the first successful result
        assert isinstance(result, dict)
        assert result["status"] == "success"
        assert result["result"] == "data1"

    def test_first_success_no_success(self, aggregator):
        """Test first_success aggregation when no successful results."""
        failed_results = {
            0: {"status": "failed", "error": "error1"},
            1: {"status": "failed", "error": "error2"},
        }
        config = {"type": "first_success"}
        result = aggregator.aggregate_results(failed_results, config)
        
        # Should return None when no successful results
        assert result is None

    def test_combine_text_aggregation(self, aggregator):
        """Test combine_text aggregation type."""
        text_results = {
            0: "Hello",
            1: "World",
            2: "Test",
        }
        config = {"type": "combine_text"}
        result = aggregator.aggregate_results(text_results, config)
        
        # Should combine text with default separator
        assert isinstance(result, str)
        assert result == "Hello\nWorld\nTest"

    def test_combine_text_custom_separator(self, aggregator):
        """Test combine_text aggregation with custom separator."""
        text_results = {
            0: "Hello",
            1: "World",
            2: "Test",
        }
        config = {"type": "combine_text", "separator": " "}
        result = aggregator.aggregate_results(text_results, config)
        
        # Should combine text with custom separator
        assert result == "Hello World Test"

    def test_combine_text_with_dict_results(self, aggregator):
        """Test combine_text aggregation with dictionary results."""
        dict_results = {
            0: {"text": "Hello"},
            1: {"output": "World"},
            2: {"result": "Test"},
            3: {"other": "value"},  # Should use str() representation
        }
        config = {"type": "combine_text", "separator": " "}
        result = aggregator.aggregate_results(dict_results, config)
        
        # Should extract text from dict fields
        assert "Hello" in result
        assert "World" in result
        assert "Test" in result

    def test_combine_text_with_metadata(self, aggregator):
        """Test combine_text aggregation with metadata."""
        text_results = {
            0: "Hello",
            1: "World",
        }
        config = {"type": "combine_text", "include_metadata": True, "separator": " "}
        result = aggregator.aggregate_results(text_results, config)
        
        # Should return dict with text and metadata
        assert isinstance(result, dict)
        assert "text" in result
        assert "metadata" in result
        assert result["text"] == "Hello World"
        assert result["metadata"]["character_count"] == 11
        assert result["metadata"]["separator"] == " "

    def test_is_result_successful(self, aggregator):
        """Test the _is_result_successful helper method."""
        # Test explicit success status
        assert aggregator._is_result_successful({"status": "success"})
        assert aggregator._is_result_successful({"status": "completed"})
        assert aggregator._is_result_successful({"status": "ok"})
        
        # Test no error field
        assert aggregator._is_result_successful({"result": "data"})
        
        # Test result/data presence
        assert aggregator._is_result_successful({"data": "some_data"})
        
        # Test non-dict success
        assert aggregator._is_result_successful("some_result")
        assert aggregator._is_result_successful(42)
        
        # Test failure cases
        assert not aggregator._is_result_successful({"status": "failed"})
        assert not aggregator._is_result_successful({"error": "some_error"})
        assert not aggregator._is_result_successful(None)

    def test_validation_with_new_types(self, aggregator):
        """Test validation of new aggregation types."""
        # Test valid new types
        valid_configs = [
            {"type": "collect_all"},
            {"type": "collect_successful"},
            {"type": "count_only"},
            {"type": "latest_only"},
            {"type": "first_success"},
            {"type": "combine_text"},
        ]
        
        for config in valid_configs:
            assert aggregator.validate_aggregation_config(config)

    def test_empty_results_handling(self, aggregator):
        """Test handling of empty results for new aggregation types."""
        empty_results = {}
        
        # All new aggregation types should handle empty results gracefully
        configs = [
            {"type": "collect_all"},
            {"type": "collect_successful"},
            {"type": "count_only"},
            {"type": "latest_only"},
            {"type": "first_success"},
            {"type": "combine_text"},
        ]
        
        for config in configs:
            result = aggregator.aggregate_results(empty_results, config)
            # Should not raise exception and return appropriate empty result
            assert result is not None or config["type"] in ["latest_only", "first_success"]
