"""
Pinecone Memory implementation for AutoGen agents.

This module provides a Pinecone-based memory implementation that follows
AutoGen's Memory protocol for storing and retrieving agent memories.
"""

import asyncio
import hashlib
import json
import logging
from typing import Any, Dict, List, Optional

from autogen_core.memory import Memory, MemoryContent, MemoryMimeType
from autogen_core.model_context import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ontext
from openai import OpenAI
from pinecone import Pinecone, ServerlessSpec

from ..shared.config.base import get_settings

logger = logging.getLogger(__name__)


class PineconeMemory(Memory):
    """
    Pinecone-based memory implementation for AutoGen agents.

    This class implements the AutoGen Memory protocol using Pinecone as the
    vector database backend for storing and retrieving agent memories.
    """

    def __init__(
        self,
        agent_id: str,
        user_id: Optional[str] = None,
        namespace: Optional[str] = None,
        embedding_model: str = "text-embedding-3-small",
        k: int = 5,
        score_threshold: float = 0.7,
        auto_create_index: bool = True,
    ):
        """
        Initialize Pinecone memory.

        Args:
            agent_id: Unique identifier for the agent
            user_id: Optional user identifier for multi-tenant scenarios
            namespace: Optional namespace for organizing memories
            embedding_model: OpenAI embedding model to use
            k: Number of top results to return in queries
            score_threshold: Minimum similarity score for retrieval
            auto_create_index: Whether to automatically create index if it doesn't exist
        """
        self.settings = get_settings()
        self.agent_id = agent_id
        self.user_id = user_id
        self.embedding_model = embedding_model
        self.k = k
        self.score_threshold = score_threshold

        # Create namespace based on agent_id and user_id
        if namespace:
            self.namespace = namespace
        elif user_id:
            self.namespace = f"agent-{agent_id}-user-{user_id}"
        else:
            self.namespace = f"agent-{agent_id}"

        # Initialize Pinecone client
        self.pc = Pinecone(api_key=self.settings.pinecone.api_key)

        # Initialize OpenAI client for embeddings
        self.openai_client = OpenAI(api_key=self.settings.openai.api_key)

        # Initialize index
        self.index = None
        self._initialized = False

        if auto_create_index:
            asyncio.create_task(self._ensure_index_exists())

    async def _ensure_index_exists(self):
        """Ensure the Pinecone index exists, create if it doesn't."""
        try:
            index_name = self.settings.pinecone.index_name

            # Check if index exists
            existing_indexes = self.pc.list_indexes()
            index_exists = any(idx.name == index_name for idx in existing_indexes)

            if not index_exists:
                logger.info(f"Creating Pinecone index: {index_name}")
                self.pc.create_index(
                    name=index_name,
                    dimension=self.settings.pinecone.dimension,
                    metric=self.settings.pinecone.metric,
                    spec=ServerlessSpec(
                        cloud=self.settings.pinecone.cloud,
                        region=self.settings.pinecone.environment,
                    ),
                )

                # Wait for index to be ready
                await asyncio.sleep(5)

            # Connect to index
            self.index = self.pc.Index(index_name)
            self._initialized = True
            logger.info(f"Connected to Pinecone index: {index_name}")

        except Exception as e:
            logger.error(f"Error ensuring Pinecone index exists: {e}")
            raise

    async def _get_embedding(self, text: str) -> List[float]:
        """Get embedding for text using OpenAI."""
        try:
            response = self.openai_client.embeddings.create(
                model=self.embedding_model, input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error getting embedding: {e}")
            raise

    def _generate_memory_id(
        self, content: str, metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate a unique ID for a memory entry."""
        # Create a hash based on content and metadata for deduplication
        content_hash = hashlib.md5(content.encode()).hexdigest()
        metadata_str = json.dumps(metadata or {}, sort_keys=True)
        metadata_hash = hashlib.md5(metadata_str.encode()).hexdigest()
        return f"{self.agent_id}-{content_hash}-{metadata_hash}"

    async def add(self, memory: MemoryContent) -> None:
        """
        Add a memory to the Pinecone index.

        Args:
            memory: The memory content to add
        """
        if not self._initialized:
            await self._ensure_index_exists()

        try:
            # Get embedding for the content
            embedding = await self._get_embedding(memory.content)

            # Generate unique ID
            memory_id = self._generate_memory_id(memory.content, memory.metadata)

            # Prepare metadata
            metadata = {
                "agent_id": self.agent_id,
                "user_id": self.user_id or "",
                "content": memory.content,
                "mime_type": memory.mime_type.value if memory.mime_type else "text",
                "timestamp": str(asyncio.get_event_loop().time()),
            }

            # Add custom metadata
            if memory.metadata:
                metadata.update(memory.metadata)

            # Upsert to Pinecone
            self.index.upsert(
                vectors=[{"id": memory_id, "values": embedding, "metadata": metadata}],
                namespace=self.namespace,
            )

            logger.debug(f"Added memory to Pinecone: {memory_id}")

        except Exception as e:
            logger.error(f"Error adding memory to Pinecone: {e}")
            raise

    async def query(self, query: str, k: Optional[int] = None) -> List[MemoryContent]:
        """
        Query memories from Pinecone.

        Args:
            query: The query string
            k: Number of results to return (defaults to self.k)

        Returns:
            List of relevant memory contents
        """
        if not self._initialized:
            await self._ensure_index_exists()

        try:
            # Get embedding for query
            query_embedding = await self._get_embedding(query)

            # Query Pinecone
            results = self.index.query(
                vector=query_embedding,
                top_k=k or self.k,
                namespace=self.namespace,
                include_metadata=True,
                filter={"agent_id": {"$eq": self.agent_id}},
            )

            # Convert results to MemoryContent
            memories = []
            for match in results.matches:
                if match.score >= self.score_threshold:
                    metadata = match.metadata.copy()
                    content = metadata.pop("content", "")
                    mime_type_str = metadata.pop("mime_type", "text")

                    # Convert mime type string back to enum
                    try:
                        mime_type = MemoryMimeType(mime_type_str)
                    except ValueError:
                        mime_type = MemoryMimeType.TEXT

                    # Remove internal metadata
                    metadata.pop("agent_id", None)
                    metadata.pop("user_id", None)
                    metadata.pop("timestamp", None)

                    # Add score to metadata
                    metadata["score"] = match.score
                    metadata["id"] = match.id

                    memory = MemoryContent(
                        content=content, mime_type=mime_type, metadata=metadata
                    )
                    memories.append(memory)

            logger.debug(f"Retrieved {len(memories)} memories from Pinecone")
            return memories

        except Exception as e:
            logger.error(f"Error querying Pinecone: {e}")
            return []

    async def update_context(self, context: ChatCompletionContext) -> None:
        """
        Update the chat completion context with relevant memories.

        Args:
            context: The chat completion context to update
        """
        try:
            # Extract the latest user message from context to use as query
            messages = await context.get_messages()
            if not messages:
                return

            # Get the last user message as the query
            query_text = ""
            for message in reversed(messages):
                # Check for different message types and attributes
                if hasattr(message, "source") and message.source == "user":
                    query_text = getattr(message, "content", "")
                    break
                elif hasattr(message, "role") and message.role == "user":
                    query_text = getattr(message, "content", "")
                    break
                elif hasattr(message, "content") and isinstance(message.content, str):
                    # Check if this looks like a user message
                    content = message.content
                    if not content.startswith(
                        "Relevant memories:"
                    ) and not content.startswith("You are"):
                        query_text = content
                        break

            if not query_text or len(query_text.strip()) == 0:
                return

            # Query for relevant memories
            memories = await self.query(query_text)

            if memories:
                # Format memories into a context string
                memory_context = "Relevant memories:\n"
                for i, memory in enumerate(memories, 1):
                    memory_context += f"{i}. {memory.content}\n"

                # Add to context as a system message
                from autogen_agentchat.messages import SystemMessage

                system_message = SystemMessage(content=memory_context)
                await context.add_message(system_message)

                logger.debug(f"Added {len(memories)} memories to context")

        except Exception as e:
            logger.error(f"Error updating context with memories: {e}")

    async def clear(self) -> None:
        """Clear all memories for this agent."""
        if not self._initialized:
            await self._ensure_index_exists()

        try:
            # Delete all vectors in the namespace for this agent
            self.index.delete(
                filter={"agent_id": {"$eq": self.agent_id}}, namespace=self.namespace
            )

            logger.info(f"Cleared all memories for agent: {self.agent_id}")

        except Exception as e:
            logger.error(f"Error clearing memories: {e}")
            raise

    async def close(self) -> None:
        """Close the memory and cleanup resources."""
        try:
            # Pinecone client doesn't need explicit closing
            self._initialized = False
            logger.debug("Closed Pinecone memory")
        except Exception as e:
            logger.error(f"Error closing Pinecone memory: {e}")

    async def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the memory store."""
        if not self._initialized:
            await self._ensure_index_exists()

        try:
            stats = self.index.describe_index_stats()

            # Get namespace-specific stats
            namespace_stats = stats.namespaces.get(self.namespace, {})

            return {
                "total_vectors": stats.total_vector_count,
                "namespace_vectors": namespace_stats.get("vector_count", 0),
                "dimension": stats.dimension,
                "index_fullness": stats.index_fullness,
                "agent_id": self.agent_id,
                "namespace": self.namespace,
            }

        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            return {}
