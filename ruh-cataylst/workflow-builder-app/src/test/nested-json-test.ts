// Test script for nested JSON object handling
import { processJsonObject } from '../lib/api';

// Sample nested JSON object
const sampleNestedJson = {
  time: "30seconds",
  objective: "educational",
  audience: "professionals",
  gender: "neutral",
  tone: "Technical",
  speakers: "expert",
  advanced_settings: {
    background_music: true,
    captions: {
      enabled: true,
      language: "en-US",
      style: {
        font: "Arial",
        size: 14,
        color: "#FFFFFF"
      }
    },
    transitions: ["fade", "dissolve", "wipe"]
  }
};

// Test the processJsonObject function
console.log("Testing processJsonObject with sample nested JSON:");
const result = processJsonObject(sampleNestedJson, "keywords", "MCP_Script_Generator_script_generate-1747733356496");
console.log(JSON.stringify(result, null, 2));

// Expected output structure:
/*
{
  "field": "keywords",
  "type": "object",
  "transition_id": "MCP_Script_Generator_script_generate-1747733356496",
  "properties": [
    {
      "field": "time",
      "type": "string",
      "transition_id": "MCP_Script_Generator_script_generate-1747733356496"
    },
    {
      "field": "objective",
      "type": "string",
      "transition_id": "MCP_Script_Generator_script_generate-1747733356496"
    },
    {
      "field": "audience",
      "type": "string",
      "transition_id": "MCP_Script_Generator_script_generate-1747733356496"
    },
    {
      "field": "gender",
      "type": "string",
      "transition_id": "MCP_Script_Generator_script_generate-1747733356496"
    },
    {
      "field": "tone",
      "type": "string",
      "transition_id": "MCP_Script_Generator_script_generate-1747733356496"
    },
    {
      "field": "speakers",
      "type": "string",
      "transition_id": "MCP_Script_Generator_script_generate-1747733356496"
    },
    {
      "field": "advanced_settings",
      "type": "object",
      "transition_id": "MCP_Script_Generator_script_generate-1747733356496",
      "properties": [
        {
          "field": "background_music",
          "type": "boolean",
          "transition_id": "MCP_Script_Generator_script_generate-1747733356496"
        },
        {
          "field": "captions",
          "type": "object",
          "transition_id": "MCP_Script_Generator_script_generate-1747733356496",
          "properties": [
            {
              "field": "enabled",
              "type": "boolean",
              "transition_id": "MCP_Script_Generator_script_generate-1747733356496"
            },
            {
              "field": "language",
              "type": "string",
              "transition_id": "MCP_Script_Generator_script_generate-1747733356496"
            },
            {
              "field": "style",
              "type": "object",
              "transition_id": "MCP_Script_Generator_script_generate-1747733356496",
              "properties": [
                {
                  "field": "font",
                  "type": "string",
                  "transition_id": "MCP_Script_Generator_script_generate-1747733356496"
                },
                {
                  "field": "size",
                  "type": "number",
                  "transition_id": "MCP_Script_Generator_script_generate-1747733356496"
                },
                {
                  "field": "color",
                  "type": "string",
                  "transition_id": "MCP_Script_Generator_script_generate-1747733356496"
                }
              ]
            }
          ]
        },
        {
          "field": "transitions",
          "type": "array",
          "transition_id": "MCP_Script_Generator_script_generate-1747733356496"
        }
      ]
    }
  ]
}
*/
