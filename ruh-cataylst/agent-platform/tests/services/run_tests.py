#!/usr/bin/env python3
"""
Simple test runner for AgentGroupService unit tests.

This script runs the unit tests without requiring the full application dependencies.
"""

import sys
import os
import asyncio
from unittest.mock import Mock, patch

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# Mock the problematic imports before importing the test module
sys.modules['aiokafka'] = Mock()
sys.modules['app.kafka_client.consumer'] = Mock()
sys.modules['app.main'] = Mock()

# Now we can import our test module
try:
    from tests.services.test_agent_group_service import TestAgentGroupService
    print("✅ Successfully imported test module")
except ImportError as e:
    print(f"❌ Failed to import test module: {e}")
    sys.exit(1)


async def run_single_test():
    """Run a single test to verify the test setup works."""
    print("\n🧪 Running sample test...")
    
    # Create test instance
    test_instance = TestAgentGroupService()
    
    # Create mock fixtures
    mock_settings = Mock()
    mock_settings.gateway = Mock()
    mock_settings.gateway.api_url = "https://test-api.example.com"
    mock_settings.gateway.api_key = "test-api-key"
    
    mock_http_client = Mock()
    
    # Test the initialization
    try:
        with patch('app.services.agent_group_service.get_settings', return_value=mock_settings), \
             patch('app.services.agent_group_service.HttpRequestHelper', return_value=mock_http_client):
            
            from app.services.agent_group_service import AgentGroupService
            service = AgentGroupService()
            
            # Verify the service was created
            assert service.settings == mock_settings
            assert hasattr(service, 'logger')
            
            print("✅ Service initialization test passed")
            
    except Exception as e:
        print(f"❌ Service initialization test failed: {e}")
        return False
    
    # Test a simple async method
    try:
        service.http_request_service = mock_http_client
        mock_http_client.get.return_value = {"group": {"id": "test-123", "name": "Test Group"}}
        
        result = await service.fetch_group_details("test-123")
        
        assert result is not None
        assert result["id"] == "test-123"
        assert result["name"] == "Test Group"
        
        print("✅ Async method test passed")
        
    except Exception as e:
        print(f"❌ Async method test failed: {e}")
        return False
    
    return True


def main():
    """Main test runner function."""
    print("🚀 Starting AgentGroupService Unit Test Runner")
    print("=" * 50)
    
    # Run the sample test
    success = asyncio.run(run_single_test())
    
    if success:
        print("\n🎉 All sample tests passed!")
        print("\n📋 Test Summary:")
        print("- Service initialization: ✅")
        print("- Async method execution: ✅")
        print("- Mock setup: ✅")
        print("\n💡 To run the full test suite, use:")
        print("   python3 -m pytest tests/services/test_agent_group_service.py -v")
        print("\n📚 For more information, see tests/services/README.md")
    else:
        print("\n❌ Some tests failed. Check the error messages above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
