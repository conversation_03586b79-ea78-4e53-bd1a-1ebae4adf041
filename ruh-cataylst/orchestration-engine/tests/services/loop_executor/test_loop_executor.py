import pytest
from unittest.mock import Mock, AsyncMock
from app.services.loop_executor.loop_executor import <PERSON><PERSON>xecutor
from app.services.loop_executor.loop_state_manager import LoopStateManager
from app.services.loop_executor.loop_aggregator import LoopAggregator


class TestLoopExecutor:
    """
    Test suite for LoopExecutor class.
    Tests loop execution, state management, and result aggregation.
    """

    @pytest.fixture
    def mock_state_manager(self):
        """Mock WorkflowStateManager for testing."""
        mock = Mock()
        mock.workflow_id = "test-workflow-123"
        mock.store_loop_state = Mock()
        mock.save_workflow_state = AsyncMock()
        return mock

    @pytest.fixture
    def mock_workflow_utils(self):
        """Mock WorkflowUtils for testing."""
        return Mock()

    @pytest.fixture
    def mock_result_callback(self):
        """Mock result callback for testing."""
        return Mock()

    @pytest.fixture
    def mock_transition_handler(self):
        """Mock TransitionHandler for testing."""
        return Mock()

    @pytest.fixture
    def sample_transitions(self):
        """Sample transitions for testing."""
        return {
            "transition_1": {"id": "transition_1", "type": "tool"},
            "transition_2": {"id": "transition_2", "type": "tool"},
            "transition_3": {"id": "transition_3", "type": "tool"},
        }

    @pytest.fixture
    def sample_nodes(self):
        """Sample nodes for testing."""
        return {
            "node_1": {"id": "node_1", "type": "tool"},
            "node_2": {"id": "node_2", "type": "tool"},
        }

    @pytest.fixture
    def loop_executor(
        self,
        mock_state_manager,
        mock_workflow_utils,
        mock_result_callback,
        mock_transition_handler,
        sample_transitions,
        sample_nodes,
    ):
        """Create LoopExecutor instance for testing."""
        return LoopExecutor(
            state_manager=mock_state_manager,
            workflow_utils=mock_workflow_utils,
            result_callback=mock_result_callback,
            transitions_by_id=sample_transitions,
            nodes=sample_nodes,
            transition_handler=mock_transition_handler,
            user_id="test-user-123",
        )

    @pytest.fixture
    def valid_loop_config(self):
        """Valid loop configuration for testing."""
        return {
            "loop_type": "context_independent",
            "iteration_source": {"type": "list", "data": ["item1", "item2", "item3"]},
            "loop_body_transitions": ["transition_1", "transition_2"],
            "aggregation_config": {"type": "list"},
            "concurrency": {"enabled": False},
        }

    def test_loop_executor_initialization(self, loop_executor):
        """Test LoopExecutor initialization."""
        assert loop_executor.state_manager is not None
        assert loop_executor.workflow_utils is not None
        assert loop_executor.result_callback is not None
        assert loop_executor.transitions_by_id is not None
        assert loop_executor.nodes is not None
        assert loop_executor.transition_handler is not None
        assert loop_executor.user_id == "test-user-123"
        assert isinstance(loop_executor.aggregator, LoopAggregator)

    def test_parse_loop_config_valid(self, loop_executor, valid_loop_config):
        """Test parsing valid loop configuration."""
        parsed = loop_executor.parse_loop_config(valid_loop_config)

        assert parsed["loop_type"] == "context_independent"
        assert parsed["iteration_source"]["type"] == "list"
        assert parsed["loop_body_transitions"] == ["transition_1", "transition_2"]
        assert parsed["aggregation_config"]["type"] == "list"
        assert parsed["concurrency"]["enabled"] is False

    def test_parse_loop_config_missing(self, loop_executor):
        """Test parsing with missing loop configuration."""
        with pytest.raises(ValueError, match="Loop configuration is required"):
            loop_executor.parse_loop_config(None)

    def test_validate_loop_config_valid(self, loop_executor, valid_loop_config):
        """Test validation of valid loop configuration."""
        parsed = loop_executor.parse_loop_config(valid_loop_config)
        # Should not raise exception
        loop_executor.validate_loop_config(parsed)

    def test_validate_loop_config_invalid_loop_type(
        self, loop_executor, valid_loop_config
    ):
        """Test validation with invalid loop type."""
        valid_loop_config["loop_type"] = "invalid_type"
        parsed = loop_executor.parse_loop_config(valid_loop_config)

        with pytest.raises(ValueError, match="Invalid loop_type"):
            loop_executor.validate_loop_config(parsed)

    def test_validate_loop_config_missing_transitions(
        self, loop_executor, valid_loop_config
    ):
        """Test validation with missing required fields for new architecture."""
        # Remove required field for new schema validation
        if "iteration_source" in valid_loop_config:
            del valid_loop_config["iteration_source"]

        parsed = loop_executor.parse_loop_config(valid_loop_config)

        with pytest.raises(ValueError, match="iteration_source"):
            loop_executor.validate_loop_config(parsed)

    def test_prepare_iteration_data_list(self, loop_executor, valid_loop_config):
        """Test preparing iteration data from list source."""
        parsed = loop_executor.parse_loop_config(valid_loop_config)
        loop_executor.current_loop_config = parsed

        loop_executor.prepare_iteration_data()

        assert len(loop_executor.current_iteration_data) == 3
        assert loop_executor.current_iteration_data[0] == (0, "item1")
        assert loop_executor.current_iteration_data[1] == (1, "item2")
        assert loop_executor.current_iteration_data[2] == (2, "item3")

    def test_prepare_iteration_data_range(self, loop_executor):
        """Test preparing iteration data from range source."""
        config = {
            "loop_type": "context_independent",
            "iteration_source": {
                "type": "range",
                "data": {"start": 0, "stop": 5, "step": 1},
            },
            "loop_body_transitions": ["transition_1"],
            "aggregation_config": {"type": "list"},
        }

        parsed = loop_executor.parse_loop_config(config)
        loop_executor.current_loop_config = parsed

        loop_executor.prepare_iteration_data()

        assert len(loop_executor.current_iteration_data) == 5
        assert loop_executor.current_iteration_data[0] == (0, 0)
        assert loop_executor.current_iteration_data[4] == (4, 4)

    @pytest.mark.asyncio
    async def test_initialize_loop_state(self, loop_executor, valid_loop_config):
        """Test loop state initialization."""
        parsed = loop_executor.parse_loop_config(valid_loop_config)
        loop_executor.current_loop_config = parsed
        loop_executor.prepare_iteration_data()

        await loop_executor.initialize_loop_state("test-transition-123")

        assert loop_executor.loop_state_manager is not None
        assert isinstance(loop_executor.loop_state_manager, LoopStateManager)
        assert loop_executor.loop_context["transition_id"] == "test-transition-123"
        assert loop_executor.loop_context["total_iterations"] == 3

        # Verify state manager was called
        loop_executor.state_manager.store_loop_state.assert_called_once()

    def test_create_iteration_context_independent(
        self, loop_executor, valid_loop_config
    ):
        """Test creating iteration context for context-independent loop."""
        parsed = loop_executor.parse_loop_config(valid_loop_config)
        loop_executor.current_loop_config = parsed
        loop_executor.loop_context = {"total_iterations": 3}

        context = loop_executor.create_iteration_context(1, "test_item")

        assert context["current_item"] == "test_item"
        assert context["iteration_index"] == 1
        assert context["total_iterations"] == 3
        assert "previous_results" not in context

    def test_create_iteration_context_preserving(
        self, loop_executor, valid_loop_config
    ):
        """Test creating iteration context for context-preserving loop."""
        valid_loop_config["loop_type"] = "context_preserving"
        parsed = loop_executor.parse_loop_config(valid_loop_config)
        loop_executor.current_loop_config = parsed
        loop_executor.loop_context = {"total_iterations": 3}
        loop_executor.iteration_results = {0: "result_0"}

        context = loop_executor.create_iteration_context(1, "test_item")

        assert context["current_item"] == "test_item"
        assert context["iteration_index"] == 1
        assert context["total_iterations"] == 3
        assert context["previous_results"] == ["result_0"]

    def test_aggregate_results_list(self, loop_executor, valid_loop_config):
        """Test result aggregation with list strategy."""
        parsed = loop_executor.parse_loop_config(valid_loop_config)
        loop_executor.current_loop_config = parsed
        loop_executor.iteration_results = {
            0: {"result": "test_0"},
            1: {"result": "test_1"},
            2: {"result": "test_2"},
        }

        result = loop_executor.aggregate_results()

        assert isinstance(result, list)
        assert len(result) == 3
        assert result[0] == {"result": "test_0"}

    def test_aggregate_results_object(self, loop_executor, valid_loop_config):
        """Test result aggregation with object strategy."""
        valid_loop_config["aggregation_config"]["type"] = "object"
        parsed = loop_executor.parse_loop_config(valid_loop_config)
        loop_executor.current_loop_config = parsed
        loop_executor.iteration_results = {
            0: {"result": "test_0"},
            1: {"result": "test_1"},
        }

        result = loop_executor.aggregate_results()

        assert isinstance(result, list)
        assert len(result) == 1
        assert isinstance(result[0], dict)
        assert result[0]["count"] == 2

    @pytest.mark.asyncio
    async def test_execute_single_iteration(self, loop_executor, valid_loop_config):
        """Test executing a single iteration."""
        parsed = loop_executor.parse_loop_config(valid_loop_config)
        loop_executor.current_loop_config = parsed
        loop_executor.loop_context = {"total_iterations": 3}

        # Mock loop state manager
        mock_loop_state_manager = Mock()
        loop_executor.loop_state_manager = mock_loop_state_manager

        result = await loop_executor.execute_single_iteration(0, "test_item")

        assert result["iteration_index"] == 0
        assert result["iteration_item"] == "test_item"
        assert result["status"] == "completed"

        # Verify state manager interactions
        mock_loop_state_manager.set_iteration_status.assert_called()
        mock_loop_state_manager.store_iteration_context.assert_called()
        mock_loop_state_manager.store_iteration_result.assert_called()

    @pytest.mark.asyncio
    async def test_finalize_loop_execution(self, loop_executor, valid_loop_config):
        """Test loop execution finalization."""
        parsed = loop_executor.parse_loop_config(valid_loop_config)
        loop_executor.current_loop_config = parsed
        loop_executor.loop_context = {"completed_iterations": 3, "failed_iterations": 0}

        # Mock loop state manager
        mock_loop_state_manager = Mock()
        mock_loop_state_manager.loop_id = "test-loop-123"
        mock_loop_state_manager.transition_id = "test-transition-123"
        mock_loop_state_manager.backup_loop_state.return_value = {"test": "data"}
        loop_executor.loop_state_manager = mock_loop_state_manager

        await loop_executor.finalize_loop_execution()

        # Verify state manager interactions
        mock_loop_state_manager.set_loop_execution_state.assert_called_with("completed")
        loop_executor.state_manager.store_loop_state.assert_called()
        loop_executor.state_manager.save_workflow_state.assert_called_once()

    def test_reset_loop_state(self, loop_executor):
        """Test resetting loop state."""
        # Set some state
        loop_executor.current_loop_config = {"test": "config"}
        loop_executor.current_iteration_data = ["data"]
        loop_executor.iteration_results = {0: "result"}
        loop_executor.loop_context = {"test": "context"}
        loop_executor.loop_state_manager = Mock()

        loop_executor.reset_loop_state()

        assert loop_executor.current_loop_config is None
        assert loop_executor.current_iteration_data == []
        assert loop_executor.iteration_results == {}
        assert loop_executor.loop_context == {}
        assert loop_executor.loop_state_manager is None

    @pytest.mark.asyncio
    async def test_handle_iteration_error(self, loop_executor):
        """Test handling iteration-level errors."""
        loop_executor.loop_state_manager = Mock()

        error = ValueError("Test error")
        await loop_executor.handle_iteration_error(error, 1, "test_item")

        # Verify error result was stored
        assert 1 in loop_executor.iteration_results
        error_result = loop_executor.iteration_results[1]
        assert error_result["status"] in [
            "failed",
            "skipped",
        ]  # Could be either based on error classification
        assert error_result["error"] == "Test error"

        # Verify state manager was updated
        loop_executor.loop_state_manager.set_iteration_status.assert_called_with(
            1, "failed"
        )
        loop_executor.loop_state_manager.store_iteration_result.assert_called()

    @pytest.mark.asyncio
    async def test_handle_loop_error(self, loop_executor):
        """Test handling loop-level errors."""
        # Set some state
        loop_executor.current_loop_config = {"test": "config"}
        mock_state_manager = Mock()
        mock_state_manager.loop_context = {}  # Make it a dict so item assignment works
        loop_executor.loop_state_manager = mock_state_manager

        error = ValueError("Test loop error")
        await loop_executor.handle_loop_error(error, "test-transition-123")

        # Verify state was reset
        assert loop_executor.current_loop_config is None
        assert loop_executor.loop_state_manager is None
