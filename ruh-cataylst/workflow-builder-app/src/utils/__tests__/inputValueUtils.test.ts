import { getEffectiveInputValue, isValueEmpty, isValueModified } from '../inputValueUtils';
import { Node } from 'reactflow';
import { WorkflowNodeData, InputDefinition } from '@/types';

// Mock the store
jest.mock('@/store/mcpToolsStore', () => ({
  useComponentStateStore: {
    getState: () => ({
      getValue: jest.fn(() => ({}))
    })
  }
}));

describe('inputValueUtils', () => {
  const mockNode: Node<WorkflowNodeData> = {
    id: 'test-node',
    type: 'WorkflowNode',
    position: { x: 0, y: 0 },
    data: {
      label: 'Test Node',
      type: 'ConditionalNode',
      originalType: 'ConditionalNode',
      definition: {
        name: 'ConditionalNode',
        display_name: 'Switch-Case Router',
        description: 'Test component',
        category: 'Logic',
        icon: 'GitBranch',
        beta: false,
        inputs: [],
        outputs: [],
        is_valid: true,
        path: 'components.control_flow.conditionalNode'
      },
      config: {}
    }
  };

  const mockInputDef: InputDefinition = {
    name: 'test_input',
    display_name: 'Test Input',
    input_type: 'string',
    required: false,
    is_handle: false,
    is_list: false,
    real_time_refresh: false,
    advanced: false,
    value: 'default_value',
    options: null,
    visibility_rules: null,
    visibility_logic: 'OR'
  };

  describe('getEffectiveInputValue', () => {
    it('should return configured value when present', () => {
      const nodeWithConfig = {
        ...mockNode,
        data: {
          ...mockNode.data,
          config: {
            test_input: 'configured_value'
          }
        }
      };

      const result = getEffectiveInputValue(nodeWithConfig, mockInputDef);
      expect(result).toBe('configured_value');
    });

    it('should return default value when config value is undefined', () => {
      const nodeWithoutConfig = {
        ...mockNode,
        data: {
          ...mockNode.data,
          config: {}
        }
      };

      const result = getEffectiveInputValue(nodeWithoutConfig, mockInputDef);
      expect(result).toBe('default_value');
    });

    it('should return type fallback when no default value is set', () => {
      const inputDefWithoutDefault = {
        ...mockInputDef,
        value: undefined
      };

      const result = getEffectiveInputValue(mockNode, inputDefWithoutDefault);
      expect(result).toBe(''); // string type fallback
    });

    it('should handle boolean input types correctly', () => {
      const boolInputDef = {
        ...mockInputDef,
        input_type: 'bool',
        value: undefined
      };

      const result = getEffectiveInputValue(mockNode, boolInputDef);
      expect(result).toBe(false); // boolean type fallback
    });

    it('should handle number input types correctly', () => {
      const intInputDef = {
        ...mockInputDef,
        input_type: 'int',
        value: undefined
      };

      const result = getEffectiveInputValue(mockNode, intInputDef);
      expect(result).toBe(0); // number type fallback
    });
  });

  describe('isValueEmpty', () => {
    it('should return true for undefined values', () => {
      expect(isValueEmpty(undefined, mockInputDef)).toBe(true);
    });

    it('should return true for null values', () => {
      expect(isValueEmpty(null, mockInputDef)).toBe(true);
    });

    it('should return true for empty strings', () => {
      expect(isValueEmpty('', mockInputDef)).toBe(true);
    });

    it('should return false for non-empty strings', () => {
      expect(isValueEmpty('test', mockInputDef)).toBe(false);
    });

    it('should return false for boolean values', () => {
      const boolInputDef = { ...mockInputDef, input_type: 'bool' };
      expect(isValueEmpty(false, boolInputDef)).toBe(false);
      expect(isValueEmpty(true, boolInputDef)).toBe(false);
    });
  });

  describe('isValueModified', () => {
    it('should return false when value equals default', () => {
      const nodeWithDefaultValue = {
        ...mockNode,
        data: {
          ...mockNode.data,
          config: {
            test_input: 'default_value'
          }
        }
      };

      const result = isValueModified(nodeWithDefaultValue, mockInputDef);
      expect(result).toBe(false);
    });

    it('should return true when value differs from default', () => {
      const nodeWithModifiedValue = {
        ...mockNode,
        data: {
          ...mockNode.data,
          config: {
            test_input: 'modified_value'
          }
        }
      };

      const result = isValueModified(nodeWithModifiedValue, mockInputDef);
      expect(result).toBe(true);
    });
  });
});
