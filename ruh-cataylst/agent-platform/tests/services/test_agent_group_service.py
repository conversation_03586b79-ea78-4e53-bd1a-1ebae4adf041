"""
Unit tests for AgentGroupService.

This module contains comprehensive unit tests for the AgentGroupService class,
covering all methods and various scenarios including success cases, error handling,
and edge cases.
"""

from unittest.mock import Mock, patch

import pytest

from app.helper.api_call import AuthType, HttpRequestHelper
from app.services.agent_group_service import AgentGroupService
from app.shared.config.base import ApiGatewayConfig, Settings


class TestAgentGroupService:
    """Test class for AgentGroupService."""

    @pytest.fixture
    def mock_settings(self):
        """Create mock settings for testing."""
        mock_settings = Mock(spec=Settings)
        mock_settings.gateway = Mock(spec=ApiGatewayConfig)
        mock_settings.gateway.api_url = "https://test-api.example.com"
        mock_settings.gateway.api_key = "test-api-key"
        return mock_settings

    @pytest.fixture
    def mock_http_client(self):
        """Create mock HTTP client for testing."""
        return Mock(spec=HttpRequestHelper)

    @pytest.fixture
    def agent_group_service(self, mock_settings, mock_http_client):
        """Create AgentGroupService instance with mocked dependencies."""
        with patch(
            "app.services.agent_group_service.get_settings", return_value=mock_settings
        ), patch(
            "app.services.agent_group_service.HttpRequestHelper",
            return_value=mock_http_client,
        ):
            service = AgentGroupService()
            service.http_request_service = mock_http_client
            return service

    @pytest.fixture
    def sample_group_data(self):
        """Sample group data for testing."""
        return {
            "id": "test-group-123",
            "name": "Test Group",
            "description": "A test group for unit testing",
            "team_type": "round_robin",
            "max_messages": 15,
            "termination_keywords": ["TERMINATE", "STOP"],
            "selector_model": "gpt-4",
            "is_active": True,
        }

    @pytest.fixture
    def sample_agents_data(self):
        """Sample agents data for testing."""
        return [
            {
                "id": "agent-1",
                "name": "Test Agent 1",
                "description": "First test agent",
                "system_message": "You are a helpful assistant.",
                "model_provider": "openai",
                "model_name": "gpt-4",
                "model_api_key": "test-key-1",
                "workflow_ids": ["workflow-1"],
                "mcp_server_ids": ["mcp-1"],
                "agent_category": "general",
                "visibility": "private",
                "tags": {"type": "test"},
            },
            {
                "id": "agent-2",
                "name": "Test Agent 2",
                "description": "Second test agent",
                "system_message": "You are a code assistant.",
                "model_provider": "openai",
                "model_name": "gpt-4o-mini",
                "model_api_key": "test-key-2",
                "workflow_ids": [],
                "mcp_server_ids": [],
                "agent_category": "coding",
                "visibility": "public",
                "tags": {},
            },
        ]

    def test_init_success(self, mock_settings):
        """Test successful initialization of AgentGroupService."""
        with patch(
            "app.services.agent_group_service.get_settings", return_value=mock_settings
        ), patch(
            "app.services.agent_group_service.HttpRequestHelper"
        ) as mock_http_helper:

            service = AgentGroupService()

            # Verify settings are loaded
            assert service.settings == mock_settings

            # Verify HTTP client is initialized with correct parameters
            mock_http_helper.assert_called_once_with(
                mock_settings.gateway.api_url,
                auth_token=mock_settings.gateway.api_key,
                auth_type=AuthType.API_KEY,
                api_key_name="X-Agent-Platform-Auth-Key",
            )

            # Verify logger is set
            assert service.logger is not None

    @pytest.mark.asyncio
    async def test_fetch_group_details_success(
        self, agent_group_service, mock_http_client, sample_group_data
    ):
        """Test successful fetching of group details."""
        # Setup mock response
        mock_response = {"group": sample_group_data}
        mock_http_client.get.return_value = mock_response

        # Call the method
        result = await agent_group_service.fetch_group_details("test-group-123")

        # Verify the result
        assert result == sample_group_data

        # Verify HTTP client was called correctly
        mock_http_client.get.assert_called_once_with(
            endpoint="agent-groups/test-group-123"
        )

    @pytest.mark.asyncio
    async def test_fetch_group_details_not_found(
        self, agent_group_service, mock_http_client
    ):
        """Test fetching group details when group is not found."""
        # Setup mock response with no group
        mock_response = {}
        mock_http_client.get.return_value = mock_response

        # Call the method
        result = await agent_group_service.fetch_group_details("nonexistent-group")

        # Verify the result is None
        assert result is None

        # Verify HTTP client was called correctly
        mock_http_client.get.assert_called_once_with(
            endpoint="agent-groups/nonexistent-group"
        )

    @pytest.mark.asyncio
    async def test_fetch_group_details_empty_response(
        self, agent_group_service, mock_http_client
    ):
        """Test fetching group details with empty response."""
        # Setup mock response as None
        mock_http_client.get.return_value = None

        # Call the method
        result = await agent_group_service.fetch_group_details("test-group-123")

        # Verify the result is None
        assert result is None

    @pytest.mark.asyncio
    async def test_fetch_group_details_exception(
        self, agent_group_service, mock_http_client
    ):
        """Test fetching group details when an exception occurs."""
        # Setup mock to raise exception
        mock_http_client.get.side_effect = Exception("Network error")

        # Call the method
        result = await agent_group_service.fetch_group_details("test-group-123")

        # Verify the result is None
        assert result is None

    @pytest.mark.asyncio
    async def test_fetch_group_agents_success(
        self, agent_group_service, mock_http_client, sample_agents_data
    ):
        """Test successful fetching of group agents."""
        # Setup mock response
        mock_response = {"agents": sample_agents_data}
        mock_http_client.get.return_value = mock_response

        # Call the method
        result = await agent_group_service.fetch_group_agents("test-group-123")

        # Verify the result
        assert result == sample_agents_data
        assert len(result) == 2

        # Verify HTTP client was called correctly
        mock_http_client.get.assert_called_once_with(
            endpoint="agent-groups/test-group-123/agents"
        )

    @pytest.mark.asyncio
    async def test_fetch_group_agents_not_found(
        self, agent_group_service, mock_http_client
    ):
        """Test fetching group agents when no agents are found."""
        # Setup mock response with no agents
        mock_response = {}
        mock_http_client.get.return_value = mock_response

        # Call the method
        result = await agent_group_service.fetch_group_agents("test-group-123")

        # Verify the result is empty list
        assert result == []

    @pytest.mark.asyncio
    async def test_fetch_group_agents_empty_response(
        self, agent_group_service, mock_http_client
    ):
        """Test fetching group agents with empty response."""
        # Setup mock response as None
        mock_http_client.get.return_value = None

        # Call the method
        result = await agent_group_service.fetch_group_agents("test-group-123")

        # Verify the result is empty list
        assert result == []

    @pytest.mark.asyncio
    async def test_fetch_group_agents_exception(
        self, agent_group_service, mock_http_client
    ):
        """Test fetching group agents when an exception occurs."""
        # Setup mock to raise exception
        mock_http_client.get.side_effect = Exception("API error")

        # Call the method
        result = await agent_group_service.fetch_group_agents("test-group-123")

        # Verify the result is empty list
        assert result == []

    @pytest.mark.asyncio
    async def test_get_group_chat_config_success(
        self, agent_group_service, sample_group_data
    ):
        """Test successful retrieval of group chat configuration."""
        # Mock fetch_group_details to return sample data
        with patch.object(
            agent_group_service, "fetch_group_details", return_value=sample_group_data
        ):
            # Call the method
            result = await agent_group_service.get_group_chat_config("test-group-123")

            # Verify the result structure
            assert result is not None
            assert result["team_type"] == "round_robin"
            assert result["max_messages"] == 15
            assert result["termination_keywords"] == ["TERMINATE", "STOP"]
            assert result["selector_model"] == "gpt-4"
            assert result["group_description"] == "A test group for unit testing"
            assert result["group_name"] == "Test Group"

    @pytest.mark.asyncio
    async def test_get_group_chat_config_with_defaults(self, agent_group_service):
        """Test group chat configuration with default values."""
        # Mock fetch_group_details to return minimal data
        minimal_group_data = {"id": "test-group-123"}

        with patch.object(
            agent_group_service, "fetch_group_details", return_value=minimal_group_data
        ):
            # Call the method
            result = await agent_group_service.get_group_chat_config("test-group-123")

            # Verify default values are used
            assert result is not None
            assert result["team_type"] == "round_robin"  # default
            assert result["max_messages"] == 10  # default
            assert result["termination_keywords"] == ["TERMINATE"]  # default
            assert result["selector_model"] is None  # not provided
            assert result["group_description"] == ""  # default
            assert result["group_name"] == "Group-test-group-123"  # default format

    @pytest.mark.asyncio
    async def test_get_group_chat_config_group_not_found(self, agent_group_service):
        """Test group chat configuration when group is not found."""
        # Mock fetch_group_details to return None
        with patch.object(
            agent_group_service, "fetch_group_details", return_value=None
        ):
            # Call the method
            result = await agent_group_service.get_group_chat_config(
                "nonexistent-group"
            )

            # Verify the result is None
            assert result is None

    @pytest.mark.asyncio
    async def test_get_group_chat_config_exception(self, agent_group_service):
        """Test group chat configuration when an exception occurs."""
        # Mock fetch_group_details to raise exception
        with patch.object(
            agent_group_service, "fetch_group_details", side_effect=Exception("Error")
        ):
            # Call the method
            result = await agent_group_service.get_group_chat_config("test-group-123")

            # Verify the result is None
            assert result is None

    @pytest.mark.asyncio
    async def test_validate_group_for_chat_success(
        self, agent_group_service, sample_group_data, sample_agents_data
    ):
        """Test successful group validation for chat."""
        # Mock both methods to return valid data
        with patch.object(
            agent_group_service, "fetch_group_details", return_value=sample_group_data
        ), patch.object(
            agent_group_service, "fetch_group_agents", return_value=sample_agents_data
        ):

            # Call the method
            result = await agent_group_service.validate_group_for_chat("test-group-123")

            # Verify the result is True
            assert result is True

    @pytest.mark.asyncio
    async def test_validate_group_for_chat_group_not_found(self, agent_group_service):
        """Test group validation when group is not found."""
        # Mock fetch_group_details to return None
        with patch.object(
            agent_group_service, "fetch_group_details", return_value=None
        ):

            # Call the method
            result = await agent_group_service.validate_group_for_chat(
                "nonexistent-group"
            )

            # Verify the result is False
            assert result is False

    @pytest.mark.asyncio
    async def test_validate_group_for_chat_no_agents(
        self, agent_group_service, sample_group_data
    ):
        """Test group validation when group has no agents."""
        # Mock methods to return group but no agents
        with patch.object(
            agent_group_service, "fetch_group_details", return_value=sample_group_data
        ), patch.object(agent_group_service, "fetch_group_agents", return_value=[]):

            # Call the method
            result = await agent_group_service.validate_group_for_chat("test-group-123")

            # Verify the result is False
            assert result is False

    @pytest.mark.asyncio
    async def test_validate_group_for_chat_inactive_group(
        self, agent_group_service, sample_agents_data
    ):
        """Test group validation when group is inactive."""
        # Create inactive group data
        inactive_group_data = {
            "id": "test-group-123",
            "name": "Inactive Group",
            "is_active": False,
        }

        # Mock methods to return inactive group and agents
        with patch.object(
            agent_group_service, "fetch_group_details", return_value=inactive_group_data
        ), patch.object(
            agent_group_service, "fetch_group_agents", return_value=sample_agents_data
        ):

            # Call the method
            result = await agent_group_service.validate_group_for_chat("test-group-123")

            # Verify the result is False
            assert result is False

    @pytest.mark.asyncio
    async def test_validate_group_for_chat_exception(self, agent_group_service):
        """Test group validation when an exception occurs."""
        # Mock fetch_group_details to raise exception
        with patch.object(
            agent_group_service, "fetch_group_details", side_effect=Exception("Error")
        ):

            # Call the method
            result = await agent_group_service.validate_group_for_chat("test-group-123")

            # Verify the result is False
            assert result is False

    @pytest.mark.asyncio
    async def test_get_group_agent_configs_success(
        self, agent_group_service, sample_agents_data
    ):
        """Test successful conversion of agent data to AgentConfig objects."""
        # Mock fetch_group_agents to return sample data
        with patch.object(
            agent_group_service, "fetch_group_agents", return_value=sample_agents_data
        ):

            # Call the method
            result = await agent_group_service.get_group_agent_configs("test-group-123")

            # Verify the result
            assert len(result) == 2

            # Verify first agent config
            first_agent = result[0]
            assert first_agent.id == "agent-1"
            assert first_agent.name == "Test Agent 1"
            assert first_agent.description == "First test agent"
            assert first_agent.system_message == "You are a helpful assistant."
            assert first_agent.model_provider == "openai"
            assert first_agent.model_name == "gpt-4"
            assert first_agent.workflow_ids == ["workflow-1"]
            assert first_agent.mcp_server_ids == ["mcp-1"]
            assert first_agent.agent_category == "general"
            assert first_agent.visibility == "private"
            assert first_agent.tags == {"type": "test"}

            # Verify second agent config
            second_agent = result[1]
            assert second_agent.id == "agent-2"
            assert second_agent.name == "Test Agent 2"
            assert second_agent.description == "Second test agent"
            assert second_agent.system_message == "You are a code assistant."
            assert second_agent.model_provider == "openai"
            assert second_agent.model_name == "gpt-4o-mini"
            assert second_agent.workflow_ids == []
            assert second_agent.mcp_server_ids == []
            assert second_agent.agent_category == "coding"
            assert second_agent.visibility == "public"
            assert second_agent.tags == {}

    @pytest.mark.asyncio
    async def test_get_group_agent_configs_with_defaults(self, agent_group_service):
        """Test agent config conversion with minimal data and defaults."""
        # Create minimal agent data
        minimal_agents_data = [{"id": "minimal-agent", "name": "Minimal Agent"}]

        # Mock fetch_group_agents to return minimal data
        with patch.object(
            agent_group_service, "fetch_group_agents", return_value=minimal_agents_data
        ):

            # Call the method
            result = await agent_group_service.get_group_agent_configs("test-group-123")

            # Verify the result
            assert len(result) == 1

            # Verify agent config with defaults
            agent = result[0]
            assert agent.id == "minimal-agent"
            assert agent.name == "Minimal Agent"
            assert agent.description == ""  # default
            assert agent.system_message == "You are a helpful AI assistant."  # default
            assert agent.model_provider == "openai"  # default
            assert agent.model_name == "gpt-4o-mini"  # default
            assert agent.workflow_ids == []  # default
            assert agent.mcp_server_ids == []  # default
            assert agent.visibility == "private"  # default
            assert agent.tags == {}  # default

    @pytest.mark.asyncio
    async def test_get_group_agent_configs_no_agents(self, agent_group_service):
        """Test agent config conversion when no agents are found."""
        # Mock fetch_group_agents to return empty list
        with patch.object(agent_group_service, "fetch_group_agents", return_value=[]):

            # Call the method
            result = await agent_group_service.get_group_agent_configs("test-group-123")

            # Verify the result is empty list
            assert result == []

    @pytest.mark.asyncio
    async def test_get_group_agent_configs_invalid_agent_data(
        self, agent_group_service
    ):
        """Test agent config conversion with invalid agent data."""
        # Create invalid agent data (missing required fields)
        invalid_agents_data = [
            {"id": "valid-agent", "name": "Valid Agent"},
            {
                # Missing required 'id' field
                "name": "Invalid Agent"
            },
            {"id": "another-valid-agent", "name": "Another Valid Agent"},
        ]

        # Mock fetch_group_agents to return mixed data
        with patch.object(
            agent_group_service, "fetch_group_agents", return_value=invalid_agents_data
        ):

            # Call the method
            result = await agent_group_service.get_group_agent_configs("test-group-123")

            # Verify only valid agents are returned (invalid ones are skipped)
            assert len(result) == 2
            assert result[0].id == "valid-agent"
            assert result[1].id == "another-valid-agent"

    @pytest.mark.asyncio
    async def test_get_group_agent_configs_exception(self, agent_group_service):
        """Test agent config conversion when an exception occurs."""
        # Mock fetch_group_agents to raise exception
        with patch.object(
            agent_group_service, "fetch_group_agents", side_effect=Exception("Error")
        ):

            # Call the method
            result = await agent_group_service.get_group_agent_configs("test-group-123")

            # Verify the result is empty list
            assert result == []
            assert result == []
