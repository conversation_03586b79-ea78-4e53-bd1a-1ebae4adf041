import asyncio
import logging
from app.helper.redis_client import RedisClient
from app.helper.session_manager import SessionManager
from app.autogen_service.chat_processor import ChatProcessor
from app.autogen_service.agent_factory import AgentFactory
from app.schemas.api import AgentConfig, ChatType

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_single_agent_chat():
    # Initialize components
    redis_client = RedisClient(host="localhost", port=6379)
    session_manager = SessionManager(redis_client)
    agent_factory = AgentFactory()
    chat_processor = ChatProcessor(session_manager, agent_factory)

    # Create agent config
    agent_config = AgentConfig(
        name="helpful_assistant",
        description="A helpful AI assistant",
        system_prompt="You are a helpful AI assistant that provides clear and concise answers.",
        model="gpt-4",
        tools=[],
    )

    try:
        # Initialize chat session
        session_id = await chat_processor.initialize_chat(
            agent_configs=[agent_config], chat_type=ChatType.SINGLE
        )
        logger.info(f"Created single agent chat session: {session_id}")

        # Test messages
        test_messages = [
            "Hello! Can you help me with Python programming?",
            "What's the best way to handle exceptions in Python?",
            "Thank you for your help!",
        ]

        # Process messages
        for message in test_messages:
            logger.info(f"\nUser: {message}")
            async for response in chat_processor.process_chat_stream(
                session_id, message
            ):
                logger.info(f"Assistant: {response}")

        # Get chat history
        history = await chat_processor.get_session_history(session_id)
        logger.info(f"\nChat history: {history}")

        # End session
        await chat_processor.end_chat_session(session_id)
        logger.info("Chat session ended")

    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise


async def test_group_chat():
    # Initialize components
    redis_client = RedisClient(host="localhost", port=6379)
    session_manager = SessionManager(redis_client)
    agent_factory = AgentFactory()
    chat_processor = ChatProcessor(session_manager, agent_factory)

    # Create agent configs
    agent_configs = [
        AgentConfig(
            name="python_expert",
            description="Python programming expert",
            system_prompt="You are a Python programming expert.",
            model="gpt-4",
            tools=[],
        ),
        AgentConfig(
            name="code_reviewer",
            description="Code review specialist",
            system_prompt="You are a code review specialist.",
            model="gpt-4",
            tools=[],
        ),
    ]

    try:
        # Initialize chat session
        session_id = await chat_processor.initialize_chat(
            agent_configs=agent_configs, chat_type=ChatType.GROUP
        )
        logger.info(f"Created group chat session: {session_id}")

        # Test message
        message = """
        Can you help me review this Python code?
        
        def process_data(data):
            result = []
            for item in data:
                result.append(item * 2)
            return result
        """

        logger.info(f"\nUser: {message}")
        async for response in chat_processor.process_chat_stream(session_id, message):
            logger.info(f"Agents: {response}")

        # Get chat history
        history = await chat_processor.get_session_history(session_id)
        logger.info(f"\nChat history: {history}")

        # End session
        await chat_processor.end_chat_session(session_id)
        logger.info("Chat session ended")

    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise


async def main():
    logger.info("Starting single agent chat test...")
    await test_single_agent_chat()

    logger.info("\nStarting group chat test...")
    await test_group_chat()


if __name__ == "__main__":
    asyncio.run(main())
