import { InputDefinition } from "@/types";

/**
 * Formats a value for display based on its type
 * @param value The value to format
 * @param inputType The type of the input
 * @returns Formatted value as a string
 */
export function formatValueForDisplay(value: any, inputType: string): string {
  if (value === undefined || value === null) {
    return "";
  }

  switch (inputType) {
    case "object":
    case "dict":
    case "json":
    case "list":
    case "array":
      return typeof value === "object"
        ? JSON.stringify(value, null, 2)
        : String(value);
    case "bool":
      return value ? "True" : "False";
    default:
      return String(value);
  }
}

/**
 * Parses a string value to the appropriate type
 * @param value The string value to parse
 * @param inputType The type of the input
 * @returns Parsed value in the appropriate type
 */
export function parseInputValue(value: string, inputType: string): any {
  switch (inputType) {
    case "int":
      return parseInt(value, 10);
    case "float":
    case "number":
      return parseFloat(value);
    case "bool":
      return value === "true" || value === "True" || value === "1";
    case "object":
    case "dict":
    case "json":
    case "list":
    case "array":
      try {
        return JSON.parse(value);
      } catch (e) {
        return value; // Return as string if parsing fails
      }
    default:
      return value;
  }
}

/**
 * Gets the appropriate input type for an HTML input element
 * @param inputType The type of the input from the definition
 * @returns HTML input type
 */
export function getHtmlInputType(inputType: string): string {
  switch (inputType) {
    case "int":
    case "float":
    case "number":
      return "number";
    case "password":
      return "password";
    default:
      return "text";
  }
}

/**
 * Gets the config value for an input, with special handling for different node types
 * @param inputName The name of the input
 * @param defaultValue Default value if not found
 * @param config The node configuration
 * @param nodeType The type of the node
 * @param nodeId The ID of the node
 * @param getStoreValue Function to get a value from the store
 * @returns The config value
 */
export function getConfigValue(
  inputName: string,
  defaultValue: any,
  config: Record<string, any>,
  nodeType: string,
  nodeId: string,
  getStoreValue?: (nodeId: string, key: string, defaultValue: any) => any
): any {
  // Special handling for MCP Tools component
  if (nodeType === "MCPToolsComponent" && getStoreValue) {
    if (inputName === "sse_url") {
      return getStoreValue(nodeId, "sse_url", config?.sse_url || defaultValue);
    }
    if (inputName === "mcp_type") {
      return getStoreValue(nodeId, "mcp_type", config?.mcp_type || defaultValue);
    }
    if (inputName === "selected_tool_name") {
      return getStoreValue(
        nodeId,
        "selected_tool_name",
        config?.selected_tool_name || defaultValue
      );
    }
    // For connection_status, check if it's in the raw inputs
    if (inputName === "connection_status") {
      const rawInputs = config?.inputs || [];
      const statusInput = rawInputs.find((input: any) => input.name === "connection_status");
      if (statusInput && statusInput.value) {
        return statusInput.value;
      }
      if (config?.connection_status) {
        return config.connection_status;
      }
    }
  }

  // Default behavior for other components and inputs
  return config?.[inputName] ?? defaultValue;
}
