"""
Loop Aggregator - Result aggregation and formatting for loop execution.

This module provides various aggregation strategies for loop results,
handling the dual output system and different aggregation types.
"""

from typing import Dict, List, Any, Optional, Union
from app.utils.enhanced_logger import get_logger

logger = get_logger("LoopAggregator")


class LoopAggregator:
    """
    Handles aggregation of loop iteration results into final outputs.
    
    This class provides various aggregation strategies and handles the
    dual output system where loops have both iteration outputs (current_item)
    and final aggregated outputs (final_results).
    """
    
    def __init__(self, logger=None):
        """
        Initialize the loop aggregator.
        
        Args:
            logger: Optional logger instance
        """
        self.logger = logger or get_logger("LoopAggregator")
    
    async def aggregate_results(
        self,
        iteration_results: List[Dict[str, Any]],
        aggregation_config: Dict[str, Any],
        loop_metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Aggregate iteration results according to configuration.
        
        Args:
            iteration_results: List of results from all iterations
            aggregation_config: Configuration for aggregation strategy
            loop_metadata: Optional metadata about the loop execution
            
        Returns:
            Aggregated results with dual output format
        """
        aggregation_type = aggregation_config.get("aggregation_type", "collect_all")
        include_metadata = aggregation_config.get("include_metadata", False)
        
        self.logger.info(f"🔄 Aggregating {len(iteration_results)} results using strategy: {aggregation_type}")
        
        # Apply aggregation strategy
        if aggregation_type == "collect_all":
            final_results = await self._collect_all(iteration_results)
        elif aggregation_type == "collect_successful":
            final_results = await self._collect_successful(iteration_results)
        elif aggregation_type == "collect_values":
            final_results = await self._collect_values(iteration_results)
        elif aggregation_type == "count_only":
            final_results = await self._count_only(iteration_results)
        elif aggregation_type == "first_successful":
            final_results = await self._first_successful(iteration_results)
        elif aggregation_type == "last_successful":
            final_results = await self._last_successful(iteration_results)
        elif aggregation_type == "merge_objects":
            final_results = await self._merge_objects(iteration_results)
        elif aggregation_type == "flatten_arrays":
            final_results = await self._flatten_arrays(iteration_results)
        else:
            self.logger.warning(f"Unknown aggregation type: {aggregation_type}, using collect_all")
            final_results = await self._collect_all(iteration_results)
        
        # Build final output with dual output system
        output = {
            "output_type": "loop_completion",
            "final_results": final_results,
            "current_item": None,  # No current item on completion
            "aggregation_metadata": {
                "aggregation_type": aggregation_type,
                "total_iterations": len(iteration_results),
                "successful_iterations": len([r for r in iteration_results if self._is_successful_result(r)]),
                "failed_iterations": len([r for r in iteration_results if not self._is_successful_result(r)])
            }
        }
        
        # Add loop metadata if requested
        if include_metadata and loop_metadata:
            output["loop_metadata"] = loop_metadata
        
        return output
    
    async def _collect_all(self, results: List[Dict[str, Any]]) -> List[Any]:
        """Collect all results including failed ones."""
        return [self._extract_result_value(result) for result in results]
    
    async def _collect_successful(self, results: List[Dict[str, Any]]) -> List[Any]:
        """Collect only successful results."""
        successful_results = [
            self._extract_result_value(result)
            for result in results
            if self._is_successful_result(result)
        ]
        return successful_results
    
    async def _collect_values(self, results: List[Dict[str, Any]]) -> List[Any]:
        """Collect only the core values, stripping metadata."""
        values = []
        for result in results:
            if self._is_successful_result(result):
                value = self._extract_core_value(result)
                if value is not None:
                    values.append(value)
        return values
    
    async def _count_only(self, results: List[Dict[str, Any]]) -> Dict[str, int]:
        """Return only counts of different result types."""
        successful = len([r for r in results if self._is_successful_result(r)])
        failed = len([r for r in results if not self._is_successful_result(r)])
        
        return {
            "total": len(results),
            "successful": successful,
            "failed": failed
        }
    
    async def _first_successful(self, results: List[Dict[str, Any]]) -> Any:
        """Return the first successful result."""
        for result in results:
            if self._is_successful_result(result):
                return self._extract_result_value(result)
        return None
    
    async def _last_successful(self, results: List[Dict[str, Any]]) -> Any:
        """Return the last successful result."""
        for result in reversed(results):
            if self._is_successful_result(result):
                return self._extract_result_value(result)
        return None
    
    async def _merge_objects(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Merge all successful results into a single object."""
        merged = {}
        
        for result in results:
            if self._is_successful_result(result):
                value = self._extract_result_value(result)
                if isinstance(value, dict):
                    merged.update(value)
                else:
                    # Use iteration index as key if value is not a dict
                    iteration_index = result.get("iteration_index", len(merged))
                    merged[f"iteration_{iteration_index}"] = value
        
        return merged
    
    async def _flatten_arrays(self, results: List[Dict[str, Any]]) -> List[Any]:
        """Flatten all array results into a single array."""
        flattened = []
        
        for result in results:
            if self._is_successful_result(result):
                value = self._extract_result_value(result)
                if isinstance(value, list):
                    flattened.extend(value)
                else:
                    flattened.append(value)
        
        return flattened
    
    def _is_successful_result(self, result: Dict[str, Any]) -> bool:
        """Check if a result represents a successful iteration."""
        status = result.get("status", "completed")
        return status not in ["failed", "error", "timeout"]
    
    def _extract_result_value(self, result: Dict[str, Any]) -> Any:
        """Extract the main result value from an iteration result."""
        # Try different possible result locations
        if "result" in result:
            return result["result"]
        elif "value" in result:
            return result["value"]
        elif "output" in result:
            return result["output"]
        elif "data" in result:
            return result["data"]
        else:
            # Return the whole result if no specific value field found
            return result
    
    def _extract_core_value(self, result: Dict[str, Any]) -> Any:
        """Extract the core value, stripping away metadata."""
        value = self._extract_result_value(result)
        
        # If the value is a dict with metadata, try to extract the core content
        if isinstance(value, dict):
            # Common patterns for core values
            for key in ["content", "value", "data", "result", "output"]:
                if key in value:
                    return value[key]
            
            # If no common pattern, check if it looks like metadata
            metadata_keys = {"status", "timestamp", "iteration_index", "metadata", "error"}
            value_keys = set(value.keys())
            
            # If most keys are metadata, try to find the actual content
            if len(metadata_keys.intersection(value_keys)) > len(value_keys) / 2:
                content_keys = value_keys - metadata_keys
                if len(content_keys) == 1:
                    return value[list(content_keys)[0]]
        
        return value
    
    async def format_current_item_output(
        self,
        iteration_result: Dict[str, Any],
        iteration_index: int,
        iteration_item: Any
    ) -> Dict[str, Any]:
        """
        Format output for current_item handle during iteration.
        
        This is used for the dual output system where current_item
        provides the result of the current iteration.
        """
        return {
            "output_type": "iteration_output",
            "current_item": self._extract_result_value(iteration_result),
            "final_results": None,  # Not available during iteration
            "iteration_metadata": {
                "iteration_index": iteration_index,
                "iteration_item": iteration_item,
                "status": iteration_result.get("status", "completed")
            }
        }
    
    async def format_error_result(
        self,
        error: Exception,
        iteration_index: int,
        iteration_item: Any,
        include_in_results: bool = True
    ) -> Dict[str, Any]:
        """
        Format an error result for aggregation.
        
        Args:
            error: The exception that occurred
            iteration_index: Index of the failed iteration
            iteration_item: Item that was being processed
            include_in_results: Whether to include this error in final results
            
        Returns:
            Formatted error result
        """
        error_result = {
            "iteration_index": iteration_index,
            "iteration_item": iteration_item,
            "error": str(error),
            "error_type": type(error).__name__,
            "status": "failed",
            "timestamp": self._get_current_timestamp()
        }
        
        if not include_in_results:
            error_result["excluded_from_results"] = True
        
        return error_result
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime
        return datetime.now().isoformat()
    
    async def validate_aggregation_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and normalize aggregation configuration.
        
        Args:
            config: Raw aggregation configuration
            
        Returns:
            Validated and normalized configuration
        """
        valid_types = {
            "collect_all", "collect_successful", "collect_values",
            "count_only", "first_successful", "last_successful",
            "merge_objects", "flatten_arrays"
        }
        
        aggregation_type = config.get("aggregation_type", "collect_all")
        if aggregation_type not in valid_types:
            self.logger.warning(f"Invalid aggregation type: {aggregation_type}, using collect_all")
            aggregation_type = "collect_all"
        
        return {
            "aggregation_type": aggregation_type,
            "include_metadata": config.get("include_metadata", False),
            "include_errors": config.get("include_errors", True),
            "preserve_order": config.get("preserve_order", True)
        }
