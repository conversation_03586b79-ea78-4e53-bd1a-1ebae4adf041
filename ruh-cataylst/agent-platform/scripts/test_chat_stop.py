#!/usr/bin/env python3
"""
Test script for the agent chat stop functionality.
This script demonstrates how to send chat stop requests via Kafka.
"""

import asyncio
import json
import os
import uuid

from typing import Dict, Any

from aiokafka import AI<PERSON>afkaProducer, AIOKafkaConsumer

# Kafka configuration
KAFKA_BOOTSTRAP_SERVERS = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9094")
KAFKA_AGENT_CHAT_STOP_TOPIC = os.getenv(
    "KAFKA_AGENT_CHAT_STOP_TOPIC", "agent_chat_stop_requests"
)
KAFKA_AGENT_RESPONSE_TOPIC = os.getenv(
    "KAFKA_AGENT_RESPONSE_TOPIC", "agent_chat_responses"
)


class ChatStopTester:
    """Test class for agent chat stop functionality."""

    def __init__(self):
        self.producer = None
        self.consumer = None

    async def init_kafka(self):
        """Initialize Kafka producer and consumer."""
        self.producer = AIOKafkaProducer(
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            value_serializer=lambda v: json.dumps(v).encode("utf-8"),
        )
        await self.producer.start()

        self.consumer = AIOKafkaConsumer(
            KAFKA_AGENT_RESPONSE_TOPIC,
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            group_id=f"chat_stop_test_{uuid.uuid4().hex[:8]}",
            auto_offset_reset="latest",
            value_deserializer=lambda m: json.loads(m.decode("utf-8")),
        )
        await self.consumer.start()

    async def cleanup_kafka(self):
        """Cleanup Kafka connections."""
        if self.producer:
            await self.producer.stop()
        if self.consumer:
            await self.consumer.stop()

    async def send_chat_stop_request(self, request_data: Dict[str, Any]) -> str:
        """
        Send a chat stop request via Kafka.

        Args:
            request_data: The chat stop request data

        Returns:
            The run_id of the request
        """
        run_id = str(uuid.uuid4())
        request_data["run_id"] = run_id

        print(f"Sending chat stop request: {json.dumps(request_data, indent=2)}")

        await self.producer.send(KAFKA_AGENT_CHAT_STOP_TOPIC, value=request_data)

        return run_id

    async def wait_for_response(self, run_id: str, timeout: int = 30) -> Dict[str, Any]:
        """
        Wait for a response with the given run_id.

        Args:
            run_id: The run_id to wait for
            timeout: Timeout in seconds

        Returns:
            The response data
        """
        print(f"Waiting for response with run_id: {run_id}")

        start_time = asyncio.get_event_loop().time()
        async for message in self.consumer:
            response = message.value

            if response.get("run_id") == run_id:
                print(f"Received response: {json.dumps(response, indent=2)}")
                return response

            # Check timeout
            if asyncio.get_event_loop().time() - start_time > timeout:
                raise TimeoutError(
                    f"Timeout waiting for response with run_id: {run_id}"
                )

        raise TimeoutError(f"No response received for run_id: {run_id}")

    async def test_stop_specific_session(self, session_id: str):
        """Test stopping a specific session."""
        print(f"\n=== Testing stop specific session: {session_id} ===")

        request_data = {
            "session_id": session_id,
            "user_id": "test_user_123",
            "stop_type": "session",
            "reason": "user_requested_stop",
        }

        run_id = await self.send_chat_stop_request(request_data)
        response = await self.wait_for_response(run_id)

        return response

    async def test_stop_user_sessions(self, user_id: str):
        """Test stopping all sessions for a user."""
        print(f"\n=== Testing stop all sessions for user: {user_id} ===")

        request_data = {
            "user_id": user_id,
            "stop_type": "user_all",
            "reason": "user_logout",
        }

        run_id = await self.send_chat_stop_request(request_data)
        response = await self.wait_for_response(run_id)

        return response

    async def test_force_stop_all(self):
        """Test force stopping all active sessions."""
        print(f"\n=== Testing force stop all sessions ===")

        request_data = {
            "user_id": "admin_user",
            "stop_type": "force_all",
            "reason": "system_maintenance",
        }

        run_id = await self.send_chat_stop_request(request_data)
        response = await self.wait_for_response(run_id)

        return response

    async def run_tests(self):
        """Run all chat stop tests."""
        try:
            await self.init_kafka()

            print("Starting chat stop functionality tests...")
            print(f"Kafka Bootstrap Servers: {KAFKA_BOOTSTRAP_SERVERS}")
            print(f"Chat Stop Topic: {KAFKA_AGENT_CHAT_STOP_TOPIC}")
            print(f"Response Topic: {KAFKA_AGENT_RESPONSE_TOPIC}")

            # Test 1: Stop specific session
            test_session_id = f"test_session_{uuid.uuid4().hex[:8]}"
            response1 = await self.test_stop_specific_session(test_session_id)

            # Test 2: Stop user sessions
            test_user_id = f"test_user_{uuid.uuid4().hex[:8]}"
            response2 = await self.test_stop_user_sessions(test_user_id)

            # Test 3: Force stop all (be careful with this in production!)
            # response3 = await self.test_force_stop_all()

            print("\n=== Test Results Summary ===")
            print(
                f"Test 1 (Stop specific session): {'PASSED' if response1.get('success') else 'FAILED'}"
            )
            print(
                f"Test 2 (Stop user sessions): {'PASSED' if response2.get('success') else 'FAILED'}"
            )
            # print(f"Test 3 (Force stop all): {'PASSED' if response3.get('success') else 'FAILED'}")

        except Exception as e:
            print(f"Test failed with error: {e}")
            raise
        finally:
            await self.cleanup_kafka()


async def main():
    """Main function to run the tests."""
    tester = ChatStopTester()
    await tester.run_tests()


if __name__ == "__main__":
    print("Agent Chat Stop Functionality Test")
    print("=" * 50)
    asyncio.run(main())
