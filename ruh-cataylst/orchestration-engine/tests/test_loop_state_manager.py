import pytest
import json
from app.core_.loop_state_manager import LoopStateManager


class TestLoopStateManager:
    """
    Test suite for LoopStateManager functionality.
    Tests state tracking, context management, and persistence operations.
    """

    @pytest.fixture
    def loop_state_manager(self):
        """Create a LoopStateManager instance for testing."""
        return LoopStateManager(
            loop_id="test-loop-123",
            transition_id="test-transition-456",
            workflow_id="test-workflow-789"
        )

    def test_initialization(self, loop_state_manager):
        """Test LoopStateManager initialization."""
        assert loop_state_manager.loop_id == "test-loop-123"
        assert loop_state_manager.transition_id == "test-transition-456"
        assert loop_state_manager.workflow_id == "test-workflow-789"
        assert loop_state_manager.current_iteration_index == 0
        assert loop_state_manager.total_iterations == 0
        assert loop_state_manager.loop_execution_state == "initialized"
        assert loop_state_manager._iteration_results == {}
        assert loop_state_manager._iteration_contexts == {}
        assert loop_state_manager._loop_metadata == {}

    def test_current_iteration_index_property(self, loop_state_manager):
        """Test current iteration index getter and setter."""
        assert loop_state_manager.current_iteration_index == 0
        
        loop_state_manager.current_iteration_index = 5
        assert loop_state_manager.current_iteration_index == 5

    def test_total_iterations_property(self, loop_state_manager):
        """Test total iterations getter and setter."""
        assert loop_state_manager.total_iterations == 0
        
        loop_state_manager.total_iterations = 10
        assert loop_state_manager.total_iterations == 10

    def test_loop_execution_state_property(self, loop_state_manager):
        """Test loop execution state getter and setter."""
        assert loop_state_manager.loop_execution_state == "initialized"
        
        loop_state_manager.set_loop_execution_state("running")
        assert loop_state_manager.loop_execution_state == "running"
        
        loop_state_manager.set_loop_execution_state("completed")
        assert loop_state_manager.loop_execution_state == "completed"

    def test_invalid_loop_execution_state(self, loop_state_manager):
        """Test setting invalid loop execution state."""
        with pytest.raises(ValueError, match="Invalid loop execution state: invalid_state"):
            loop_state_manager.set_loop_execution_state("invalid_state")

    def test_iteration_status_management(self, loop_state_manager):
        """Test iteration status getter and setter."""
        # Initially no status
        assert loop_state_manager.get_iteration_status(0) is None
        
        # Set status
        loop_state_manager.set_iteration_status(0, "running")
        assert loop_state_manager.get_iteration_status(0) == "running"
        
        # Update status
        loop_state_manager.set_iteration_status(0, "completed")
        assert loop_state_manager.get_iteration_status(0) == "completed"

    def test_invalid_iteration_status(self, loop_state_manager):
        """Test setting invalid iteration status."""
        with pytest.raises(ValueError, match="Invalid iteration status: invalid_status"):
            loop_state_manager.set_iteration_status(0, "invalid_status")

    def test_iteration_context_management(self, loop_state_manager):
        """Test iteration context storage and retrieval."""
        context = {"item": "test_item", "index": 0, "metadata": {"key": "value"}}
        
        # Store context
        loop_state_manager.store_iteration_context(0, context)
        
        # Retrieve context
        retrieved_context = loop_state_manager.retrieve_iteration_context(0)
        assert retrieved_context == context
        
        # Test non-existent context
        assert loop_state_manager.retrieve_iteration_context(999) is None

    def test_previous_contexts_retrieval(self, loop_state_manager):
        """Test retrieval of previous iteration contexts."""
        # Store multiple contexts
        contexts = [
            {"item": "item_0", "index": 0},
            {"item": "item_1", "index": 1},
            {"item": "item_2", "index": 2},
        ]
        
        for i, context in enumerate(contexts):
            loop_state_manager.store_iteration_context(i, context)
        
        # Get previous contexts for iteration 2
        previous = loop_state_manager.get_previous_contexts(2)
        assert len(previous) == 2
        assert previous[0] == contexts[0]
        assert previous[1] == contexts[1]
        
        # Get previous contexts for iteration 0 (should be empty)
        previous = loop_state_manager.get_previous_contexts(0)
        assert previous == []

    def test_iteration_result_management(self, loop_state_manager):
        """Test iteration result storage and retrieval."""
        result = {"status": "completed", "output": "test_output", "duration": 1.5}
        
        # Store result
        loop_state_manager.store_iteration_result(0, result)
        
        # Retrieve result
        retrieved_result = loop_state_manager.retrieve_iteration_result(0)
        assert retrieved_result == result
        
        # Test non-existent result
        assert loop_state_manager.retrieve_iteration_result(999) is None

    def test_all_results_retrieval(self, loop_state_manager):
        """Test retrieval of all iteration results."""
        results = {
            0: {"output": "result_0"},
            2: {"output": "result_2"},
            1: {"output": "result_1"},
        }
        
        for index, result in results.items():
            loop_state_manager.store_iteration_result(index, result)
        
        all_results = loop_state_manager.get_all_results()
        assert all_results == results

    def test_ordered_results_retrieval(self, loop_state_manager):
        """Test retrieval of ordered iteration results."""
        results = {
            2: {"output": "result_2"},
            0: {"output": "result_0"},
            1: {"output": "result_1"},
        }
        
        for index, result in results.items():
            loop_state_manager.store_iteration_result(index, result)
        
        ordered_results = loop_state_manager.get_ordered_results()
        expected_order = [
            {"output": "result_0"},
            {"output": "result_1"},
            {"output": "result_2"},
        ]
        assert ordered_results == expected_order

    def test_loop_metadata_management(self, loop_state_manager):
        """Test loop metadata storage and retrieval."""
        # Set metadata
        loop_state_manager.set_loop_metadata("start_time", "2024-01-01T00:00:00Z")
        loop_state_manager.set_loop_metadata("config", {"type": "test"})
        
        # Get metadata
        assert loop_state_manager.get_loop_metadata("start_time") == "2024-01-01T00:00:00Z"
        assert loop_state_manager.get_loop_metadata("config") == {"type": "test"}
        assert loop_state_manager.get_loop_metadata("nonexistent") is None

    def test_initialize_loop_state(self, loop_state_manager):
        """Test loop state initialization."""
        metadata = {"test_key": "test_value"}
        
        loop_state_manager.initialize_loop_state(5, metadata)
        
        assert loop_state_manager.current_iteration_index == 0
        assert loop_state_manager.total_iterations == 5
        assert loop_state_manager.loop_execution_state == "initialized"
        assert loop_state_manager.get_loop_metadata("test_key") == "test_value"
        
        # Check that all iterations are initialized as pending
        for i in range(5):
            assert loop_state_manager.get_iteration_status(i) == "pending"

    def test_reset_loop_state(self, loop_state_manager):
        """Test loop state reset."""
        # Set some state
        loop_state_manager.current_iteration_index = 3
        loop_state_manager.total_iterations = 10
        loop_state_manager.set_loop_execution_state("running")
        loop_state_manager.set_iteration_status(0, "completed")
        loop_state_manager.store_iteration_result(0, {"result": "test"})
        loop_state_manager.store_iteration_context(0, {"context": "test"})
        loop_state_manager.set_loop_metadata("key", "value")
        
        # Reset state
        loop_state_manager.reset_loop_state()
        
        # Verify reset
        assert loop_state_manager.current_iteration_index == 0
        assert loop_state_manager.total_iterations == 0
        assert loop_state_manager.loop_execution_state == "initialized"
        assert loop_state_manager.get_iteration_status(0) is None
        assert loop_state_manager.retrieve_iteration_result(0) is None
        assert loop_state_manager.retrieve_iteration_context(0) is None
        assert loop_state_manager.get_loop_metadata("key") is None

    def test_backup_and_restore_loop_state(self, loop_state_manager):
        """Test loop state backup and restore."""
        # Set up some state
        loop_state_manager.current_iteration_index = 2
        loop_state_manager.total_iterations = 5
        loop_state_manager.set_loop_execution_state("running")
        loop_state_manager.set_iteration_status(0, "completed")
        loop_state_manager.set_iteration_status(1, "completed")
        loop_state_manager.set_iteration_status(2, "running")
        loop_state_manager.store_iteration_result(0, {"result": "test_0"})
        loop_state_manager.store_iteration_result(1, {"result": "test_1"})
        loop_state_manager.store_iteration_context(0, {"context": "test_0"})
        loop_state_manager.set_loop_metadata("test_key", "test_value")
        
        # Create backup
        backup = loop_state_manager.backup_loop_state()
        
        # Modify state
        loop_state_manager.reset_loop_state()
        
        # Restore from backup
        loop_state_manager.restore_loop_state(backup)
        
        # Verify restoration
        assert loop_state_manager.current_iteration_index == 2
        assert loop_state_manager.total_iterations == 5
        assert loop_state_manager.loop_execution_state == "running"
        assert loop_state_manager.get_iteration_status(0) == "completed"
        assert loop_state_manager.get_iteration_status(1) == "completed"
        assert loop_state_manager.get_iteration_status(2) == "running"
        assert loop_state_manager.retrieve_iteration_result(0) == {"result": "test_0"}
        assert loop_state_manager.retrieve_iteration_result(1) == {"result": "test_1"}
        assert loop_state_manager.retrieve_iteration_context(0) == {"context": "test_0"}
        assert loop_state_manager.get_loop_metadata("test_key") == "test_value"

    def test_execution_summary(self, loop_state_manager):
        """Test execution summary generation."""
        # Set up state
        loop_state_manager.initialize_loop_state(5)
        loop_state_manager.current_iteration_index = 3
        loop_state_manager.set_loop_execution_state("running")
        loop_state_manager.set_iteration_status(0, "completed")
        loop_state_manager.set_iteration_status(1, "completed")
        loop_state_manager.set_iteration_status(2, "running")
        loop_state_manager.set_iteration_status(3, "failed")
        loop_state_manager.set_iteration_status(4, "pending")
        
        summary = loop_state_manager.get_execution_summary()
        
        assert summary["loop_id"] == "test-loop-123"
        assert summary["transition_id"] == "test-transition-456"
        assert summary["workflow_id"] == "test-workflow-789"
        assert summary["execution_state"] == "running"
        assert summary["current_iteration"] == 3
        assert summary["total_iterations"] == 5
        assert summary["completed_iterations"] == 2
        assert summary["failed_iterations"] == 1
        assert summary["running_iterations"] == 1
        assert summary["pending_iterations"] == 1
        assert summary["progress_percentage"] == 40.0  # 2/5 * 100

    def test_is_loop_complete(self, loop_state_manager):
        """Test loop completion detection."""
        # Empty state - not complete
        assert not loop_state_manager.is_loop_complete()
        
        # Initialize with some iterations
        loop_state_manager.initialize_loop_state(3)
        assert not loop_state_manager.is_loop_complete()  # All pending
        
        # Complete some iterations
        loop_state_manager.set_iteration_status(0, "completed")
        loop_state_manager.set_iteration_status(1, "failed")
        assert not loop_state_manager.is_loop_complete()  # One still pending
        
        # Complete all iterations
        loop_state_manager.set_iteration_status(2, "completed")
        assert loop_state_manager.is_loop_complete()

    def test_serialize_and_deserialize_state(self, loop_state_manager):
        """Test state serialization and deserialization."""
        # Set up some state
        loop_state_manager.current_iteration_index = 2
        loop_state_manager.total_iterations = 3
        loop_state_manager.set_loop_execution_state("running")
        loop_state_manager.set_iteration_status(0, "completed")
        loop_state_manager.store_iteration_result(0, {"result": "test"})
        loop_state_manager.set_loop_metadata("key", "value")
        
        # Serialize
        serialized = loop_state_manager.serialize_state()
        assert isinstance(serialized, str)
        
        # Create new instance and deserialize
        new_manager = LoopStateManager("new-loop", "new-transition", "new-workflow")
        new_manager.deserialize_state(serialized)
        
        # Verify deserialization
        assert new_manager.current_iteration_index == 2
        assert new_manager.total_iterations == 3
        assert new_manager.loop_execution_state == "running"
        assert new_manager.get_iteration_status(0) == "completed"
        assert new_manager.retrieve_iteration_result(0) == {"result": "test"}
        assert new_manager.get_loop_metadata("key") == "value"
