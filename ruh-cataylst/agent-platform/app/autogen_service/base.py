import json
from typing import List
from autogen_core import (
    FunctionCall,
    MessageContext,
    RoutedAgent,
    TopicId,
    message_handler,
)

from autogen_core.models import (
    AssistantMessage,
    ChatCompletionClient,
    FunctionExecutionResult,
    FunctionExecutionResultMessage,
    SystemMessage,
)
from autogen_core.tools import Tool
from api.models import UserTask, AgentResponse


class AIAgent(RoutedAgent):
    """
    Base AI agent with routing and tool execution capabilities
    """

    def __init__(
        self,
        description: str,
        system_message: SystemMessage,
        model_client: ChatCompletionClient,
        tools: List[Tool],
        delegate_tools: List[Tool],
        agent_topic_type: str,
        user_topic_type: str,
    ) -> None:
        super().__init__(description)
        self._system_message = system_message
        self._model_client = model_client
        self._tools = dict([(tool.name, tool) for tool in tools])
        self._tool_schema = [tool.schema for tool in tools]
        self._delegate_tools = dict([(tool.name, tool) for tool in delegate_tools])
        self._delegate_tool_schema = [tool.schema for tool in delegate_tools]
        self._agent_topic_type = agent_topic_type
        self._user_topic_type = user_topic_type
        print(
            f"🤖 Initialized {agent_topic_type} with {len(tools)} tools and {len(delegate_tools)} delegate tools"
        )

    @message_handler
    async def handle_task(self, message: UserTask, ctx: MessageContext) -> None:
        """
        Handle incoming user tasks

        Parameters:
        -----------
        message: UserTask
            The user task message
        ctx: MessageContext
            The message context
        """
        print(
            f"📥 {self._agent_topic_type} received task: {message.context[-1].content}"
        )

        llm_result = await self._model_client.create(
            messages=[self._system_message] + message.context,
            tools=self._tool_schema + self._delegate_tool_schema,
            cancellation_token=ctx.cancellation_token,
        )

        if isinstance(llm_result.content, str):
            print(
                f"💬 {self._agent_topic_type} responding directly: {llm_result.content[:100]}..."
            )
            message.context.append(
                AssistantMessage(content=llm_result.content, source=self.id.type)
            )
            await self.publish_message(
                AgentResponse(
                    context=message.context, reply_to_topic_type=self._agent_topic_type
                ),
                topic_id=TopicId(self._user_topic_type, source=self.id.key),
            )
            return

        print(f"🔄 {self._agent_topic_type} processing function calls")
        while isinstance(llm_result.content, list) and all(
            isinstance(m, FunctionCall) for m in llm_result.content
        ):
            tool_call_results = []
            delegate_targets = []

            for call in llm_result.content:
                print(f"🛠️ {self._agent_topic_type} attempting to use tool: {call.name}")
                arguments = json.loads(call.arguments)
                if call.name in self._tools:
                    print(f"⚙️ {self._agent_topic_type} executing tool: {call.name}")
                    result = await self._tools[call.name].run_json(
                        arguments, ctx.cancellation_token
                    )
                    result_as_str = self._tools[call.name].return_value_as_string(
                        result
                    )
                    print(f"Result received from tool call: {result_as_str[:100]}...")
                    tool_call_results.append(
                        FunctionExecutionResult(call_id=call.id, content=result_as_str)
                    )
                elif call.name in self._delegate_tools:
                    print(f"👉 {self._agent_topic_type} delegating using: {call.name}")
                    result = await self._delegate_tools[call.name].run_json(
                        arguments, ctx.cancellation_token
                    )
                    topic_type = self._delegate_tools[call.name].return_value_as_string(
                        result
                    )
                    print(f"🔀 {self._agent_topic_type} delegating to: {topic_type}")
                    delegate_messages = list(message.context) + [
                        AssistantMessage(content=[call], source=self.id.type),
                        FunctionExecutionResultMessage(
                            content=[
                                FunctionExecutionResult(
                                    call_id=call.id,
                                    content=f"Transferred to {topic_type}",
                                )
                            ]
                        ),
                    ]
                    delegate_targets.append(
                        (topic_type, UserTask(context=delegate_messages))
                    )
                else:
                    print(
                        f"❌ {self._agent_topic_type} encountered unknown tool: {call.name}"
                    )
                    raise ValueError(f"Unknown tool: {call.name}")

            if delegate_targets:
                print(
                    f"📤 {self._agent_topic_type} initiating delegation to {[target[0] for target in delegate_targets]}"
                )
                handoff_message = f"I'll connect you with our {delegate_targets[0][0].replace('Agent', '').lower()} specialist who can better assist you."
                message.context.append(
                    AssistantMessage(content=handoff_message, source=self.id.type)
                )
                for topic_type, task in delegate_targets:
                    print(f"✈️ {self._agent_topic_type} delegating task to {topic_type}")
                    await self.publish_message(
                        task, topic_id=TopicId(topic_type, source=self.id.key)
                    )
                print(f"✅ {self._agent_topic_type} completed delegation")
                return

            if tool_call_results:
                print(
                    f"🔄 {self._agent_topic_type} processing tool results and continuing conversation"
                )
                message.context.extend(
                    [
                        AssistantMessage(
                            content=llm_result.content, source=self.id.type
                        ),
                        FunctionExecutionResultMessage(content=tool_call_results),
                    ]
                )
                llm_result = await self._model_client.create(
                    messages=[self._system_message] + message.context,
                    tools=self._tool_schema + self._delegate_tool_schema,
                    cancellation_token=ctx.cancellation_token,
                )
            else:
                return

        print(f"📤 {self._agent_topic_type} sending final response")
        message.context.append(
            AssistantMessage(content=llm_result.content, source=self.id.type)
        )
        await self.publish_message(
            AgentResponse(
                context=message.context, reply_to_topic_type=self._agent_topic_type
            ),
            topic_id=TopicId(self._user_topic_type, source=self.id.key),
        )
