import asyncio

import pytest
from autogen_agentchat.agents import Assistant<PERSON><PERSON>
from autogen_ext.models.openai import OpenAIChatCompletionClient

from app.tools.mcp_tool_loader import Mcp<PERSON><PERSON><PERSON>oa<PERSON>, load_mcp_tool_adapters_from_schema

# Example schema for a running MCP server (update sse_url as needed)
EXAMPLE_SCHEMA = {"mcp": {"sse_url": "http://localhost:8931/sse"}}


@pytest.mark.asyncio
async def test_mcp_tool_adapter_integration():
    # Load all tool adapters from the MCP server
    tool_adapters = await load_mcp_tool_adapters_from_schema(EXAMPLE_SCHEMA)
    assert tool_adapters, "No tool adapters found on the MCP server."

    # Create a model client (ensure your OpenAI key/model is configured)
    model_client = OpenAIChatCompletionClient(model="gpt-4.1-nano")

    # Create an agent with the loaded tools
    agent = AssistantAgent(
        name="test_agent",
        model_client=model_client,
        tools=tool_adapters,
        reflect_on_tool_use=True,
    )

    # Run a simple task (update the task as appropriate for your MCP server/tools)
    result = await agent.run(task="Say hello using any available tool.")
    print("Agent result:", result)

    # Optionally, assert on result content
    assert result is not None


@pytest.mark.asyncio
async def test_complex_schema_handling():
    """Test the MCP tool loader with complex schema structures including $defs and $ref."""

    # Sample complex schema data similar to what you provided
    sample_tools_data = [
        {
            "name": "script_generate",
            "description": "Provide topic and keyword to generator Script",
            "input_schema": {
                "$defs": {
                    "Keywords": {
                        "properties": {
                            "time": {
                                "anyOf": [{"type": "string"}, {"type": "null"}],
                                "default": None,
                                "title": "Time",
                            },
                            "objective": {
                                "anyOf": [{"type": "string"}, {"type": "null"}],
                                "default": None,
                                "title": "Objective",
                            },
                            "audience": {
                                "anyOf": [{"type": "string"}, {"type": "null"}],
                                "default": None,
                                "title": "Audience",
                            },
                            "gender": {
                                "anyOf": [{"type": "string"}, {"type": "null"}],
                                "default": None,
                                "title": "Gender",
                            },
                            "tone": {
                                "anyOf": [{"type": "string"}, {"type": "null"}],
                                "default": None,
                                "title": "Tone",
                            },
                            "speakers": {
                                "anyOf": [
                                    {"items": {"type": "string"}, "type": "array"},
                                    {"type": "null"},
                                ],
                                "default": None,
                                "title": "Speakers",
                            },
                        },
                        "title": "Keywords",
                        "type": "object",
                    },
                    "ScriptType": {
                        "enum": ["VIDEO", "TOPIC", "SCRIPT", "BLOG", "AI"],
                        "title": "ScriptType",
                        "type": "string",
                    },
                    "VideoType": {
                        "enum": ["SHORT", "LONG"],
                        "title": "VideoType",
                        "type": "string",
                    },
                },
                "properties": {
                    "topic": {"title": "Topic", "type": "string"},
                    "script_type": {"$ref": "#/$defs/ScriptType", "default": "TOPIC"},
                    "keywords": {"$ref": "#/$defs/Keywords"},
                    "video_type": {"$ref": "#/$defs/VideoType", "default": "SHORT"},
                    "link": {
                        "anyOf": [
                            {
                                "format": "uri",
                                "maxLength": 2083,
                                "minLength": 1,
                                "type": "string",
                            },
                            {"type": "null"},
                        ],
                        "default": None,
                        "title": "Link",
                    },
                },
                "required": ["topic"],
                "title": "GenerateScriptInput",
                "type": "object",
            },
            "output_schema": None,
            "annotations": None,
        },
        {
            "name": "research",
            "description": "Research for the given topic",
            "input_schema": {
                "properties": {"topic": {"title": "Topic", "type": "string"}},
                "required": ["topic"],
                "title": "ResearchInput",
                "type": "object",
            },
            "output_schema": None,
            "annotations": None,
        },
    ]

    # Create MCP metadata structure
    mcp_metadata = {
        "id": "test-mcp",
        "name": "Test MCP",
        "url": "https://test-mcp-server.com/sse",
        "mcp_tools_config": {"tools": sample_tools_data},
    }

    # Initialize the MCP tool loader
    loader = McpToolLoader()

    # Test creating tools from the complex metadata
    try:
        tools = await loader.create_mcp_tool_from_metadata(mcp_metadata)

        # Verify we got the expected number of tools
        assert len(tools) == 2, f"Expected 2 tools, got {len(tools)}"

        # Verify tool names
        tool_names = [tool.name for tool in tools]
        assert "script_generate" in tool_names, "script_generate tool not found"
        assert "research" in tool_names, "research tool not found"

        # Test the script_generate tool schema handling
        script_tool = next(tool for tool in tools if tool.name == "script_generate")

        # Verify the tool has the correct description
        assert (
            script_tool.description == "Provide topic and keyword to generator Script"
        )

        # Test that the args model was created correctly
        args_model = script_tool.args_type
        assert args_model is not None, "Args model should not be None"

        # Check that required fields are handled correctly
        model_fields = args_model.model_fields
        assert "topic" in model_fields, "topic field should be present"

        # Check that optional fields with defaults are handled
        assert "script_type" in model_fields, "script_type field should be present"
        assert "video_type" in model_fields, "video_type field should be present"

        print("✅ Complex schema handling test passed!")
        print(f"Created tools: {[tool.name for tool in tools]}")

        return True

    except Exception as e:
        print(f"❌ Complex schema handling test failed: {e}")
        raise e
