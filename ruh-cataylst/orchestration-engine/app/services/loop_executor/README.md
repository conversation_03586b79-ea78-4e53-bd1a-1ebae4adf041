# Loop Executor - Dynamic Loop Execution System

## Overview

The Loop Executor provides comprehensive loop execution capabilities for the orchestration engine, supporting dynamic configuration, multiple iteration sources, and seamless integration with the main workflow state management system.

## Key Features

- **Dynamic Configuration**: Supports both iteration lists and number ranges
- **Flexible Execution**: Sequential and parallel execution modes
- **Dual Output System**: Provides both `current_item` (iteration output) and `final_results` (exit output)
- **State Integration**: Full integration with main WorkflowStateManager
- **Error Handling**: Comprehensive error handling with retry logic
- **Exit Detection**: Automatic detection of loop completion via exit transitions

## Architecture

### Core Components

1. **LoopExecutor**: Main orchestrator for loop execution
2. **LoopStateManager**: State management and iteration tracking
3. **LoopBodyExecutor**: Execution of loop body transition chains
4. **LoopParameterResolver**: Dynamic parameter resolution
5. **LoopAggregator**: Result aggregation and formatting

### Integration Points

- **TransitionHandler**: Called via `execute_tool` method
- **WorkflowStateManager**: Integrated for state persistence and dependency tracking
- **Main Execution Loop**: Follows same patterns as main orchestration engine

## Configuration Schema

### Iteration List Source
```json
{
  "iteration_source": {
    "source_type": "iteration_list",
    "iteration_list": ["item1", "item2", "item3"],
    "batch_size": 1
  }
}
```

### Number Range Source
```json
{
  "iteration_source": {
    "source_type": "number_range",
    "number_range": {
      "start": 1,
      "end": 5
    },
    "step": 1
  }
}
```

### Execution Settings
```json
{
  "iteration_settings": {
    "parallel_execution": false,
    "max_concurrent": 3,
    "preserve_order": true,
    "iteration_timeout": 60
  }
}
```

### Result Aggregation
```json
{
  "result_aggregation": {
    "aggregation_type": "collect_all",
    "include_metadata": false
  }
}
```

### Loop Body Configuration
```json
{
  "loop_body_configuration": {
    "entry_transitions": ["transition-entry-1"],
    "exit_transitions": ["transition-exit-1"],
    "chain_completion_detection": "explicit_exit_transitions"
  }
}
```

## Usage Examples

### Basic Loop Execution

```python
from app.services.loop_executor import LoopExecutor

# Initialize loop executor
loop_executor = LoopExecutor(
    state_manager=state_manager,
    workflow_utils=workflow_utils,
    result_callback=result_callback,
    transitions_by_id=transitions_by_id,
    nodes=nodes,
    transition_handler=transition_handler
)

# Execute loop
result = await loop_executor.execute_tool(
    tool_parameters=tool_params,
    loop_config=loop_config,
    transition_id=transition_id,
    input_data=input_data,
    output_routing=output_routing
)
```

### Dynamic Parameter Resolution

The system automatically resolves parameters from:
- Tool parameters in transition schema
- Input data from connected transitions
- Pre-built loop configurations
- Default values

### State Management Integration

The loop executor integrates with the main state manager:
- Tracks pending/completed/waiting transitions
- Manages dependency maps for loop body
- Persists loop state for recovery
- Handles iteration-specific transition tracking

## Dual Output System

Loops provide two types of outputs:

1. **current_item**: Available during iteration execution
   - Contains the result of the current iteration
   - Used for connections within the loop body

2. **final_results**: Available on loop completion
   - Contains aggregated results from all iterations
   - Used for connections after the loop

## Error Handling

### Iteration Error Strategies
- `continue`: Continue with next iteration
- `retry_once`: Retry failed iteration once
- `fail_fast`: Stop loop on first error

### Error Inclusion
- `include_errors: true`: Include error results in final output
- `include_errors: false`: Exclude error results

## Aggregation Types

- `collect_all`: Collect all results including errors
- `collect_successful`: Collect only successful results
- `collect_values`: Extract core values without metadata
- `count_only`: Return only counts
- `first_successful`: Return first successful result
- `last_successful`: Return last successful result
- `merge_objects`: Merge all results into single object
- `flatten_arrays`: Flatten array results

## Loop Body Execution

### Entry Transitions
- Define where loop body execution starts
- Receive the current iteration item
- Initialize the transition chain

### Exit Transitions
- Mark the end of loop body execution
- Trigger completion of current iteration
- Move to next iteration or complete loop

### Chain Execution
1. Start with entry transitions
2. Execute transitions based on dependencies
3. Track completion and route to next transitions
4. Detect exit transitions and complete iteration

## Integration with Main Engine

### Transition Handler Integration
```python
# In TransitionHandler
if execution_type == "loop":
    loop_executor = LoopExecutor(...)
    result = await loop_executor.execute_tool(
        tool_parameters=tool_params,
        transition_id=transition_id,
        input_data=input_data
    )
```

### State Manager Integration
```python
# Loop state is stored in main state manager
state_manager.store_loop_state(loop_id, transition_id, loop_data)

# Iteration transitions are tracked in pending/completed sets
state_manager.pending_transitions.add(iteration_transition_id)
state_manager.completed_transitions.add(iteration_transition_id)
```

## Testing

The system includes comprehensive tests for:
- Dynamic parameter resolution
- Different iteration sources
- Sequential and parallel execution
- Error handling and recovery
- State management integration
- Result aggregation

## Performance Considerations

- **Parallel Execution**: Use for I/O bound operations
- **Batch Processing**: Configure batch_size for large datasets
- **Memory Management**: Results are aggregated incrementally
- **State Persistence**: Loop state is persisted for recovery

## Troubleshooting

### Common Issues

1. **Missing Entry Transitions**: Ensure entry_transitions are defined
2. **Exit Detection**: Verify exit_transitions are properly configured
3. **Parameter Resolution**: Check input data mappings and tool parameters
4. **State Conflicts**: Ensure unique loop IDs and proper cleanup

### Debug Logging

Enable debug logging to trace execution:
```python
import logging
logging.getLogger("LoopExecutor").setLevel(logging.DEBUG)
```

## Future Enhancements

- Conditional loop termination
- Dynamic iteration source modification
- Advanced dependency resolution
- Performance metrics and monitoring
- Loop nesting support
