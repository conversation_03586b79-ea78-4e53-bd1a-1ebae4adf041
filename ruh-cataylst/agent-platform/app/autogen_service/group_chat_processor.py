"""
Group Chat Processor for handling group chat conversations and sessions.
"""

import asyncio
import logging
import uuid
from typing import Any, AsyncGenerator, Dict, List, Optional


from autogen_agentchat.base import TaskResult


from ..autogen_service.agent_factory import AgentFactory
from ..autogen_service.group_chat_factory import GroupChatFactory
from ..helper.session_manager import SessionManager
from ..services.agent_group_service import AgentGroupService

logger = logging.getLogger(__name__)


class GroupChatProcessor:
    """Processor for handling group chat conversations."""

    def __init__(self, session_manager: SessionManager, agent_factory: AgentFactory):
        self.session_manager = session_manager
        self.agent_factory = agent_factory
        self.group_chat_factory = GroupChatFactory()
        self.agent_group_service = AgentGroupService()
        self.logger = logger
        self._active_group_chats = {}
        self._group_chat_lock = asyncio.Lock()

    async def create_group_chat_session(
        self,
        group_id: str,
        user_id: str,
        communication_type: str = "group_chat",
        organization_id: Optional[str] = None,
        use_knowledge: Optional[bool] = False,
        variables: Optional[dict] = None,
    ) -> Optional[str]:
        """
        Create a new group chat session.

        Args:
            group_id: The ID of the agent group
            user_id: User creating the session
            communication_type: Type of communication
            organization_id: Organization ID for knowledge access
            use_knowledge: Whether to enable knowledge tools
            variables: Variables for dynamic prompt substitution

        Returns:
            Session ID if successful, None otherwise
        """
        try:
            self.logger.info(f"Creating group chat session for group: {group_id}")

            # Validate group
            if not await self.agent_group_service.validate_group_for_chat(group_id):
                self.logger.error(f"Group {group_id} is not valid for chat")
                return None

            # Get group agent configurations
            agent_configs = await self.agent_group_service.get_group_agent_configs(
                group_id
            )
            if not agent_configs:
                self.logger.error(f"No agent configs found for group {group_id}")
                return None

            # Create session ID
            session_id = str(uuid.uuid4())

            # Create agents using agent factory
            agents = []
            for agent_config in agent_configs:
                try:
                    # Create model context (you may need to adjust this based on your setup)
                    from autogen_core.model_context import BufferedChatCompletionContext

                    model_context = BufferedChatCompletionContext(buffer_size=1000)

                    agent = await self.agent_factory.create_agent(
                        run_id=session_id,
                        agent_config=agent_config,
                        model_context=model_context,
                        memory=None,  # Will use Pinecone memory
                        organization_id=organization_id,
                        use_knowledge=use_knowledge,
                        variables=variables,
                        user_id=user_id,
                        use_pinecone_memory=True,
                    )
                    agents.append(agent)

                except Exception as e:
                    self.logger.error(f"Error creating agent {agent_config.name}: {e}")
                    continue

            if not agents:
                self.logger.error(f"No agents created for group {group_id}")
                return None

            # Get group chat configuration
            chat_config = await self.agent_group_service.get_group_chat_config(group_id)

            # Create group chat team
            team = await self.group_chat_factory.create_group_chat_team(
                group_id=group_id, agents=agents, chat_config=chat_config
            )

            if not team:
                self.logger.error(f"Failed to create team for group {group_id}")
                return None

            # Store session data
            await self.session_manager.create_session(
                session_id=session_id,
                user_id=user_id,
                agent_config=agent_configs[0],  # Use first agent config as primary
                communication_type=communication_type,
                organization_id=organization_id,
                use_knowledge=use_knowledge,
                agent_group_id=group_id,
                variables=variables,
            )

            # Store group chat team
            async with self._group_chat_lock:
                self._active_group_chats[session_id] = {
                    "team": team,
                    "agents": agents,
                    "group_id": group_id,
                    "chat_config": chat_config,
                    "created_at": asyncio.get_event_loop().time(),
                }

            self.logger.info(f"Successfully created group chat session: {session_id}")
            return session_id

        except Exception as e:
            self.logger.error(
                f"Error creating group chat session for group {group_id}: {e}"
            )
            return None

    async def process_group_chat(
        self,
        session_id: str,
        user_message: str,
        run_id: Optional[str] = None,
    ) -> AsyncGenerator[dict, None]:
        """
        Process a message in a group chat session.

        Args:
            session_id: The session ID
            user_message: Message from the user
            run_id: Optional run identifier

        Yields:
            Dictionary with chat response data
        """
        try:
            self.logger.info(f"Processing group chat message for session: {session_id}")

            # Get group chat data
            async with self._group_chat_lock:
                group_chat_data = self._active_group_chats.get(session_id)

            if not group_chat_data:
                self.logger.error(
                    f"No active group chat found for session: {session_id}"
                )
                yield {
                    "session_id": session_id,
                    "error": "Group chat session not found",
                    "success": False,
                }
                return

            team = group_chat_data["team"]
            group_id = group_chat_data["group_id"]

            # Get cancellation token
            (
                agent_config,
                communication_type,
                memory,
                cancellation_token,
                organization_id,
                use_knowledge,
                variables,
            ) = await self.session_manager.get_session_data(session_id)

            # Update session memory with user message
            await self.session_manager.update_session_memory(
                session_id=session_id, message={"role": "user", "content": user_message}
            )

            # Process message with team
            self.logger.info(f"Running team chat for group {group_id}")

            async for message in team.run_stream(
                task=user_message, cancellation_token=cancellation_token
            ):
                if isinstance(message, TaskResult):
                    # Final result
                    self.logger.info(
                        f"Group chat completed with stop reason: {message.stop_reason}"
                    )

                    # Update session memory with final result
                    if message.messages:
                        last_message = message.messages[-1]
                        await self.session_manager.update_session_memory(
                            session_id=session_id,
                            message={
                                "role": "assistant",
                                "content": last_message.content,
                            },
                        )

                    yield {
                        "session_id": session_id,
                        "run_id": run_id,
                        "group_id": group_id,
                        "message_type": "final_result",
                        "stop_reason": message.stop_reason,
                        "total_messages": len(message.messages),
                        "success": True,
                        "final": True,
                    }
                else:
                    # Intermediate message
                    yield {
                        "session_id": session_id,
                        "run_id": run_id,
                        "group_id": group_id,
                        "message_type": "agent_message",
                        "source": getattr(message, "source", "unknown"),
                        "content": getattr(message, "content", str(message)),
                        "message_class": type(message).__name__,
                        "success": True,
                        "final": False,
                    }

        except Exception as e:
            self.logger.error(
                f"Error processing group chat for session {session_id}: {e}"
            )
            yield {
                "session_id": session_id,
                "error": str(e),
                "success": False,
                "final": True,
            }

    async def get_group_chat_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a group chat session.

        Args:
            session_id: The session ID

        Returns:
            Dictionary with group chat information
        """
        try:
            async with self._group_chat_lock:
                group_chat_data = self._active_group_chats.get(session_id)

            if not group_chat_data:
                return None

            team = group_chat_data["team"]
            team_info = self.group_chat_factory.get_team_info(team)

            return {
                "session_id": session_id,
                "group_id": group_chat_data["group_id"],
                "team_info": team_info,
                "chat_config": group_chat_data["chat_config"],
                "created_at": group_chat_data["created_at"],
                "agent_count": len(group_chat_data["agents"]),
            }

        except Exception as e:
            self.logger.error(
                f"Error getting group chat info for session {session_id}: {e}"
            )
            return None

    async def end_group_chat_session(self, session_id: str) -> bool:
        """
        End a group chat session and cleanup resources.

        Args:
            session_id: The session ID

        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info(f"Ending group chat session: {session_id}")

            # Remove from active group chats
            async with self._group_chat_lock:
                group_chat_data = self._active_group_chats.pop(session_id, None)

            if group_chat_data:
                # Reset team if it has a reset method
                team = group_chat_data["team"]
                if hasattr(team, "reset"):
                    await team.reset()

            # End session in session manager
            await self.session_manager.end_session(session_id)

            self.logger.info(f"Successfully ended group chat session: {session_id}")
            return True

        except Exception as e:
            self.logger.error(f"Error ending group chat session {session_id}: {e}")
            return False

    async def get_active_sessions(self) -> List[str]:
        """
        Get list of active group chat session IDs.

        Returns:
            List of active group chat session IDs
        """
        async with self._group_chat_lock:
            return list(self._active_group_chats.keys())
