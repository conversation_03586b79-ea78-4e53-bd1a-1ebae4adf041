/**
 * Credential Feature Flags
 * 
 * This module manages feature flags for the credential management system,
 * enabling gradual rollout and easy rollback capabilities.
 */

import { CredentialFeatureFlags } from '../types/credentials';

/**
 * Get credential feature flags from environment variables
 * Provides safe defaults and validation
 */
export const getCredentialFeatureFlags = (): CredentialFeatureFlags => {
  // Main feature flag to enable/disable credential API
  const useRealAPI = process.env.NEXT_PUBLIC_CREDENTIAL_API_ENABLED === 'true';
  
  // Rollout percentage for gradual deployment (0-100)
  const rolloutPercentage = parseInt(
    process.env.NEXT_PUBLIC_CREDENTIAL_API_ROLLOUT || '0',
    10
  );
  
  // Validate rollout percentage
  const validRolloutPercentage = Math.max(0, Math.min(100, rolloutPercentage));
  
  // Caching feature flag
  const enableCaching = process.env.NEXT_PUBLIC_CREDENTIAL_CACHING_ENABLED !== 'false';
  
  // Migration feature flag
  const enableMigration = process.env.NEXT_PUBLIC_CREDENTIAL_MIGRATION_ENABLED === 'true';
  
  return {
    useRealAPI,
    enableCaching,
    enableMigration,
    rolloutPercentage: validRolloutPercentage,
  };
};

/**
 * Simple hash function for consistent user-based rollout
 * Ensures the same user always gets the same experience
 */
const simpleHash = (str: string): number => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
};

/**
 * Determine if a user should use the credential API based on rollout percentage
 * Uses consistent hashing to ensure same user always gets same experience
 *
 * @deprecated Mock system has been removed. This function now always returns true.
 * All credential operations use the real API endpoints.
 */
export const shouldUseCredentialAPI = (userId?: string): boolean => {
  // Mock system has been completely removed
  // All credential operations now use the real API
  return true;
};

/**
 * Check if credential caching is enabled
 */
export const shouldEnableCredentialCaching = (): boolean => {
  const flags = getCredentialFeatureFlags();
  return flags.enableCaching;
};

/**
 * Check if credential migration is enabled
 */
export const shouldEnableCredentialMigration = (): boolean => {
  const flags = getCredentialFeatureFlags();
  return flags.enableMigration;
};

/**
 * Get all feature flags for debugging/admin purposes
 */
export const getCredentialFeatureFlagsDebug = (): CredentialFeatureFlags & {
  environment: Record<string, string | undefined>;
} => {
  const flags = getCredentialFeatureFlags();
  
  return {
    ...flags,
    environment: {
      NEXT_PUBLIC_CREDENTIAL_API_ENABLED: process.env.NEXT_PUBLIC_CREDENTIAL_API_ENABLED,
      NEXT_PUBLIC_CREDENTIAL_API_ROLLOUT: process.env.NEXT_PUBLIC_CREDENTIAL_API_ROLLOUT,
      NEXT_PUBLIC_CREDENTIAL_CACHING_ENABLED: process.env.NEXT_PUBLIC_CREDENTIAL_CACHING_ENABLED,
      NEXT_PUBLIC_CREDENTIAL_MIGRATION_ENABLED: process.env.NEXT_PUBLIC_CREDENTIAL_MIGRATION_ENABLED,
    },
  };
};

/**
 * Log feature flag status for debugging
 */
export const logCredentialFeatureFlags = (userId?: string): void => {
  const flags = getCredentialFeatureFlags();
  const useAPI = shouldUseCredentialAPI(userId);
  
  console.log('Credential Feature Flags:', {
    useRealAPI: flags.useRealAPI,
    rolloutPercentage: flags.rolloutPercentage,
    enableCaching: flags.enableCaching,
    enableMigration: flags.enableMigration,
    userShouldUseAPI: useAPI,
    userId: userId ? `${userId.substring(0, 4)}***` : 'not provided',
  });
};

/**
 * Override feature flags for testing purposes
 * Only works in development/test environments
 */
export const overrideCredentialFeatureFlags = (overrides: Partial<CredentialFeatureFlags>): void => {
  if (process.env.NODE_ENV === 'production') {
    console.warn('Feature flag overrides are not allowed in production');
    return;
  }
  
  // Store overrides in sessionStorage for testing
  if (typeof window !== 'undefined') {
    const currentOverrides = JSON.parse(
      sessionStorage.getItem('credentialFeatureFlagOverrides') || '{}'
    );
    
    const newOverrides = { ...currentOverrides, ...overrides };
    sessionStorage.setItem('credentialFeatureFlagOverrides', JSON.stringify(newOverrides));
    
    console.log('Credential feature flag overrides applied:', newOverrides);
  }
};

/**
 * Clear feature flag overrides
 */
export const clearCredentialFeatureFlagOverrides = (): void => {
  if (typeof window !== 'undefined') {
    sessionStorage.removeItem('credentialFeatureFlagOverrides');
    console.log('Credential feature flag overrides cleared');
  }
};

/**
 * Get feature flags with test overrides applied
 * Used internally by other functions
 */
const getCredentialFeatureFlagsWithOverrides = (): CredentialFeatureFlags => {
  const baseFlags = getCredentialFeatureFlags();
  
  // Apply overrides in non-production environments
  if (process.env.NODE_ENV !== 'production' && typeof window !== 'undefined') {
    const overrides = JSON.parse(
      sessionStorage.getItem('credentialFeatureFlagOverrides') || '{}'
    );
    
    return { ...baseFlags, ...overrides };
  }
  
  return baseFlags;
};

/**
 * Validate feature flag configuration
 * Ensures flags are properly configured for the environment
 */
export const validateCredentialFeatureFlags = (): {
  isValid: boolean;
  warnings: string[];
  errors: string[];
} => {
  const flags = getCredentialFeatureFlags();
  const warnings: string[] = [];
  const errors: string[] = [];
  
  // Check for common misconfigurations
  if (flags.useRealAPI && flags.rolloutPercentage === 0) {
    warnings.push('Credential API is enabled but rollout percentage is 0%');
  }
  
  if (!flags.useRealAPI && flags.rolloutPercentage > 0) {
    warnings.push('Credential API is disabled but rollout percentage is > 0%');
  }
  
  if (flags.enableMigration && !flags.useRealAPI) {
    errors.push('Migration is enabled but credential API is disabled');
  }
  
  // Environment-specific validations
  if (process.env.NODE_ENV === 'production') {
    if (flags.rolloutPercentage > 0 && flags.rolloutPercentage < 100) {
      warnings.push(`Production rollout is at ${flags.rolloutPercentage}% - consider full rollout or rollback`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    warnings,
    errors,
  };
};

/**
 * Get recommended feature flag settings for different environments
 */
export const getRecommendedCredentialFeatureFlags = (environment: 'development' | 'staging' | 'production'): CredentialFeatureFlags => {
  switch (environment) {
    case 'development':
      return {
        useRealAPI: true,
        enableCaching: true,
        enableMigration: true,
        rolloutPercentage: 100,
      };
      
    case 'staging':
      return {
        useRealAPI: true,
        enableCaching: true,
        enableMigration: true,
        rolloutPercentage: 100,
      };
      
    case 'production':
      return {
        useRealAPI: false, // Start with false for safety
        enableCaching: true,
        enableMigration: false, // Enable only when ready to migrate
        rolloutPercentage: 0, // Start with 0% rollout
      };
      
    default:
      return getCredentialFeatureFlags();
  }
};
