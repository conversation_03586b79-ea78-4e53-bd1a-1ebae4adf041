/**
 * Workflow store for managing workflow state
 * 
 * This store manages the current workflow data including nodes, edges,
 * and workflow metadata. It provides a centralized state management
 * solution for workflow-related operations.
 */

import { create } from 'zustand';
import { Node, Edge } from 'reactflow';
import { WorkflowNodeData } from '@/types';

/**
 * Workflow interface representing the structure of a workflow
 */
export interface Workflow {
  id?: string;
  name?: string;
  description?: string;
  nodes: Node<WorkflowNodeData>[];
  edges: Edge[];
  metadata?: {
    createdAt?: Date;
    updatedAt?: Date;
    version?: string;
    [key: string]: any;
  };
}

/**
 * Workflow store state interface
 */
interface WorkflowState {
  // Current workflow data
  workflow: Workflow | null;
  
  // Loading states
  isLoading: boolean;
  isSaving: boolean;
  
  // Error state
  error: string | null;
  
  // Actions
  setWorkflow: (workflow: Workflow | null) => void;
  updateWorkflow: (updates: Partial<Workflow>) => void;
  updateNodes: (nodes: Node<WorkflowNodeData>[]) => void;
  updateEdges: (edges: Edge[]) => void;
  setLoading: (loading: boolean) => void;
  setSaving: (saving: boolean) => void;
  setError: (error: string | null) => void;
  clearWorkflow: () => void;
}

/**
 * Create the workflow store using Zustand
 */
export const useWorkflowStore = create<WorkflowState>()((set, get) => ({
  // Initial state
  workflow: null,
  isLoading: false,
  isSaving: false,
  error: null,
  
  // Actions
  setWorkflow: (workflow) => {
    set({ workflow, error: null });
  },
  
  updateWorkflow: (updates) => {
    const currentWorkflow = get().workflow;
    if (currentWorkflow) {
      set({
        workflow: {
          ...currentWorkflow,
          ...updates,
          metadata: {
            ...currentWorkflow.metadata,
            ...updates.metadata,
            updatedAt: new Date(),
          },
        },
      });
    }
  },
  
  updateNodes: (nodes) => {
    const currentWorkflow = get().workflow;
    if (currentWorkflow) {
      set({
        workflow: {
          ...currentWorkflow,
          nodes,
          metadata: {
            ...currentWorkflow.metadata,
            updatedAt: new Date(),
          },
        },
      });
    }
  },
  
  updateEdges: (edges) => {
    const currentWorkflow = get().workflow;
    if (currentWorkflow) {
      set({
        workflow: {
          ...currentWorkflow,
          edges,
          metadata: {
            ...currentWorkflow.metadata,
            updatedAt: new Date(),
          },
        },
      });
    }
  },
  
  setLoading: (isLoading) => {
    set({ isLoading });
  },
  
  setSaving: (isSaving) => {
    set({ isSaving });
  },
  
  setError: (error) => {
    set({ error });
  },
  
  clearWorkflow: () => {
    set({
      workflow: null,
      isLoading: false,
      isSaving: false,
      error: null,
    });
  },
}));

/**
 * Selector hooks for common workflow data access patterns
 */
export const useWorkflowNodes = () => useWorkflowStore((state) => state.workflow?.nodes || []);
export const useWorkflowEdges = () => useWorkflowStore((state) => state.workflow?.edges || []);
export const useWorkflowMetadata = () => useWorkflowStore((state) => state.workflow?.metadata);
export const useWorkflowName = () => useWorkflowStore((state) => state.workflow?.name);
export const useWorkflowId = () => useWorkflowStore((state) => state.workflow?.id);
