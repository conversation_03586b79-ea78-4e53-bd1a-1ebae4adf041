import asyncio
import logging
import json
import uuid
from typing import Dict, Any
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer  # type: ignore
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


# Kafka configuration
KAFKA_BOOTSTRAP_SERVERS = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092")
KAFKA_AGENT_CREATION_TOPIC = os.getenv(
    "KAFKA_AGENT_CREATION_TOPIC", "agent_creation_requests"
)
KAFKA_AGENT_CHAT_TOPIC = os.getenv("KAFKA_AGENT_CHAT_TOPIC", "agent_chat_requests")
KAFKA_AGENT_QUERY_TOPIC = os.getenv("KAFKA_AGENT_QUERY_TOPIC", "agent_query_requests")
KAFKA_AGENT_MESSAGE_TOPIC = os.getenv(
    "KAFKA_AGENT_MESSAGE_TOPIC", "agent_message_requests"
)
KAFKA_AGENT_SESSION_DELETION_TOPIC = os.getenv(
    "KAFKA_AGENT_SESSION_DELETION_TOPIC", "agent_session_deletion_requests"
)
KAFKA_AGENT_RESPONSE_TOPIC = os.getenv(
    "KAFKA_AGENT_RESPONSE_TOPIC", "agent_chat_responses"
)


class ComprehensiveKafkaTestClient:
    def __init__(self):
        self.producer = None
        self.consumer = None
        self._initialized = False

    async def initialize(self):
        if self._initialized:
            return

        self.producer = AIOKafkaProducer(
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            max_request_size=524288000,
        )
        self.consumer = AIOKafkaConsumer(
            KAFKA_AGENT_RESPONSE_TOPIC,
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            group_id=f"test-comprehensive-group-{uuid.uuid4()}",
            auto_offset_reset="latest",
            enable_auto_commit=True,
        )

        await self.producer.start()
        await self.consumer.start()
        self._initialized = True
        logger.info("Comprehensive Kafka test client initialized")

    async def cleanup(self):
        if self.producer:
            await self.producer.stop()
        if self.consumer:
            await self.consumer.stop()

    async def send_message(
        self, topic: str, message: Dict[str, Any], headers: list = None
    ) -> None:
        try:
            value = json.dumps(message).encode("utf-8")
            await self.producer.send_and_wait(topic, value=value, headers=headers)
            logger.info(f"Message sent to topic {topic}")
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise

    async def listen_for_responses(self, correlation_id: str = None, timeout: int = 30):
        """Listen for responses with correlation ID matching"""
        try:
            start_time = asyncio.get_event_loop().time()
            responses = []
            logger.info(f"Listening for correlation_id: {correlation_id}")

            while True:
                try:
                    msg = await asyncio.wait_for(
                        self.consumer.getone(), timeout=timeout
                    )

                    current_time = asyncio.get_event_loop().time()
                    if current_time - start_time > timeout:
                        logger.warning("Response listening timeout reached")
                        break

                    # Process message headers
                    msg_correlation_id = None
                    if msg.headers:
                        headers_dict = {
                            k: v.decode("utf-8") if isinstance(v, bytes) else v
                            for k, v in msg.headers
                        }
                        msg_correlation_id = headers_dict.get("correlationId")

                    # Decode and process message value
                    try:
                        response = json.loads(msg.value.decode("utf-8"))

                        # Check correlation ID match
                        if correlation_id and msg_correlation_id == correlation_id:
                            logger.info(f"Matched response for {correlation_id}")
                            responses.append(response)

                            # Check if this is a final response
                            if response.get("final", True) or response.get(
                                "session_id"
                            ):
                                logger.info("Received final response")
                                break

                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to decode message: {e}")
                        continue

                except asyncio.TimeoutError:
                    logger.warning("Timeout waiting for message")
                    break
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    continue

            logger.info(f"Collected {len(responses)} responses")
            return responses

        except Exception as e:
            logger.error(f"Error in listen_for_responses: {e}")
            raise


def get_sample_agent_config():
    """Get a sample agent configuration"""
    return {
        "id": "test-agent-001",
        "name": "Test Assistant",
        "description": "A helpful test assistant",
        "system_message": (
            "You are a helpful assistant. Respond to user queries "
            "in a friendly and informative manner."
        ),
        "model_config": {
            "model": "gpt-4o-mini",
            "temperature": 0.7,
            "max_tokens": 1000,
        },
        "tools": [],
        "capabilities": ["general_assistance", "question_answering"],
    }


async def test_agent_creation(client: ComprehensiveKafkaTestClient):
    """Test agent creation functionality"""
    print("\n🔧 Testing Agent Creation")
    print("-" * 40)

    try:
        run_id = str(uuid.uuid4())
        creation_request = {
            "agent_id": "d406f37f-5c8f-43ce-8835-a69a9a8a764a",
            "user_id": "test_user",
            "communication_type": "single",
            "run_id": run_id,
            "agent_group_id": None,
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        await client.send_message(KAFKA_AGENT_CREATION_TOPIC, creation_request, headers)

        responses = await client.listen_for_responses(correlation_id=run_id, timeout=60)

        if responses and responses[0].get("session_id"):
            session_id = responses[0]["session_id"]
            print(f"✅ Agent created successfully")
            print(f"   Session ID: {session_id}")
            return session_id
        else:
            print("❌ Agent creation failed")
            return None

    except Exception as e:
        print(f"❌ Agent creation error: {e}")
        return None


async def test_agent_chat(client: ComprehensiveKafkaTestClient, session_id: str):
    """Test agent chat functionality"""
    print("\n💬 Testing Agent Chat")
    print("-" * 40)

    try:
        request_id = str(uuid.uuid4())
        chat_request = {
            "run_id": request_id,
            "session_id": session_id,
            "chat_context": [{"role": "user", "content": "Hello, how are you today?"}],
        }

        headers = [
            ("correlationId", request_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        await client.send_message(KAFKA_AGENT_CHAT_TOPIC, chat_request, headers)

        responses = await client.listen_for_responses(
            correlation_id=request_id, timeout=30
        )

        if responses:
            for response in responses:
                if response.get("success", False):
                    agent_response = response.get("agent_response", {})
                    content = agent_response.get("content", "No content")
                    print(f"✅ Chat response received")
                    print(f"   Content: {content[:100]}...")
                    return True
                else:
                    print(f"❌ Chat failed: {response.get('message')}")
                    return False
        else:
            print("❌ No chat response received")
            return False

    except Exception as e:
        print(f"❌ Chat error: {e}")
        return False


async def test_agent_query(client: ComprehensiveKafkaTestClient):
    """Test agent query functionality"""
    print("\n🔍 Testing Agent Query")
    print("-" * 40)

    try:
        run_id = str(uuid.uuid4())
        query_request = {
            "agent_id": "d406f37f-5c8f-43ce-8835-a69a9a8a764a",
            "query": "What is the capital of France?",
            "run_id": run_id,
            "user_id": "test_user",
            "organization_id": None,
            "variables": {},
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        await client.send_message(KAFKA_AGENT_QUERY_TOPIC, query_request, headers)

        responses = await client.listen_for_responses(correlation_id=run_id, timeout=30)

        if responses:
            for response in responses:
                if response.get("success", False):
                    agent_response = response.get("agent_response", {})
                    content = agent_response.get("content", "No content")
                    print(f"✅ Query response received")
                    print(f"   Content: {content[:100]}...")
                    return True
                else:
                    print(f"❌ Query failed: {response.get('message')}")
                    return False
        else:
            print("❌ No query response received")
            return False

    except Exception as e:
        print(f"❌ Query error: {e}")
        return False


async def test_agent_message(client: ComprehensiveKafkaTestClient):
    """Test agent message functionality"""
    print("\n📨 Testing Agent Message")
    print("-" * 40)

    try:
        run_id = str(uuid.uuid4())
        agent_config = get_sample_agent_config()

        message_request = {
            "agent_config": agent_config,
            "run_id": run_id,
            "query": "Tell me a fun fact about space.",
            "user_id": "test_user",
            "variables": {},
            "organization_id": None,
            "use_knowledge": False,
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        await client.send_message(KAFKA_AGENT_MESSAGE_TOPIC, message_request, headers)

        responses = await client.listen_for_responses(correlation_id=run_id, timeout=30)

        if responses:
            for response in responses:
                if response.get("success", False):
                    agent_response = response.get("agent_response", {})
                    content = agent_response.get("content", "No content")
                    print(f"✅ Message response received")
                    print(f"   Content: {content[:100]}...")
                    return True
                else:
                    print(f"❌ Message failed: {response.get('message')}")
                    return False
        else:
            print("❌ No message response received")
            return False

    except Exception as e:
        print(f"❌ Message error: {e}")
        return False


async def test_session_deletion(
    client: ComprehensiveKafkaTestClient, session_id: str
):
    """Test session deletion functionality"""
    print("\n🗑️ Testing Session Deletion")
    print("-" * 40)

    try:
        run_id = str(uuid.uuid4())
        deletion_request = {
            "session_id": session_id,
            "run_id": run_id,
            "user_id": "test_user",
            "reason": "chat_complete",
            "force": False
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        await client.send_message(
            KAFKA_AGENT_SESSION_DELETION_TOPIC, deletion_request, headers
        )

        responses = await client.listen_for_responses(
            correlation_id=run_id, timeout=30
        )

        if responses:
            for response in responses:
                if response.get("success", False):
                    print("✅ Session deletion successful")
                    print(f"   Deleted at: {response.get('deleted_at')}")
                    return True
                else:
                    print(f"❌ Deletion failed: {response.get('message')}")
                    return False
        else:
            print("❌ No deletion response received")
            return False

    except Exception as e:
        print(f"❌ Deletion error: {e}")
        return False


async def run_comprehensive_test():
    """Run comprehensive test of all Kafka functionality"""
    client = ComprehensiveKafkaTestClient()
    await client.initialize()

    try:
        print("🚀 Starting Comprehensive Kafka Test Suite")
        print("=" * 50)

        results = {
            "agent_creation": False,
            "agent_chat": False,
            "agent_query": False,
            "agent_message": False,
            "session_deletion": False,
        }

        # Test 1: Agent Creation
        session_id = await test_agent_creation(client)
        results["agent_creation"] = session_id is not None

        # Test 2: Agent Chat (only if creation succeeded)
        if session_id:
            results["agent_chat"] = await test_agent_chat(client, session_id)

        # Test 3: Agent Query
        results["agent_query"] = await test_agent_query(client)

        # Test 4: Agent Message
        results["agent_message"] = await test_agent_message(client)

        # Test 5: Session Deletion (only if creation succeeded)
        if session_id:
            results["session_deletion"] = await test_session_deletion(
                client, session_id
            )

        # Print summary
        print("\n📊 Test Results Summary")
        print("=" * 50)

        passed = sum(results.values())
        total = len(results)

        for test_name, passed_test in results.items():
            status = "✅ PASSED" if passed_test else "❌ FAILED"
            print(f"{test_name.replace('_', ' ').title()}: {status}")

        print(f"\nOverall: {passed}/{total} tests passed")

        if passed == total:
            print("🎉 All tests passed! Kafka integration is working correctly.")
        else:
            print("⚠️  Some tests failed. Check the logs for details.")

        return results

    except Exception as e:
        logger.error(f"Comprehensive test error: {e}")
        print(f"❌ Test suite failed: {e}")
        return None
    finally:
        await client.cleanup()


async def main():
    try:
        await run_comprehensive_test()
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
