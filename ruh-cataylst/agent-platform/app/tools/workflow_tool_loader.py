import asyncio
import json
import logging
from typing import Any, Dict, List

import httpx
import slugify
from autogen_core import CancellationToken
from autogen_core.tools import BaseTool
from pydantic import BaseModel, create_model

from app.shared.config.base import Settings

from ..helper.api_call import AuthType, HttpMethods, HttpRequestHelper
from ..kafka_client.producer import kafka_producer
from ..shared.config.base import get_settings
from ..utils.constants import SSEEventType

# Configure logger
logger = logging.getLogger(__name__)

# Map JSON Schema types to Python types
json_type_to_py = {
    "string": str,
    "integer": int,
    "number": float,
    "boolean": bool,
    "object": dict,
    "array": list,
}

workflow_execute_endpoint = "workflow-execute/server/execute"
stream_endpoint = "workflow-execute/stream"


class WorkflowToolLoader:
    """
    Utility class to load workflows as dynamic tools and execute them.
    """

    def __init__(self):
        """
        Initialize the workflow tool loader.

        Args:
            workflow_api_base_url: Base URL for workflow execution API
            stream_api_base_url: Base URL for streaming results
            auth_token: Optional authentication token
            auth_type: Authentication type (default: BEARER)
        """
        self.settings = get_settings()
        logger.info("Initializing WorkflowToolLoader")

        # Initialize HTTP request helper for API calls
        self.http_client = HttpRequestHelper(
            base_url=self.settings.workflow_api_gateway.api_url,
            auth_token=self.settings.workflow_api_gateway.api_key,
            auth_type=AuthType.API_KEY,
            api_key_name="X-Server-Auth-Key",
            timeout=60,  # Longer timeout for workflow operations
        )

        self.kafka_agent_response_topic = self.settings.kafka.kafka_agent_response_topic
        logger.debug(
            f"HTTP client initialized with base URL: {self.settings.workflow_api_gateway.api_url}"
        )

    def create_workflow_tool_from_metadata(
        self,
        run_id: str,
        user_id: str,
        workflow_metadata: Dict[str, Any],
    ) -> BaseTool:
        """
        Create a dynamic tool from workflow metadata.

        Args:
            workflow_metadata: Workflow metadata from the API

        Returns:
            A BaseTool instance that can execute the workflow
        """
        workflow_id = workflow_metadata.get("id")
        logger.info(f"Creating workflow tool for workflow ID: {workflow_id}")

        name = f"{workflow_metadata.get('name', 'unnamed').lower().replace(' ', '_')}"

        name = slugify.slugify(name)

        description = workflow_metadata.get(
            "description",
            f"Execute workflow: {workflow_metadata.get('name', workflow_id)}",
        )

        json_schema = workflow_metadata.get("start_nodes", {})
        logger.debug(f"Workflow tool schema for {name}: {json_schema}")

        # Create the dynamic tool with our execution endpoint
        return self._create_workflow_execution_tool(
            run_id=run_id,
            user_id=user_id,
            workflow_id=workflow_id,
            name=name,
            description=description,
            json_schema=json_schema,
        )

    def _create_workflow_execution_tool(
        self,
        run_id: str,
        user_id: str,
        workflow_id: str,
        name: str,
        description: str,
        json_schema: Dict[str, Any],
    ) -> BaseTool:
        """
        Create a dynamic tool that executes a workflow.

        Args:
            workflow_id: ID of the workflow to execute
            name: Name for the tool
            description: Description for the tool
            json_schema: JSON schema for the tool parameters

        Returns:
            A BaseTool instance
        """
        logger.debug(
            f"Creating execution tool for workflow {workflow_id} with name {name}"
        )

        # Store field to transition_id mapping for payload construction
        field_transition_mapping = {}

        # Check if json_schema is a list (new format) or dict (old format)
        if isinstance(json_schema, list):
            # New format - list of field definitions
            logger.debug(
                f"Processing new schema format (list) with {len(json_schema)} fields"
            )
            fields = {}
            for field_def in json_schema:
                field_name = field_def.get("field")
                if not field_name:
                    continue

                # Store the transition_id for this field
                transition_id = field_def.get("transition_id")
                if transition_id:
                    field_transition_mapping[field_name] = transition_id
                    logger.debug(
                        f"Mapped field {field_name} to transition_id {transition_id}"
                    )

                # Determine the base type from the field definition
                field_type_str = field_def.get("type", "string")

                # Handle enum type specially
                if field_type_str == "enum" and "enum" in field_def:
                    py_type = str
                    logger.debug(
                        f"Added enum field {field_name} with values: {field_def.get('enum')}"
                    )
                else:
                    py_type = json_type_to_py.get(field_type_str, str)
                    logger.debug(f"Added field {field_name} with type {py_type}")

                # All fields are required in the new format
                fields[field_name] = (py_type, ...)
        else:
            # Old format - JSON Schema object
            logger.debug("Processing traditional JSON Schema format")
            fields = {}
            for k, v in json_schema.get("properties", {}).items():
                py_type = json_type_to_py.get(v.get("type", "string"), str)
                default = ... if k in json_schema.get("required", []) else None
                fields[k] = (py_type, default)
                logger.debug(f"Added field {k} with type {py_type} to tool schema")

        ArgsModel = create_model(f"{name}_Args", **fields)
        ArgsModel.model_rebuild()  # Ensure the model is fully defined
        logger.debug(f"Created args model for {name}: {ArgsModel}")

        class WorkflowTool(BaseTool[ArgsModel, BaseModel]):
            def __init__(self, loader: WorkflowToolLoader):
                super().__init__(
                    args_type=ArgsModel,
                    return_type=BaseModel,
                    name=name,
                    description=description,
                )
                self.workflow_id = workflow_id
                self.loader = loader
                self.run_id: str = run_id
                self.user_id: str = user_id
                self.settings: Settings = get_settings()
                self.kafka_agent_response_topic = (
                    self.settings.kafka.kafka_agent_response_topic
                )
                # Store the field to transition_id mapping for payload construction
                self.field_transition_mapping = field_transition_mapping
                logger.debug(f"Initialized WorkflowTool for {workflow_id}")
                logger.debug(f"Field transition mapping: {field_transition_mapping}")

            async def run(self, args: ArgsModel, cancellation_token: CancellationToken):
                """
                Execute the workflow and return the result as a string.
                If the workflow returns a stream, join the stream and return the full result.
                If an error occurs, return a user-friendly error message.
                """
                args_dict = args.model_dump(exclude_none=True)

                print(f"args_dict: {args_dict}")

                # Build user_payload_template with transition_id for each field
                user_payload_template = {}
                for field_name, field_value in args_dict.items():
                    transition_id = self.field_transition_mapping.get(field_name)
                    if transition_id:
                        # New format with value and transition_id
                        user_payload_template[field_name] = {
                            "value": field_value,
                            "transition_id": transition_id,
                        }
                        logger.debug(
                            f"Added field {field_name} with transition_id {transition_id}"
                        )
                    else:
                        # Fallback to old format if no transition_id found
                        user_payload_template[field_name] = field_value
                        logger.warning(
                            f"No transition_id found for field {field_name}, using old format"
                        )

                payload = {
                    "user_id": self.user_id,
                    "workflow_id": self.workflow_id,
                    "approval": False,
                    "payload": {
                        "user_dependent_fields": list(args_dict.keys()),
                        "user_payload_template": user_payload_template,
                    },
                }
                logger.info(f"Workflow execution payload: {payload}")

                headers = [
                    ("correlationId", self.run_id.encode("utf-8")),
                    (
                        "reply-topic",
                        self.kafka_agent_response_topic.encode("utf-8"),
                    ),
                ]

                await kafka_producer.init_kafka_producer()

                try:
                    logger.info(
                        f"Sending execution request for workflow {self.workflow_id}"
                    )
                    response = await asyncio.to_thread(
                        self.loader.http_client.post,
                        workflow_execute_endpoint,
                        json_data=payload,
                    )
                    logger.debug(f"Execution response: {response}")

                    if isinstance(response, dict) and "correlationId" in response:
                        correlation_id = response["correlationId"]
                        logger.info(
                            f"Got correlation ID from response: {correlation_id}"
                        )

                        resp = {
                            "run_id": self.run_id,
                            "correlation_id": correlation_id,
                            "message": f"Agent initiated {name} workflow execution.",
                            "workflow_id": self.workflow_id,
                            "success": True,
                            "final": True,
                            "event_type": SSEEventType.WORKFLOW_EXECUTION_STARTED.value,
                        }
                        await kafka_producer.send_message(
                            self.kafka_agent_response_topic,
                            resp,
                            headers,
                        )
                    else:
                        logger.warning(f"No correlation ID in response: {response}")

                    # # Stream the results
                    # async def stream_results():
                    #     stream_url = f"{stream_endpoint}/{correlation_id}"
                    #     logger.info(f"Starting to stream results from: {stream_url}")

                    #     async with httpx.AsyncClient(
                    #         base_url=self.loader.http_client.base_url,
                    #         headers=self.loader.http_client.headers,
                    #         timeout=300,
                    #     ) as client:
                    #         try:
                    #             logger.info(
                    #                 f"Opening stream connection to {stream_url}"
                    #             )
                    #             async with client.stream(
                    #                 HttpMethods.GET.value, stream_url
                    #             ) as response:
                    #                 response.raise_for_status()
                    #                 logger.info(
                    #                     f"Stream connection established, status: {response.status_code}"
                    #                 )

                    #                 buffer = ""
                    #                 async for chunk in response.aiter_text():
                    #                     logger.info(
                    #                         f"Received stream chunk of size: {len(chunk)} bytes"
                    #                     )
                    #                     buffer += chunk
                    #                     while "\n\n" in buffer:
                    #                         message, buffer = buffer.split("\n\n", 1)
                    #                         for line in message.split("\n"):
                    #                             if line.startswith("data:"):
                    #                                 data = line[5:].strip()
                    #                                 try:
                    #                                     json_data = json.loads(data)
                    #                                     logger.info(
                    #                                         f"Parsed JSON data from stream: {json_data}"
                    #                                     )
                    #                                     yield json.dumps(json_data)
                    #                                 except json.JSONDecodeError:
                    #                                     logger.warning(
                    #                                         f"Failed to parse JSON from stream: {data}"
                    #                                     )
                    #                                     yield data
                    #         except Exception as e:
                    #             logger.error(
                    #                 f"Error streaming results: {str(e)}", exc_info=True
                    #             )
                    #             yield json.dumps(
                    #                 {"error": f"Streaming error: {str(e)}"}
                    #             )

                    # # Join the stream and return the full result as a string
                    # logger.info(
                    #     f"Returning stream generator for workflow {self.workflow_id}"
                    # )
                    # result_chunks = []
                    # async for chunk in stream_results():
                    #     result_chunks.append(chunk)
                    # return "\n".join(result_chunks)

                    return {
                        "success": True,
                        "message": f"{name} Workflow execution initiated",
                        "correlation_id": correlation_id,
                    }

                except Exception as e:
                    logger.error(
                        f"Error executing workflow {self.workflow_id}: {str(e)}",
                        exc_info=True,
                    )
                    resp = {
                        "run_id": self.run_id,
                        "correlation_id": None,
                        "workflow_id": self.workflow_id,
                        "message": f"Workflow execution error: {str(e)}",
                        "success": False,
                        "final": True,
                        "event_type": SSEEventType.WORKFLOW_EXECUTION_FAILED.value,
                    }
                    await kafka_producer.send_message(
                        self.kafka_agent_response_topic,
                        resp,
                        headers,
                    )
                    return {
                        "success": False,
                        "message": f"Workflow execution error: {str(e)}",
                        "correlation_id": correlation_id,
                    }

            def return_value_as_string(self, value):
                return str(value)

            async def join_stream(self, value):
                """
                Helper to join all chunks from an async generator.
                If value is already a string, return as is.
                """
                logger.info(f"Joining stream for workflow {self.workflow_id}")
                if hasattr(value, "__aiter__"):
                    results = []
                    async for chunk in value:
                        logger.info(f"Appending chunk of size: {len(chunk)} bytes")
                        results.append(chunk)
                    joined = "\n".join(results)
                    logger.info(f"Joined stream, total size: {len(joined)} bytes")
                    return joined
                logger.info("Value is not a stream, returning as string")
                return str(value)

        logger.info(f"Created workflow tool: {name}")
        return WorkflowTool(self)

    async def load_workflows_as_tools(
        self, run_id, user_id, workflows
    ) -> List[BaseTool]:
        """
        Load all available workflows as tools.

        Args:
            workflows_endpoint: API endpoint to get workflow metadata

        Returns:
            List of BaseTool instances for each workflow
        """
        try:
            logger.info(
                f"Loading workflows as tools, count: {len(workflows) if isinstance(workflows, list) else 'unknown'}"
            )

            if not isinstance(workflows, list):
                logger.error(
                    f"Error: Expected list of workflows, got {type(workflows)}"
                )
                return []

            # Create a tool for each workflow
            tools = []
            for workflow in workflows:
                try:
                    workflow_id = workflow.get("id", "unknown")
                    logger.info(f"Creating tool for workflow: {workflow_id}")
                    tool = self.create_workflow_tool_from_metadata(
                        run_id, user_id, workflow
                    )
                    tools.append(tool)
                    logger.info(
                        f"Successfully created tool for workflow: {workflow_id}"
                    )
                except Exception as e:
                    logger.error(
                        f"Error creating tool for workflow {workflow.get('id', 'unknown')}: {str(e)}",
                        exc_info=True,
                    )

            logger.info(f"Successfully loaded {len(tools)} workflow tools")
            return tools

        except Exception as e:
            logger.error(f"Error loading workflows: {str(e)}", exc_info=True)
            return []
