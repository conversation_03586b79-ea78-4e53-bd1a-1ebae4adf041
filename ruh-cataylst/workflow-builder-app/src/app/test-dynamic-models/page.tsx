'use client';

import React, { useState, useEffect } from 'react';
import { fetchProviders, fetchModels, fetchModelsByProvider, Provider, Model } from '@/lib/api';
import { DynamicModelDropdown } from '@/components/ui/dynamic-model-dropdown';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function TestDynamicModelsPage() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [allModels, setAllModels] = useState<Model[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Provider-models mapping for the dynamic dropdown
  const [providerModelsMapping, setProviderModelsMapping] = useState<Record<string, {
    models: string[];
    providerId: string;
    providerName: string;
  }>>({});

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Loading providers and models...');
      
      const [providersResponse, modelsResponse] = await Promise.all([
        fetchProviders(),
        fetchModels()
      ]);

      console.log('Providers response:', providersResponse);
      console.log('Models response:', modelsResponse);

      if (providersResponse.success) {
        setProviders(providersResponse.providers);
      } else {
        console.warn('Failed to fetch providers:', providersResponse.message);
        setProviders([]);
      }

      if (modelsResponse.success) {
        setAllModels(modelsResponse.models);
        
        // Create provider-models mapping
        const mapping: Record<string, { models: string[]; providerId: string; providerName: string; }> = {};
        
        modelsResponse.models.forEach(model => {
          const providerName = model.provider.provider;
          const providerId = model.provider.id;
          
          if (!mapping[providerName]) {
            mapping[providerName] = {
              models: [],
              providerId: providerId,
              providerName: providerName
            };
          }
          mapping[providerName].models.push(model.model);
        });
        
        // Sort models within each provider
        Object.keys(mapping).forEach(provider => {
          mapping[provider].models.sort();
        });
        
        setProviderModelsMapping(mapping);
        console.log('Provider-models mapping:', mapping);
      } else {
        console.warn('Failed to fetch models:', modelsResponse.message);
        setAllModels([]);
      }

    } catch (err) {
      console.error('Error loading data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const testProviderModels = async (providerName: string) => {
    if (!providerName || !providerModelsMapping[providerName]) return;
    
    const providerId = providerModelsMapping[providerName].providerId;
    console.log(`Testing API call for provider: ${providerName} (ID: ${providerId})`);
    
    try {
      const response = await fetchModelsByProvider(providerId);
      console.log(`API response for ${providerName}:`, response);
    } catch (err) {
      console.error(`Error testing API for ${providerName}:`, err);
    }
  };

  const allModelNames = allModels.map(m => m.model).sort();

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <h1 className="text-3xl font-bold mb-6">Dynamic Model Filtering Test</h1>
      
      {loading && (
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
          Loading data...
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          Error: {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Provider Selection */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">1. Select Provider</h2>
          <Select value={selectedProvider} onValueChange={setSelectedProvider}>
            <SelectTrigger>
              <SelectValue placeholder="Select a provider..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">-- All Providers --</SelectItem>
              {providers.map((provider) => (
                <SelectItem key={provider.id} value={provider.provider}>
                  <div className="flex flex-col">
                    <span>{provider.provider}</span>
                    <span className="text-xs text-muted-foreground">
                      {provider.modelCount} models
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {selectedProvider && (
            <div className="mt-4">
              <button
                onClick={() => testProviderModels(selectedProvider)}
                className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
              >
                Test API Call
              </button>
            </div>
          )}
        </div>

        {/* Dynamic Model Selection */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">2. Select Model (Dynamic)</h2>
          <DynamicModelDropdown
            value={selectedModel}
            onChange={setSelectedModel}
            selectedProvider={selectedProvider}
            providerModelsMapping={providerModelsMapping}
            allModels={allModelNames}
            placeholder="Select a model..."
          />
          
          {selectedModel && (
            <div className="mt-4 p-3 bg-gray-50 rounded">
              <p className="text-sm">
                <strong>Selected:</strong> {selectedModel}
              </p>
              {selectedProvider && (
                <p className="text-sm text-gray-600">
                  <strong>Provider:</strong> {selectedProvider}
                </p>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Provider-Models Mapping Display */}
      <div className="mt-6 bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Provider-Models Mapping</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(providerModelsMapping).map(([providerName, data]) => (
            <div key={providerName} className="border rounded p-4">
              <h3 className="font-medium mb-2">{providerName}</h3>
              <p className="text-xs text-gray-500 mb-2">ID: {data.providerId}</p>
              <div className="space-y-1">
                {data.models.slice(0, 5).map((model, index) => (
                  <div key={index} className="text-sm text-gray-700">
                    {model}
                  </div>
                ))}
                {data.models.length > 5 && (
                  <div className="text-xs text-gray-500">
                    +{data.models.length - 5} more...
                  </div>
                )}
              </div>
              <div className="mt-2 text-xs text-gray-500">
                Total: {data.models.length} models
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* All Models Display */}
      <div className="mt-6 bg-white shadow rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">All Available Models ({allModels.length})</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 max-h-64 overflow-y-auto">
          {allModels.map((model) => (
            <div key={model.id} className="text-sm p-2 border rounded">
              <div className="font-medium">{model.model}</div>
              <div className="text-xs text-gray-500">{model.provider.provider}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 bg-gray-50 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Test Instructions</h2>
        <ol className="list-decimal list-inside space-y-2 text-sm">
          <li>Select a provider from the dropdown</li>
          <li>Watch the model dropdown update to show only models for that provider</li>
          <li>Select "-- All Providers --" to see all models</li>
          <li>Click "Test API Call" to test the provider-specific API endpoint</li>
          <li>Check browser DevTools → Network tab for API calls</li>
          <li>Check browser console for detailed logs</li>
        </ol>
        
        <div className="mt-4 p-3 bg-blue-100 border border-blue-400 rounded">
          <p className="text-sm font-medium">🎯 Expected Behavior:</p>
          <ul className="text-sm mt-2 space-y-1">
            <li>• Provider selection should filter models dynamically</li>
            <li>• API calls should only happen when needed</li>
            <li>• Model selection should reset when provider changes</li>
            <li>• Fallback to static mapping when API fails</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
