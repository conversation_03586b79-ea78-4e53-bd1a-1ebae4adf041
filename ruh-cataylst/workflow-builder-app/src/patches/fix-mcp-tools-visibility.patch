diff --git a/frontend/src/components/layout/InspectorPanel.tsx b/frontend/src/components/layout/InspectorPanel.tsx
index abcdefg..hijklmn 100644
--- a/frontend/src/components/layout/InspectorPanel.tsx
+++ b/frontend/src/components/layout/InspectorPanel.tsx
@@ -1,4 +1,4 @@
-import React, { useCallback, useState, useEffect } from 'react';
+import React, { useCallback, useState, useEffect, useRef } from 'react';
 import { useComponentStateStore, getMcpToolsValue, setMcpToolsValue, clearMcpToolsState, clearAllMcpToolsState } from '@/store/mcpToolsStore';
 import { Node, Edge } from 'reactflow';
 import { useConnectedHandles } from '@/hooks/useConnectedHandles';
@@ -20,6 +20,9 @@ interface InspectorPanelProps {
 
 export function InspectorPanel({ selectedNode, onNodeDataChange, onClose, onDeleteNode, edges, nodes }: InspectorPanelProps) {
   const [isOpen, setIsOpen] = useState(!!selectedNode);
+  
+  // Add a state variable to force re-renders when needed
+  const [forceUpdate, setForceUpdate] = useState(0);
 
   // Update open state when selectedNode changes
   useEffect(() => {
@@ -27,6 +30,12 @@ export function InspectorPanel({ selectedNode, onNodeDataChange, onClose, onDele
     if (selectedNode) setIsOpen(true);
   }, [selectedNode]);
 
+  // Use forceUpdate in a useEffect to trigger re-renders when needed
+  useEffect(() => {
+    // This effect will run whenever forceUpdate changes
+    console.log('Forced update triggered:', forceUpdate);
+  }, [forceUpdate]);
+
   // --- Handle Connected Inputs ---
   const { isInputConnected, getConnectedEdge, getSourceNode } = useConnectedHandles(edges, nodes);
 
@@ -89,6 +98,18 @@ export function InspectorPanel({ selectedNode, onNodeDataChange, onClose, onDele
 
   // --- Check Input Visibility based on rules ---
   const checkInputVisibility = (inputDef: InputDefinition, config: Record<string, any>): boolean => {
+    // Special handling for MCP Tools component
+    if (selectedNode?.data.type === "MCPToolsComponent") {
+      // For command and fetch_stdio_tools, only show when mode is Stdio
+      if (inputDef.name === "command" || inputDef.name === "fetch_stdio_tools") {
+        return config.mode === "Stdio";
+      }
+      
+      // For sse_url and fetch_sse_tools, only show when mode is SSE
+      if (inputDef.name === "sse_url" || inputDef.name === "fetch_sse_tools") {
+        return config.mode === "SSE";
+      }
+    
     // Special handling for DynamicCombineTextComponent
     if (selectedNode?.data.type === "DynamicCombineTextComponent" && inputDef.name.startsWith("input_") && !inputDef.is_handle) {
       // Extract the index from the input name (e.g., "input_3" -> 3)
@@ -101,7 +122,7 @@ export function InspectorPanel({ selectedNode, onNodeDataChange, onClose, onDele
     }
 
     // Special handling for MCP Tools component
-    if (selectedNode?.data.type === "MCPToolsComponent") {
+    //if (selectedNode?.data.type === "MCPToolsComponent") {
       // For selected_tool_name, check connection_status
       if (inputDef.name === "selected_tool_name") {
         // Check connection status from different sources
@@ -117,7 +138,7 @@ export function InspectorPanel({ selectedNode, onNodeDataChange, onClose, onDele
         console.log(`Hiding button: ${inputDef.name}`);
         return false; // Always hide these buttons
       }
-    }
+    //}
 
     // Use the utility function for standard visibility rules
     return shouldShowInput(inputDef, config);
@@ -242,6 +263,9 @@ export function InspectorPanel({ selectedNode, onNodeDataChange, onClose, onDele
                   // Clear state when switching modes
                   if (selectedNode) {
                     clearMcpToolsState(selectedNode.id);
+                    
+                    // Force a re-render to update visibility
+                    setForceUpdate(prev => prev + 1);
                   }
                   handleConfigChange(inputDef.name, value);
                 }}
@@ -456,7 +480,13 @@ export function InspectorPanel({ selectedNode, onNodeDataChange, onClose, onDele
                             {selectedNode.data.definition.inputs
                               .map(inputDef => {
                                 // Check if this input should be visible based on rules
-                                const isVisible = checkInputVisibility(inputDef, selectedNode.data.config || {});
+                                // Make sure we have the latest mode value in the config
+                                const configWithLatestMode = {
+                                  ...selectedNode.data.config,
+                                  mode: getConfigValue("mode", "Stdio") // Ensure mode is up-to-date
+                                };
+                                
+                                const isVisible = checkInputVisibility(inputDef, configWithLatestMode);
                                 if (!isVisible) return null;
 
                                 // Check if this input is connected to another node
