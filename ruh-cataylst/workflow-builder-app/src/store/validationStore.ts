import { create } from "zustand";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { validateWorkflow } from "@/lib/validation";
import {
  validateWorkflowSmart,
  validateWorkflowBeforeSave,
  validateWorkflowBeforeExecution,
  validateWorkflowDuringEditing,
} from "@/lib/validation/smartValidation";
import {
  ValidationError,
  ValidationResult,
  MissingField,
  WorkflowValidationOptions,
} from "@/lib/validation/types";

/**
 * Validation state interface
 */
interface ValidationState {
  // State
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  infos: ValidationError[];
  missingFields: MissingField[];
  startNodeId?: string;
  connectedNodes?: Set<string>;
  isValidating: boolean;
  hasValidated: boolean;

  // Actions
  validateWorkflow: (
    nodes: Node<WorkflowNodeData>[],
    edges: Edge[],
    options?: WorkflowValidationOptions
  ) => ValidationResult;
  validateWorkflowSmart: (
    nodes: Node<WorkflowNodeData>[],
    edges: Edge[],
    options?: WorkflowValidationOptions
  ) => Promise<ValidationResult>;
  validateBeforeSave: (
    nodes: Node<WorkflowNodeData>[],
    edges: Edge[]
  ) => Promise<ValidationResult>;
  validateBeforeExecution: (
    nodes: Node<WorkflowNodeData>[],
    edges: Edge[]
  ) => Promise<ValidationResult>;
  validateDuringEditing: (
    nodes: Node<WorkflowNodeData>[],
    edges: Edge[]
  ) => Promise<ValidationResult>;
  clearValidation: () => void;
}

/**
 * Validation store using Zustand
 */
export const useValidationStore = create<ValidationState>()((set, get) => ({
  // Initial state
  isValid: true,
  errors: [],
  warnings: [],
  infos: [],
  missingFields: [],
  isValidating: false,
  hasValidated: false,

  // Actions
  validateWorkflow: (nodes, edges, options) => {
    set({ isValidating: true });

    // Perform validation
    const result = validateWorkflow(nodes, edges, options);

    // Update state with results
    set({
      isValid: result.isValid,
      errors: result.errors,
      warnings: result.warnings,
      infos: result.infos || [],
      missingFields: result.missingFields || [],
      startNodeId: result.startNodeId,
      connectedNodes: result.connectedNodes,
      isValidating: false,
      hasValidated: true,
    });

    return result;
  },

  validateWorkflowSmart: async (nodes, edges, options) => {
    set({ isValidating: true });

    // Perform validation
    const result = await validateWorkflowSmart(nodes, edges, options);

    // Update state with results
    set({
      isValid: result.isValid,
      errors: result.errors,
      warnings: result.warnings,
      infos: result.infos || [],
      missingFields: result.missingFields || [],
      startNodeId: result.startNodeId,
      connectedNodes: result.connectedNodes,
      isValidating: false,
      hasValidated: true,
    });

    return result;
  },

  validateBeforeSave: async (nodes, edges) => {
    set({ isValidating: true });

    // Perform validation
    const result = await validateWorkflowBeforeSave(nodes, edges);

    // Update state with results
    set({
      isValid: result.isValid,
      errors: result.errors,
      warnings: result.warnings,
      infos: result.infos || [],
      missingFields: result.missingFields || [],
      startNodeId: result.startNodeId,
      connectedNodes: result.connectedNodes,
      isValidating: false,
      hasValidated: true,
    });

    return result;
  },

  validateBeforeExecution: async (nodes, edges) => {
    set({ isValidating: true });

    // Perform validation
    const result = await validateWorkflowBeforeExecution(nodes, edges);

    // Update state with results
    set({
      isValid: result.isValid,
      errors: result.errors,
      warnings: result.warnings,
      infos: result.infos || [],
      missingFields: result.missingFields || [],
      startNodeId: result.startNodeId,
      connectedNodes: result.connectedNodes,
      isValidating: false,
      hasValidated: true,
    });

    return result;
  },

  validateDuringEditing: async (nodes, edges) => {
    set({ isValidating: true });

    // Perform validation
    const result = await validateWorkflowDuringEditing(nodes, edges);

    // Update state with results
    set({
      isValid: result.isValid,
      errors: result.errors,
      warnings: result.warnings,
      infos: result.infos || [],
      missingFields: result.missingFields || [],
      startNodeId: result.startNodeId,
      connectedNodes: result.connectedNodes,
      isValidating: false,
      hasValidated: true,
    });

    return result;
  },

  clearValidation: () => {
    set({
      isValid: true,
      errors: [],
      warnings: [],
      infos: [],
      missingFields: [],
      startNodeId: undefined,
      connectedNodes: undefined,
      hasValidated: false,
    });
  },
}));
