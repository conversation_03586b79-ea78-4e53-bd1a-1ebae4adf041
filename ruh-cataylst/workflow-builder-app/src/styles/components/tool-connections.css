/**
 * CSS styles for AgenticAI tool connections and visual distinctions
 */

/* AgenticAI node with connected tools */
.agentic-ai-has-tools {
  border: 2px solid #f59e0b !important;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2) !important;
  background: linear-gradient(135deg, #ffffff 0%, #fef3c7 100%) !important;
}

/* Dark mode support for AgenticAI with tools */
.dark .agentic-ai-has-tools {
  background: linear-gradient(135deg, #1f2937 0%, #451a03 100%) !important;
  border-color: #f59e0b !important;
}

/* Tool count indicator badge */
.tool-count-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #f59e0b;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

/* Dark mode support for tool count badge */
.dark .tool-count-badge {
  border-color: #1f2937;
}

/* Tool handle styling */
.tool-handle {
  background-color: #f59e0b !important;
  border: 2px solid #d97706 !important;
  position: relative;
}

.tool-handle:hover {
  background-color: #d97706 !important;
  transform: scale(1.1);
}

/* Tool handle icon */
.tool-handle::before {
  content: "🔧";
  font-size: 10px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

/* Components connected as tools */
.connected-as-tool {
  border: 2px dashed #f59e0b !important;
  background: rgba(245, 158, 11, 0.05) !important;
  position: relative;
}

/* Dark mode support for connected as tool */
.dark .connected-as-tool {
  background: rgba(245, 158, 11, 0.1) !important;
}

/* Tool connection indicator badge for connected components */
.connected-as-tool::after {
  content: "🔧";
  position: absolute;
  top: -8px;
  left: -8px;
  background-color: #f59e0b;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

/* Dark mode support for tool connection indicator */
.dark .connected-as-tool::after {
  border-color: #1f2937;
}

/* Tool handle label styling */
.tool-handle-label {
  background-color: #f59e0b;
  color: white;
  border: 1px solid #d97706;
  font-size: 9px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.tool-handle-label::before {
  content: "🔧";
  font-size: 8px;
}

/* Regular handle styling (for contrast) */
.regular-handle {
  background-color: #3b82f6 !important;
  border: 2px solid #1e40af !important;
}

.regular-handle:hover {
  background-color: #1e40af !important;
  transform: scale(1.1);
}

/* Animation for tool connection state changes */
.tool-connection-transition {
  transition: all 0.3s ease-in-out;
}

/* Pulse animation for newly connected tools */
@keyframes tool-connection-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

.tool-connection-pulse {
  animation: tool-connection-pulse 2s infinite;
}

/* Hover effects for tool-related elements */
.agentic-ai-has-tools:hover {
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.3) !important;
}

.connected-as-tool:hover {
  background: rgba(245, 158, 11, 0.1) !important;
  border-color: #d97706 !important;
}

/* Enhanced tool handle states */
.tool-handle-hover {
  background-color: #f59e0b !important;
  transform: scale(1.1);
  box-shadow: 0 0 8px rgba(245, 158, 11, 0.5);
}

.tool-handle-focus {
  background-color: #f59e0b !important;
  outline: 2px solid #f59e0b;
  outline-offset: 2px;
}

.tool-handle-connected {
  background-color: #10b981 !important;
  border-color: #10b981 !important;
}

.tool-handle-disabled {
  background-color: #6b7280 !important;
  cursor: not-allowed;
  opacity: 0.5;
}

.tool-handle-error {
  background-color: #ef4444 !important;
  border-color: #ef4444 !important;
  animation: pulse 2s infinite;
}

/* Tool handle container */
.tool-handle-container {
  position: relative;
  display: flex;
  align-items: center;
}

.tool-handle-right {
  justify-content: flex-end;
}

/* Enhanced tool handle labels */
.tool-handle-label-connected {
  color: #10b981;
  border-color: #10b981;
}

/* Handle sections */
.handles-container {
  position: relative;
  width: 100%;
}

.handles-scrollable {
  max-height: 200px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #f59e0b transparent;
}

.handles-scrollable::-webkit-scrollbar {
  width: 4px;
}

.handles-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.handles-scrollable::-webkit-scrollbar-thumb {
  background-color: #f59e0b;
  border-radius: 2px;
}

/* Available tool slots */
.tool-slot-available {
  position: relative;
  margin: 2px 0;
}

.tool-slot-available .border-dashed {
  border-style: dashed;
  border-width: 2px;
  border-color: #f59e0b;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.tool-slot-available:hover .border-dashed {
  opacity: 1;
}

/* Focus states for accessibility */
.tool-handle:focus,
.tool-handle:focus-visible {
  outline: 2px solid #f59e0b;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .agentic-ai-has-tools {
    border-width: 3px !important;
  }
  
  .connected-as-tool {
    border-width: 3px !important;
  }
  
  .tool-count-badge,
  .connected-as-tool::after {
    border-width: 3px !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .tool-connection-transition,
  .tool-connection-pulse,
  .tool-handle,
  .regular-handle {
    animation: none !important;
    transition: none !important;
  }
}
