import pytest
from app.autogen_service.head_agent import HeadAgent
from app.autogen_service.ai_agent import AIAgent


@pytest.mark.asyncio
async def test_head_agent_initialization(head_agent):
    assert isinstance(head_agent, HeadAgent)
    assert head_agent.name == "head_agent"


@pytest.mark.asyncio
async def test_head_agent_task_delegation():
    head_agent = HeadAgent()
    message = "Create a marketing plan"
    response = await head_agent.process_message(message)
    assert response is not None
    assert isinstance(response, str)


@pytest.mark.asyncio
async def test_head_agent_error_handling():
    head_agent = HeadAgent()
    with pytest.raises(ValueError):
        await head_agent.process_message("")


@pytest.mark.asyncio
async def test_agent_registration():
    head_agent = HeadAgent()
    test_agent = AIAgent("test_agent")
    head_agent.register_agent(test_agent)
    assert "test_agent" in head_agent.get_registered_agents()
