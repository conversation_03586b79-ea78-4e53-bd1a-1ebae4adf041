import asyncio
from ..shared.config.base import get_settings
import argparse
import os
from ..services.agent_chat import Agent<PERSON><PERSON>
from ..schemas.agent_config import AgentConfig


settings = get_settings()


async def interactive_chat():
    agent_chat = AgentChat()

    # Example configuration
    config = AgentConfig(
        name="Assistant",
        description="A helpful AI assistant",
        agent_type="autogen",
        ai_model_config={
            "model": "gpt-4o-mini",  # Using the default model from settings
            "api_key": settings.openai.api_key,  # Ensure you have your API key set
            "temperature": 0.7,  # Optional
            "max_tokens": 150,  # Optional
        },
        system_message="You are a helpful AI assistant. Use tools to solve tasks.",
        memory_enabled=True,
        memory_config={
            "initial_entries": [
                "The weather should be in metric units",
                "Meal recipe must be vegan",
            ]
        },
        tools=[
            {
                "tool_type": "workflow",
                "url": "http://localhost:5000/execute-by-name",
                "workflow": {
                    "workflow_id": "data_processing_test_workflow",
                    "approval": "False",
                    "description": "Generate a script, audio, and images for a marketing video",
                    "payload": {
                        "user_dependent_fields": ["topic", "video_type", "keywords"],
                        "user_payload_template": {
                            "topic": "",
                            "video_type": "",
                            "keywords": "",
                        },
                    },
                },
            }
        ],
    )

    # Register agent
    print("Registering new agent...")
    agent = await agent_chat.register_agent(config)
    print(f"\nAgent '{agent.name}' registered successfully!")

    # Start chat session
    session_id = await agent_chat.start_chat_session(agent.name)
    print("\nChat session started. Type 'quit' to end the session.")
    print("-" * 50)

    try:
        while True:
            user_input = input("\nYou: ").strip()

            if user_input.lower() in ["quit", "exit", "bye"]:
                break

            if user_input.startswith("addmem:"):
                memory_content = user_input[7:].strip()
                await agent_chat.send_message(
                    session_id, memory_content, add_to_memory=True
                )
                print("Memory added.")
                continue

            response = await agent_chat.send_message(session_id, user_input)
            print(f"\nAssistant: {response.content}")

    finally:
        await agent_chat.end_chat_session(session_id)
        print("\nChat session ended")


def main():
    parser = argparse.ArgumentParser(description="Interactive Agent Chat CLI")
    args = parser.parse_args()

    asyncio.run(interactive_chat())


if __name__ == "__main__":
    main()
