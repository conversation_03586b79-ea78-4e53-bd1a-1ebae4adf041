"""
Integration Tests for Loop Executor.

This module tests integration between LoopExecutor and other components
including WorkflowEngine, state persistence, and transition routing.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from app.services.loop_executor.loop_executor import <PERSON><PERSON>xecutor
from app.services.loop_executor.loop_state_manager import LoopStateManager


class TestLoopExecutorIntegration:
    """Test integration between LoopExecutor and other components."""

    @pytest.fixture
    def mock_state_manager(self):
        """Create a mock state manager."""
        state_manager = Mock()
        state_manager.workflow_id = "test_workflow_123"
        state_manager.store_loop_state = AsyncMock()
        state_manager.load_loop_state = AsyncMock()
        state_manager.save_workflow_state = AsyncMock()
        return state_manager

    @pytest.fixture
    def mock_transition_handler(self):
        """Create a mock transition handler."""
        handler = Mock()
        handler.execute_transition = AsyncMock(return_value={"status": "completed", "result": "test_result"})
        return handler

    @pytest.fixture
    def loop_executor(self, mock_state_manager, mock_transition_handler):
        """Create a LoopExecutor instance for testing."""
        workflow_utils = Mock()
        result_callback = Mock()
        transitions_by_id = {
            "loop_body_1": {"id": "loop_body_1", "type": "tool"},
            "loop_body_2": {"id": "loop_body_2", "type": "tool"}
        }
        nodes = {"test_node": {"id": "test_node"}}
        
        return LoopExecutor(
            state_manager=mock_state_manager,
            workflow_utils=workflow_utils,
            result_callback=result_callback,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            transition_handler=mock_transition_handler,
        )

    @pytest.mark.asyncio
    async def test_loop_execution_with_workflow_engine(self, loop_executor, mock_state_manager):
        """Test loop execution integration with workflow engine."""
        loop_config = {
            "loop_type": "context_preserving",
            "aggregation_config": {"type": "list"},
            "iteration_source": {
                "type": "list",
                "data": ["item1", "item2", "item3"]
            },
            "loop_body_transitions": ["loop_body_1"],
            "exit_transition": "next_transition"
        }

        # Mock the execute_single_iteration to return test results
        async def mock_execute_iteration(index, item):
            return {"iteration_index": index, "item": item, "status": "completed"}
        
        loop_executor.execute_single_iteration = mock_execute_iteration

        result = await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="test_loop_transition"
        )

        # Verify results
        assert isinstance(result, list)
        assert len(result) == 3
        
        # Verify state manager interactions
        mock_state_manager.store_loop_state.assert_called()
        mock_state_manager.save_workflow_state.assert_called()

    @pytest.mark.asyncio
    async def test_loop_state_persistence_and_recovery(self, loop_executor, mock_state_manager):
        """Test loop state persistence and recovery mechanisms."""
        # Setup mock state data
        mock_loop_state = {
            "loop_id": "test_loop_123",
            "transition_id": "test_transition",
            "current_iteration_index": 2,
            "total_iterations": 5,
            "iteration_results": {0: "result1", 1: "result2"},
            "loop_status": "running"
        }
        
        mock_state_manager.load_loop_state.return_value = mock_loop_state

        # Initialize loop state
        await loop_executor.initialize_loop_state("test_transition")
        
        # Verify state manager was called for storage
        mock_state_manager.store_loop_state.assert_called()
        
        # Test state recovery
        loop_state_manager = loop_executor.loop_state_manager
        assert loop_state_manager is not None
        assert loop_state_manager.transition_id == "test_transition"

    @pytest.mark.asyncio
    async def test_loop_result_aggregation_integration(self, loop_executor):
        """Test integration of loop result aggregation with different strategies."""
        test_cases = [
            {
                "aggregation_type": "list",
                "expected_type": list,
                "data": ["a", "b", "c"]
            },
            {
                "aggregation_type": "object", 
                "expected_type": dict,
                "data": [1, 2, 3]
            },
            {
                "aggregation_type": "concatenate",
                "expected_type": str,
                "data": ["hello", "world"]
            }
        ]

        for case in test_cases:
            loop_config = {
                "loop_type": "context_independent",
                "aggregation_config": {"type": case["aggregation_type"]},
                "iteration_source": {
                    "type": "list",
                    "data": case["data"]
                },
                "loop_body_transitions": ["loop_body_1"]
            }

            # Mock iteration execution
            async def mock_execute_iteration(index, item):
                return {"result": item, "status": "completed"}
            
            loop_executor.execute_single_iteration = mock_execute_iteration

            result = await loop_executor.execute_tool(
                loop_config=loop_config,
                transition_id="test_transition"
            )

            assert isinstance(result, case["expected_type"])

    @pytest.mark.asyncio
    async def test_concurrent_loop_execution_integration(self, loop_executor):
        """Test concurrent loop execution integration."""
        loop_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "list"},
            "iteration_source": {
                "type": "range",
                "data": {"start": 0, "stop": 10, "step": 1}
            },
            "loop_body_transitions": ["loop_body_1"],
            "concurrency": {
                "enabled": True,
                "max_concurrent": 3,
                "preserve_order": True
            }
        }

        # Mock concurrent iteration execution
        async def mock_execute_iteration(index, item):
            await asyncio.sleep(0.01)  # Simulate work
            return {"iteration_index": index, "value": item * 2, "status": "completed"}
        
        loop_executor.execute_single_iteration = mock_execute_iteration

        result = await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="test_concurrent_loop"
        )

        # Verify concurrent execution completed
        assert isinstance(result, list)
        assert len(result) == 10
        
        # Verify order preservation
        for i, item in enumerate(result):
            assert item["iteration_index"] == i

    @pytest.mark.asyncio
    async def test_loop_transition_routing_integration(self, loop_executor, mock_transition_handler):
        """Test loop transition routing integration with TransitionHandler."""
        loop_config = {
            "loop_type": "context_preserving",
            "aggregation_config": {"type": "object"},
            "iteration_source": {
                "type": "list",
                "data": ["test1", "test2"]
            },
            "loop_body_transitions": ["loop_body_1", "loop_body_2"],
            "exit_transition": "next_step"
        }

        # Test that loop body transitions are properly identified
        assert "loop_body_1" in loop_executor.transitions_by_id
        assert "loop_body_2" in loop_executor.transitions_by_id

        # Mock transition execution
        mock_transition_handler.execute_transition.return_value = {
            "status": "completed",
            "result": "transition_result"
        }

        # Execute loop
        result = await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="test_routing_loop"
        )

        # Verify execution completed
        assert isinstance(result, dict)

    @pytest.mark.asyncio
    async def test_loop_error_propagation_integration(self, loop_executor):
        """Test loop error propagation to workflow engine."""
        loop_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "list"},
            "iteration_source": {
                "type": "list",
                "data": ["item1", "item2", "item3"]
            },
            "loop_body_transitions": ["loop_body_1"]
        }

        # Mock iteration that fails on second item
        async def mock_failing_iteration(index, item):
            if index == 1:
                raise ValueError(f"Test error for item: {item}")
            return {"iteration_index": index, "item": item, "status": "completed"}
        
        loop_executor.execute_single_iteration = mock_failing_iteration

        # Execute and expect error handling
        result = await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="test_error_loop"
        )

        # Verify error was handled and loop continued
        assert isinstance(result, list)
        # Should have results for items 0 and 2, with error result for item 1
        assert len(result) == 3

    @pytest.mark.asyncio
    async def test_loop_state_cleanup_integration(self, loop_executor, mock_state_manager):
        """Test loop state cleanup integration."""
        loop_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "list"},
            "iteration_source": {
                "type": "list",
                "data": ["item1"]
            },
            "loop_body_transitions": ["loop_body_1"]
        }

        # Mock iteration execution
        async def mock_execute_iteration(index, item):
            return {"result": item, "status": "completed"}
        
        loop_executor.execute_single_iteration = mock_execute_iteration

        # Execute loop
        await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="test_cleanup_loop"
        )

        # Verify state was cleaned up
        assert loop_executor.current_loop_config is None
        assert loop_executor.current_iteration_data == []
        assert loop_executor.iteration_results == {}
        assert loop_executor.loop_context == {}
        assert loop_executor.loop_state_manager is None

    @pytest.mark.asyncio
    async def test_loop_context_preservation_integration(self, loop_executor):
        """Test loop context preservation across iterations."""
        loop_config = {
            "loop_type": "context_preserving",
            "aggregation_config": {"type": "list"},
            "iteration_source": {
                "type": "list",
                "data": [1, 2, 3]
            },
            "loop_body_transitions": ["loop_body_1"]
        }

        # Track context across iterations
        iteration_contexts = []

        async def mock_context_preserving_iteration(index, item):
            context = loop_executor.create_iteration_context(index, item)
            iteration_contexts.append(context)
            return {"iteration_index": index, "item": item, "context": context}
        
        loop_executor.execute_single_iteration = mock_context_preserving_iteration

        await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="test_context_loop"
        )

        # Verify context preservation
        assert len(iteration_contexts) == 3
        
        # Each iteration should have access to previous results
        for i, context in enumerate(iteration_contexts):
            assert context["iteration_index"] == i
            assert context["current_item"] == i + 1
            if i > 0:
                assert "previous_results" in context
                assert len(context["previous_results"]) == i

    @pytest.mark.asyncio
    async def test_loop_performance_monitoring_integration(self, loop_executor):
        """Test loop performance monitoring integration."""
        loop_config = {
            "loop_type": "context_independent",
            "aggregation_config": {"type": "list"},
            "iteration_source": {
                "type": "range",
                "data": {"start": 0, "stop": 5, "step": 1}
            },
            "loop_body_transitions": ["loop_body_1"],
            "performance": {
                "enable_profiling": True,
                "memory_efficient": True
            }
        }

        # Mock iteration with small delay
        async def mock_timed_iteration(index, item):
            await asyncio.sleep(0.001)  # 1ms delay
            return {"iteration_index": index, "value": item, "status": "completed"}
        
        loop_executor.execute_single_iteration = mock_timed_iteration

        # Execute with profiling
        result = await loop_executor.execute_tool(
            loop_config=loop_config,
            transition_id="test_performance_loop"
        )

        # Verify execution completed
        assert isinstance(result, list)
        assert len(result) == 5
