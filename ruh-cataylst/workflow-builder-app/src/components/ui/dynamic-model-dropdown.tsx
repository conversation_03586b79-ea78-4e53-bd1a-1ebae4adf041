/**
 * Dynamic Model Dropdown Component
 * 
 * This component provides a model dropdown that dynamically filters models
 * based on the selected provider.
 */

import React, { useState, useEffect, useMemo } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { fetchModelsByProvider } from '@/lib/api';
import type { Model } from '@/lib/api';

interface DynamicModelDropdownProps {
  value: string;
  onChange: (value: string) => void;
  selectedProvider?: string;
  providerIdMapping?: Record<string, {
    providerId: string;
    providerName: string;
    isActive: boolean;
  }>;
  allModels?: string[];
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

export function DynamicModelDropdown({
  value,
  onChange,
  selectedProvider,
  providerIdMapping,
  allModels = [],
  disabled = false,
  placeholder = "Select a model...",
  className
}: DynamicModelDropdownProps) {
  const [loading, setLoading] = useState(false);
  const [dynamicModels, setDynamicModels] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Get available models based on selected provider
  const availableModels = useMemo(() => {
    if (!selectedProvider || selectedProvider === 'Custom') {
      // If no provider selected or Custom, show fallback models
      return allModels;
    }

    // Use dynamically fetched models
    return dynamicModels;
  }, [selectedProvider, allModels, dynamicModels]);

  // Fetch models dynamically when provider changes
  useEffect(() => {
    if (!selectedProvider || selectedProvider === 'Custom') {
      setDynamicModels([]);
      setError(null);
      return;
    }

    // Always fetch models dynamically when provider changes

    // Fetch models dynamically
    const fetchModels = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const providerId = providerIdMapping?.[selectedProvider]?.providerId;
        if (!providerId) {
          console.warn(`No provider ID found for provider: ${selectedProvider}`);
          setDynamicModels([]);
          return;
        }

        console.log(`Fetching models for provider: ${selectedProvider} (ID: ${providerId})`);
        const response = await fetchModelsByProvider(providerId);
        
        if (response.success && response.models) {
          const modelNames = response.models
            .filter(model => model.isActive)
            .map(model => model.model)
            .sort();
          
          setDynamicModels(modelNames);
          console.log(`Fetched ${modelNames.length} models for provider ${selectedProvider}`);
        } else {
          console.warn(`Failed to fetch models for provider ${selectedProvider}:`, response.message);
          setDynamicModels([]);
        }
      } catch (err) {
        console.error(`Error fetching models for provider ${selectedProvider}:`, err);
        setError(err instanceof Error ? err.message : 'Failed to fetch models');
        setDynamicModels([]);
      } finally {
        setLoading(false);
      }
    };

    fetchModels();
  }, [selectedProvider, providerIdMapping]);

  // Reset model selection if current value is not in available models
  useEffect(() => {
    if (value && availableModels.length > 0 && !availableModels.includes(value)) {
      console.log(`Current model "${value}" not available for provider "${selectedProvider}", resetting to first available model`);
      onChange(availableModels[0]);
    }
  }, [value, availableModels, selectedProvider, onChange]);

  // Show loading state
  if (loading) {
    return (
      <Select disabled>
        <SelectTrigger className={className}>
          <SelectValue placeholder="Loading models..." />
        </SelectTrigger>
      </Select>
    );
  }

  // Show error state
  if (error) {
    return (
      <Select disabled>
        <SelectTrigger className={className}>
          <SelectValue placeholder={`Error: ${error}`} />
        </SelectTrigger>
      </Select>
    );
  }

  // Show empty state when no provider selected
  if (!selectedProvider) {
    return (
      <Select disabled>
        <SelectTrigger className={className}>
          <SelectValue placeholder="Select a provider first" />
        </SelectTrigger>
      </Select>
    );
  }

  // Show no models available
  if (availableModels.length === 0) {
    return (
      <Select disabled>
        <SelectTrigger className={className}>
          <SelectValue placeholder="No models available" />
        </SelectTrigger>
      </Select>
    );
  }

  return (
    <Select
      value={value}
      onValueChange={onChange}
      disabled={disabled}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder}>
          {value || placeholder}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {availableModels.map((model) => (
          <SelectItem key={model} value={model}>
            <div className="flex flex-col">
              <span>{model}</span>
              {selectedProvider && selectedProvider !== 'Custom' && (
                <span className="text-xs text-muted-foreground">
                  {selectedProvider}
                </span>
              )}
            </div>
          </SelectItem>
        ))}
        
        {/* Show count info */}
        <div className="px-2 py-1 text-xs text-muted-foreground border-t">
          {availableModels.length} model{availableModels.length !== 1 ? 's' : ''} available
          {selectedProvider && selectedProvider !== 'Custom' && (
            <span> for {selectedProvider}</span>
          )}
        </div>
      </SelectContent>
    </Select>
  );
}

/**
 * Hook to get models for a specific provider
 */
export function useModelsForProvider(
  providerName: string,
  providerIdMapping?: Record<string, { providerId: string; providerName: string; isActive: boolean; }>
) {
  const [models, setModels] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!providerName || providerName === 'Custom') {
      setModels([]);
      return;
    }

    // Fetch dynamically
    const fetchModels = async () => {
      setLoading(true);
      setError(null);

      try {
        const providerId = providerIdMapping?.[providerName]?.providerId;
        if (!providerId) {
          setModels([]);
          return;
        }

        const response = await fetchModelsByProvider(providerId);
        if (response.success && response.models) {
          const modelNames = response.models
            .filter(model => model.isActive)
            .map(model => model.model)
            .sort();
          setModels(modelNames);
        } else {
          setModels([]);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch models');
        setModels([]);
      } finally {
        setLoading(false);
      }
    };

    fetchModels();
  }, [providerName, providerIdMapping]);

  return { models, loading, error };
}
