#!/usr/bin/env python3
"""
Test script to verify the loop body transitions fix.
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.loop_executor.loop_executor import LoopExecutor
from unittest.mock import Mock, AsyncMock
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_loop_body_transitions_conversion():
    """Test that loop_body_transitions are properly converted to entry/exit transitions."""
    
    # Create mock dependencies
    mock_state_manager = Mock()
    mock_transition_handler = AsyncMock()
    mock_workflow_utils = Mock()
    mock_result_callback = Mock()
    mock_transitions_by_id = {
        "transition-CombineTextComponent-1750769520925": {
            "id": "transition-CombineTextComponent-1750769520925",
            "execution_type": "Components"
        }
    }
    mock_nodes = {
        "CombineTextComponent": {
            "id": "CombineTextComponent",
            "type": "component"
        }
    }

    # Create loop executor
    loop_executor = LoopExecutor(
        state_manager=mock_state_manager,
        transition_handler=mock_transition_handler,
        workflow_utils=mock_workflow_utils,
        result_callback=mock_result_callback,
        transitions_by_id=mock_transitions_by_id,
        nodes=mock_nodes
    )
    
    # Test configuration with loop_body_transitions and empty loop_body_configuration
    loop_config = {
        "iteration_behavior": "independent",
        "iteration_source": {
            "number_range": {"start": 1, "end": 3},
            "step": 1
        },
        "exit_condition": {
            "condition_type": "all_items_processed"
        },
        "loop_body_configuration": {
            "entry_transitions": [],
            "exit_transitions": [],
            "chain_completion_detection": "explicit_exit_transitions"
        },
        "loop_body_transitions": ["transition-CombineTextComponent-1750769520925"]
    }
    
    # Parse the configuration
    parsed_config = loop_executor.parse_loop_config(loop_config)
    loop_executor.current_loop_config = parsed_config
    
    print("=== TESTING LOOP BODY TRANSITIONS CONVERSION ===")
    print(f"Original loop_body_transitions: {loop_config.get('loop_body_transitions')}")
    print(f"Original entry_transitions: {loop_config['loop_body_configuration']['entry_transitions']}")
    print(f"Original exit_transitions: {loop_config['loop_body_configuration']['exit_transitions']}")
    
    # Test the _execute_loop_body_chain method to see if it converts properly
    try:
        # This should trigger the conversion logic
        result = await loop_executor._execute_loop_body_chain(0, 1, {})
        print(f"✅ Loop body chain execution completed with result: {result}")
    except Exception as e:
        print(f"❌ Loop body chain execution failed: {str(e)}")
        # This is expected since we don't have real dependencies, but we should see the conversion logs
    
    print("\n=== CHECKING PARSED CONFIGURATION ===")
    print(f"Parsed config has loop_body_transitions: {'loop_body_transitions' in parsed_config}")
    if 'loop_body_transitions' in parsed_config:
        print(f"Parsed loop_body_transitions: {parsed_config['loop_body_transitions']}")
    
    print("\n=== TEST COMPLETED ===")

if __name__ == "__main__":
    asyncio.run(test_loop_body_transitions_conversion())
