import asyncio
import base64
import json
import logging
import os
import sys
import uuid
from typing import Any, Dict, List

from aiokafka import AIOKafkaConsumer, AIOKafkaProducer  # type: ignore

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


KAFKA_BOOTSTRAP_SERVERS = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "34.172.106.233:9092")
KAFKA_AGENT_CREATION_TOPIC = "agent_creation_requests"
KAFKA_AGENT_CHAT_TOPIC = "agent_chat_requests"
KAFKA_AGENT_RESPONSE_TOPIC = "agent_chat_responses"


def create_sample_image_attachment(use_url: bool = True) -> Dict[str, Any]:
    """Create a sample image attachment for testing"""
    if use_url:
        return {
            "file_name": "sample_image.jpeg",
            "file_type": "image/jpeg",
            "file_size": 0,  # Size will be determined when downloaded
            "file_url": "https://images.pexels.com/photos/30091614/pexels-photo-30091614.jpeg",
            "metadata": {"description": "Sample image for multimodal chat test"},
        }
    else:
        # Create a simple base64 encoded image (1x1 pixel red PNG)
        red_pixel_png = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="

        return {
            "file_name": "red_pixel.png",
            "file_type": "image/png",
            "file_size": len(base64.b64decode(red_pixel_png)),
            "file_data": red_pixel_png,
            "metadata": {"description": "A simple 1x1 red pixel for testing"},
        }


def create_sample_text_attachment() -> Dict[str, Any]:
    """Create a sample text document attachment for testing"""
    text_content = """
    # Agent Chat Test Document

    This document is used to test the multimodal capabilities of the agent chat system.

    ## Key Features Being Tested:
    - Image processing and analysis
    - Document understanding and summarization
    - Multimodal conversation handling

    ## Expected Behavior:
    1. The agent should be able to process both text and attached files
    2. Responses should reference content from attachments
    3. Conversation context should be maintained

    This is a test of the multimodal agent chat system.
    """

    base64_data = base64.b64encode(text_content.encode("utf-8")).decode("utf-8")

    return {
        "file_name": "agent_chat_test_doc.md",
        "file_type": "text/markdown",
        "file_size": len(text_content.encode("utf-8")),
        "file_data": base64_data,
        "metadata": {"description": "Test document for multimodal agent chat"},
    }


def parse_multimodal_input(user_input: str) -> tuple[str, List[Dict[str, Any]]]:
    """
    Parse user input for multimodal commands and return message and attachments.

    Commands:
    - /image - Add sample image attachment
    - /doc - Add sample document attachment
    - /both - Add both image and document attachments
    """
    attachments = []
    message = user_input

    if "/image" in user_input:
        attachments.append(create_sample_image_attachment())
        message = user_input.replace("/image", "").strip()
        if not message:
            message = "Please analyze the attached image."

    if "/doc" in user_input:
        attachments.append(create_sample_text_attachment())
        message = user_input.replace("/doc", "").strip()
        if not message:
            message = "Please summarize the attached document."

    if "/both" in user_input:
        attachments.append(create_sample_image_attachment())
        attachments.append(create_sample_text_attachment())
        message = user_input.replace("/both", "").strip()
        if not message:
            message = "Please analyze the attached image and document."

    return message, attachments


class KafkaTestClient:
    def __init__(self):
        self.producer = None
        self.consumer = None
        self._initialized = False

    async def initialize(self):
        if self._initialized:
            return

        self.producer = AIOKafkaProducer(
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            max_request_size=524288000,
        )
        self.consumer = AIOKafkaConsumer(
            KAFKA_AGENT_RESPONSE_TOPIC,
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            group_id=f"test-group-{uuid.uuid4()}",
            auto_offset_reset="latest",
            enable_auto_commit=True,
        )

        await self.producer.start()
        await self.consumer.start()
        self._initialized = True
        logger.info("Kafka client initialized successfully")

    async def cleanup(self):
        if self.producer:
            await self.producer.stop()
        if self.consumer:
            await self.consumer.stop()

    async def send_message(
        self, topic: str, message: Dict[str, Any], headers: list = None
    ) -> None:
        try:
            value = json.dumps(message).encode("utf-8")
            await self.producer.send_and_wait(topic, value=value, headers=headers)
            logger.info(f"Message sent to topic {topic}: {message}")
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise

    async def listen_for_responses(self, correlation_id: str = None, timeout: int = 30):
        """Listen for responses with improved debugging and error handling"""
        try:
            start_time = asyncio.get_event_loop().time()
            responses = []
            logger.info(
                f"Starting to listen for responses with correlation_id: {correlation_id}"
            )

            while True:
                try:
                    # Set a timeout for each message poll
                    msg = await asyncio.wait_for(
                        self.consumer.getone(), timeout=timeout
                    )

                    current_time = asyncio.get_event_loop().time()
                    if current_time - start_time > timeout:
                        logger.warning("Response listening timeout reached")
                        break

                    # Log raw message for debugging
                    logger.debug(f"Received raw message: {msg}")

                    # Process message headers
                    msg_correlation_id = None
                    if msg.headers:
                        headers_dict = {
                            k: v.decode("utf-8") if isinstance(v, bytes) else v
                            for k, v in msg.headers
                        }
                        msg_correlation_id = headers_dict.get("correlationId")
                        logger.debug(f"Message headers: {headers_dict}")

                    # Decode and process message value
                    try:
                        response = json.loads(msg.value.decode("utf-8"))
                        logger.debug(f"Decoded response: {response}")

                        # Check correlation ID match
                        if correlation_id and msg_correlation_id == correlation_id:
                            logger.info(
                                f"Matched response for correlation_id {correlation_id}"
                            )
                            responses.append(response)

                            # Check if this is a final response
                            if "agent_response" in response and response.get(
                                "final", True
                            ):
                                logger.info("Received final response, breaking loop")
                                break

                            # For agent creation response
                            if "session_id" in response:
                                logger.info(
                                    "Received session_id in response, breaking loop"
                                )
                                break
                        else:
                            logger.debug(
                                f"Skipping message with different correlation_id: {msg_correlation_id}"
                            )

                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to decode message value: {e}")
                        continue

                except asyncio.TimeoutError:
                    logger.warning("Timeout waiting for message")
                    break
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    continue

            logger.info(f"Finished listening. Collected {len(responses)} responses")
            return responses

        except Exception as e:
            logger.error(f"Error in listen_for_responses: {e}")
            raise

    async def listen_for_streaming_responses(
        self,
        correlation_id: str = None,
        timeout: int = 60,
    ):
        """Listen for streaming responses and yield single chunks as they arrive"""
        try:
            start_time = asyncio.get_event_loop().time()
            logger.info(
                f"Starting to listen for streaming responses with correlation_id: {correlation_id}"
            )

            while True:
                try:
                    # Set a timeout for each message poll
                    msg = await asyncio.wait_for(
                        self.consumer.getone(),
                        timeout=timeout,  # Shorter timeout for streaming
                    )

                    current_time = asyncio.get_event_loop().time()
                    if current_time - start_time > timeout:
                        logger.warning("Streaming response timeout reached")
                        break

                    # Process message headers
                    msg_correlation_id = None
                    if msg.headers:
                        headers_dict = {
                            k: v.decode("utf-8") if isinstance(v, bytes) else v
                            for k, v in msg.headers
                        }
                        msg_correlation_id = headers_dict.get("correlationId")

                    # Decode and process message value
                    try:
                        response = json.loads(msg.value.decode("utf-8"))

                        # Check correlation ID match
                        if correlation_id and msg_correlation_id == correlation_id:
                            chunk_id = response.get("stream_chunk_id", 0)
                            is_final = response.get("final", False)
                            success = response.get("success", False)
                            agent_response = response.get("agent_response", {})

                            # Log chunk info in debug mode
                            if success and agent_response.get("content"):
                                logger.debug(
                                    f"📦 Chunk {chunk_id}: {len(agent_response['content'])} chars"
                                )

                            # Yield the chunk immediately
                            yield {
                                "chunk_id": chunk_id,
                                "content": agent_response.get("content", ""),
                                "is_final": is_final,
                                "success": success,
                                "error": response.get("error"),
                                "full_response": response,
                            }

                            # If this is the final chunk, break
                            if is_final:
                                logger.info(
                                    f"✅ Streaming complete - final chunk {chunk_id}"
                                )
                                break

                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to decode message value: {e}")
                        continue

                except asyncio.TimeoutError:

                    logger.debug("⏰ Timeout waiting for first chunk")

                    break
                except Exception as e:
                    logger.error(f"Error processing streaming message: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error in listen_for_streaming_responses: {e}")
            raise


async def create_agent() -> str:
    """Create a new agent and return the session ID"""
    client = KafkaTestClient()
    await client.initialize()

    try:
        run_id = str(uuid.uuid4())
        creation_request = {
            "agent_id": "26b1fda5-2e28-403d-8cf8-8c08e03436c6",
            "user_id": "d962b421-66b5-44f5-bfc0-5425c88c9c04",
            "communication_type": "single",
            "run_id": run_id,
            "agent_group_id": None,
            "use_knowledge": True,
            "organization_id": "e8d7c29e-11d1-4f71-8541-cb4580d600b5",
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        logger.info(f"Creating new agent with run_id: {run_id}")
        await client.send_message(KAFKA_AGENT_CREATION_TOPIC, creation_request, headers)

        # Wait for response with increased timeout
        responses = await client.listen_for_responses(correlation_id=run_id, timeout=60)

        if not responses:
            raise Exception("No response received for agent creation")

        logger.info(f"Received responses: {responses}")

        session_id = responses[0].get("session_id")
        if not session_id:
            raise Exception("No session ID in response")

        logger.info(f"Agent created successfully with session ID: {session_id}")
        return session_id

    finally:
        await client.cleanup()


async def chat_with_agent(session_id: str):
    """Interactive chat session with the agent using streaming responses"""
    client = KafkaTestClient()
    await client.initialize()

    try:
        print("\n🚀 Agent CLI Chat - Streaming Mode")
        print("💡 Agent responses will appear in real-time as they're generated")
        print("📝 Type 'quit', 'exit', or 'bye' to end the chat session")
        print("🎯 Multimodal commands:")
        print("   /image - Add sample image attachment")
        print("   /doc - Add sample document attachment")
        print("   /both - Add both image and document attachments")
        print("🤖 Connected to AI Agent - Ready to assist!")
        print("=" * 70)

        while True:
            # Get user input
            user_input = input("\nYou: ").strip()
            if user_input.lower() in ["quit", "exit", "bye"]:
                break

            if not user_input:
                continue

            # Parse multimodal input
            message, attachments = parse_multimodal_input(user_input)

            # Prepare chat request
            request_id = str(uuid.uuid4())

            chat_request = {
                "run_id": request_id,
                "session_id": session_id,
                "chat_context": [{"role": "user", "content": message}],
                "chat_response": "stream",
                "attachments": attachments,
            }

            # Log attachment info if present
            if attachments:
                attachment_summary = ", ".join(
                    [f"{att['file_name']} ({att['file_type']})" for att in attachments]
                )
                print(f"📎 Attachments: {attachment_summary}")

            headers = [
                ("correlationId", request_id.encode("utf-8")),
                ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
            ]

            # Send chat request
            await client.send_message(KAFKA_AGENT_CHAT_TOPIC, chat_request, headers)

            # Process streaming responses in real-time
            print("🔄 Sending request and waiting for response...")
            print("\n🤖 Agent: ", end="", flush=True)

            # Show typing indicator while waiting for first chunk
            print("⏳ ", end="", flush=True)
            first_chunk_received = False
            complete_response = ""

            async for chunk in client.listen_for_streaming_responses(
                correlation_id=request_id,
                timeout=60,
            ):
                # Clear typing indicator on first chunk
                if not first_chunk_received:
                    print("\b\b  \b\b", end="", flush=True)  # Clear typing indicator
                    first_chunk_received = True

                # Handle successful content chunks
                if chunk["success"] and chunk["content"] and not chunk["is_final"]:
                    content = chunk["content"]
                    print(f"\n🤖 Agent: {content}", end="", flush=True)
                    complete_response += content

                # Handle error chunks
                elif not chunk["success"] and not chunk["is_final"]:
                    error_msg = chunk["error"] or "Unknown error"
                    print(f"\n❌ [Agent Error: {error_msg}]", end="", flush=True)

                # Handle final chunk
                if chunk["is_final"]:
                    print()  # New line after response is complete
                    if complete_response.strip():
                        logger.info(
                            f"✅ Agent response completed: {len(complete_response)} characters"
                        )
                    break

            # If no chunks were received, clear typing indicator
            if not first_chunk_received:
                print("\b\b  \b\b", end="", flush=True)
                print("❌ No response received from agent")
                logger.warning("No streaming response received from agent")

    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise
    finally:
        await client.cleanup()


async def chat_with_agent_non_streaming(session_id: str):
    """Interactive chat session with the agent using non-streaming responses (fallback)"""
    client = KafkaTestClient()
    await client.initialize()

    try:
        print("\n🤖 Agent CLI Chat - Standard Mode")
        print("📝 Type 'quit', 'exit', or 'bye' to end the chat session")
        print("🎯 Multimodal commands:")
        print("   /image - Add sample image attachment")
        print("   /doc - Add sample document attachment")
        print("   /both - Add both image and document attachments")
        print("🤖 Connected to AI Agent - Ready to assist!")
        print("-" * 70)

        while True:
            # Get user input
            user_input = input("\nYou: ").strip()
            if user_input.lower() in ["quit", "exit", "bye"]:
                break

            if not user_input:
                continue

            # Parse multimodal input
            message, attachments = parse_multimodal_input(user_input)

            # Prepare chat request
            request_id = str(uuid.uuid4())
            chat_request = {
                "run_id": request_id,
                "session_id": session_id,
                "chat_context": [{"role": "user", "content": message}],
                "chat_response": "message",
                "attachments": attachments,
            }

            # Log attachment info if present
            if attachments:
                attachment_summary = ", ".join(
                    [f"{att['file_name']} ({att['file_type']})" for att in attachments]
                )
                print(f"📎 Attachments: {attachment_summary}")

            headers = [
                ("correlationId", request_id.encode("utf-8")),
                ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
            ]

            # Send chat request
            await client.send_message(KAFKA_AGENT_CHAT_TOPIC, chat_request, headers)

            # Wait for and process responses
            responses = await client.listen_for_responses(correlation_id=request_id)

            if responses:
                for response in responses:
                    # Extract clean content from the response
                    agent_response = response.get("agent_response", {})
                    content = agent_response.get("content", "")
                    if content:
                        print(f"\n🤖 Agent: {content}")
                        logger.info(
                            f"✅ Agent response received: {len(content)} characters"
                        )
            else:
                print("\n❌ No response received from agent")
                logger.warning("No response received from agent")

    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise
    finally:
        await client.cleanup()


async def main():
    try:
        # Create agent and get session ID
        session_id = await create_agent()

        print(f"Session ID: {session_id}")

        # Ask user for chat mode preference (default to streaming)
        print("\n🎯 Choose chat mode:")
        print("1. 🚀 Streaming chat (real-time responses) [DEFAULT]")
        print("2. 📄 Non-streaming chat (traditional responses)")
        print("3. ❓ Help & Commands")

        while True:
            choice = input(
                "\nEnter your choice (1, 2, 3, or press Enter for default): "
            ).strip()
            if choice == "1" or choice == "":
                # Start streaming interactive chat (default)
                await chat_with_agent(session_id)
                break
            elif choice == "2":
                # Start non-streaming interactive chat
                await chat_with_agent_non_streaming(session_id)
                break
            elif choice == "3":
                # Help
                print("\n📖 Help & Commands:")
                print("=" * 40)
                print("Multimodal Chat Commands:")
                print("  /image - Add sample image attachment")
                print("  /doc - Add sample document attachment")
                print("  /both - Add both image and document attachments")
                print("  quit/exit/bye - End chat session")
                print("\nExample usage:")
                print("  'Analyze this data /image'")
                print("  'Summarize the document /doc'")
                print("  'Compare image and document /both'")
                print("\nFeatures:")
                print("  ✅ Multimodal support (images, documents)")
                print("  ✅ Real-time streaming responses")
                print("  ✅ Session management")
                print("  ✅ Error handling and recovery")
                continue
            else:
                print("❌ Invalid choice. Please enter 1, 2, 3, or press Enter.")

    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
