"""
Service for managing knowledge sources for AutoGen agents.
"""

import asyncio
import logging
from contextlib import AsyncExitStack
from typing import Any, Dict, Optional
from urllib.parse import urlparse

import httpx
from autogen_agentchat.agents import AssistantAgent

# Import MCP-related modules for SSE client functionality
from mcp.client.session import ClientSession
from mcp.client.sse import sse_client

from ..autogen_service.model_factory import ModelFactory
from ..knowledge.knowledge_manager import KnowledgeManager
from ..schemas.agent_config import AgentConfig

logger = logging.getLogger(__name__)

# HTTP error status code threshold
HTTP_ERROR_STATUS_CODE = 400


class KnowledgeService:
    """Service for managing knowledge sources for AutoGen agents."""

    def __init__(self):
        self.knowledge_managers = {}  # Map of agent_id -> KnowledgeManager

    async def create_knowledge_manager(
        self, agent_id: str, collection_name: Optional[str] = None
    ) -> KnowledgeManager:
        """
        Create a knowledge manager for an agent.

        Args:
            agent_id: ID of the agent
            collection_name: Optional name for the collection

        Returns:
            The created KnowledgeManager
        """
        if agent_id in self.knowledge_managers:
            return self.knowledge_managers[agent_id]

        # Use agent_id as collection name if not provided
        if collection_name is None:
            collection_name = f"knowledge_{agent_id}"

        # Create knowledge manager
        knowledge_manager = KnowledgeManager(collection_name=collection_name)
        self.knowledge_managers[agent_id] = knowledge_manager

        return knowledge_manager

    async def add_knowledge_to_agent(
        self, agent_id: str, agent: AssistantAgent, knowledge_sources: Dict[str, Any]
    ) -> AssistantAgent:
        """
        Add knowledge to an agent from various sources.

        Args:
            agent_id: ID of the agent
            agent: The AssistantAgent instance
            knowledge_sources: Dictionary with keys:
                - 'texts': List of text strings
                - 'urls': List of website URLs
                - 'documents': List of document paths

        Returns:
            The enhanced agent
        """
        # Get or create knowledge manager
        if agent_id in self.knowledge_managers:
            knowledge_manager = self.knowledge_managers[agent_id]
        else:
            knowledge_manager = await self.create_knowledge_manager(agent_id)

        # Add texts
        if "texts" in knowledge_sources and knowledge_sources["texts"]:
            for i, text in enumerate(knowledge_sources["texts"]):
                try:
                    await knowledge_manager.add_text(text, f"text_input_{i}")
                except Exception as e:
                    logger.error(f"Error adding text {i}: {str(e)}")

        # Add URLs
        if "urls" in knowledge_sources and knowledge_sources["urls"]:
            for url in knowledge_sources["urls"]:
                try:
                    await knowledge_manager.add_website(url)
                except Exception as e:
                    logger.error(f"Error adding URL {url}: {str(e)}")

        # Add documents
        if "documents" in knowledge_sources and knowledge_sources["documents"]:
            for doc_path in knowledge_sources["documents"]:
                try:
                    await knowledge_manager.add_document(doc_path)
                except Exception as e:
                    logger.error(f"Error adding document {doc_path}: {str(e)}")

        # Enhance agent with knowledge
        return await knowledge_manager.enhance_agent_with_knowledge(agent)

    async def create_knowledgeable_agent(
        self, config: AgentConfig, knowledge_sources: Dict[str, Any]
    ) -> AssistantAgent:
        """
        Create a new agent with knowledge from various sources.

        Args:
            config: Agent configuration
            knowledge_sources: Dictionary with knowledge sources

        Returns:
            The created agent with knowledge
        """
        # Create model client from config
        model_config = {
            "provider": "OpenAIChatCompletionClient",
            "model": config.ai_model_config["model"],
            "api_key": config.ai_model_config.get("api_key"),
        }

        # Add optional parameters if present
        for param in ["temperature", "max_tokens"]:
            if param in config.ai_model_config:
                model_config[param] = config.ai_model_config[param]

        model_client = ModelFactory.create_model_client(model_config)

        if not model_client:
            raise ValueError("Failed to create model client")

        # Create agent without knowledge first
        agent = AssistantAgent(
            name=config.name,
            model_client=model_client,
            system_message=config.system_message,
            tools=[],  # Tools can be added separately
        )

        # Add knowledge to the agent
        return await self.add_knowledge_to_agent(
            agent_id=config.name, agent=agent, knowledge_sources=knowledge_sources
        )

    async def clear_knowledge(self, agent_id: str) -> bool:
        """
        Clear all knowledge for an agent.

        Args:
            agent_id: ID of the agent

        Returns:
            True if successful, False otherwise
        """
        if agent_id not in self.knowledge_managers:
            return False

        try:
            await self.knowledge_managers[agent_id].clear_knowledge()
            return True
        except Exception as e:
            logger.error(f"Error clearing knowledge for agent {agent_id}: {str(e)}")
            return False

    async def close_all(self) -> None:
        """Close all knowledge managers."""
        for agent_id, manager in self.knowledge_managers.items():
            try:
                await manager.close()
            except Exception as e:
                logger.error(
                    f"Error closing knowledge manager for agent {agent_id}: {str(e)}"
                )

        self.knowledge_managers = {}


class KnowledgeSseClientWrapper:
    """
    SSE client wrapper for knowledge server connections.
    Similar to MCPSseClientWrapper but for knowledge servers.
    """

    def __init__(self):
        self.write = None
        self.sse = None
        self.session: ClientSession | None = None
        self.exit_stack = AsyncExitStack()
        self.max_retries = 3
        self.retry_delay = 1.0  # seconds

    async def validate_url(self, url: str | None) -> tuple[bool, str]:
        """Validate the SSE URL before attempting connection."""
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return (
                    False,
                    "Invalid URL format. Must include scheme (http/https) and host.",
                )

            async with httpx.AsyncClient() as client:
                try:
                    # First try a HEAD request to check if server is reachable
                    response = await client.head(url, timeout=5.0)
                    if response.status_code >= HTTP_ERROR_STATUS_CODE:
                        return (
                            False,
                            f"Server returned error status: {response.status_code}",
                        )

                except httpx.TimeoutException:
                    return (
                        False,
                        "Connection timed out. Server may be down or unreachable.",
                    )
                except httpx.NetworkError:
                    return False, "Network error. Could not reach the server."
                else:
                    return True, ""

        except (httpx.HTTPError, ValueError, OSError) as e:
            return False, f"URL validation error: {e!s}"

    async def pre_check_redirect(self, url: str | None) -> str | None:
        """Check for redirects and return the final URL."""
        if url is None:
            return url
        try:
            async with httpx.AsyncClient(follow_redirects=False) as client:
                response = await client.request("HEAD", url)
                if response.status_code == httpx.codes.TEMPORARY_REDIRECT:
                    return response.headers.get("Location", url)
        except (httpx.RequestError, httpx.HTTPError) as e:
            logger.warning(f"Error checking redirects: {e}")
        return url

    async def _connect_with_timeout(
        self,
        url: str | None,
        headers: dict[str, str] | None,
        timeout_seconds: int,
        sse_read_timeout_seconds: int,
    ):
        """Attempt to connect with timeout."""
        try:
            if url is None:
                return
            sse_transport = await self.exit_stack.enter_async_context(
                sse_client(url, headers, timeout_seconds, sse_read_timeout_seconds)
            )
            self.sse, self.write = sse_transport
            self.session = await self.exit_stack.enter_async_context(
                ClientSession(self.sse, self.write)
            )
            await self.session.initialize()
        except Exception as e:
            msg = f"Failed to establish SSE connection: {e!s}"
            raise ConnectionError(msg) from e

    async def connect_to_server(
        self,
        url: str | None,
        headers: dict[str, str] | None,
        timeout_seconds: int = 30,
        sse_read_timeout_seconds: int = 30,
    ):
        """Connect to server with retries and improved error handling."""
        if headers is None:
            headers = {}

        # First validate the URL
        is_valid, error_msg = await self.validate_url(url)
        if not is_valid:
            msg = f"Invalid SSE URL ({url}): {error_msg}"
            raise ValueError(msg)

        url = await self.pre_check_redirect(url)
        last_error = None

        for attempt in range(self.max_retries):
            try:
                await asyncio.wait_for(
                    self._connect_with_timeout(
                        url, headers, timeout_seconds, sse_read_timeout_seconds
                    ),
                    timeout=timeout_seconds,
                )

                if self.session is None:
                    msg = "Session not initialized"
                    raise ValueError(msg)

                response = await self.session.list_tools()

            except asyncio.TimeoutError:
                last_error = (
                    f"Connection to {url} timed out after {timeout_seconds} seconds"
                )
                logger.warning(f"Connection attempt {attempt + 1} failed: {last_error}")
            except ConnectionError as err:
                last_error = str(err)
                logger.warning(f"Connection attempt {attempt + 1} failed: {last_error}")
            except (ValueError, httpx.HTTPError, OSError) as err:
                last_error = f"Connection error: {err!s}"
                logger.warning(f"Connection attempt {attempt + 1} failed: {last_error}")
            else:
                return response.tools

            if attempt < self.max_retries - 1:
                await asyncio.sleep(self.retry_delay * (attempt + 1))

        msg = f"Failed to connect after {self.max_retries} attempts. Last error: {last_error}"
        raise ConnectionError(msg)
