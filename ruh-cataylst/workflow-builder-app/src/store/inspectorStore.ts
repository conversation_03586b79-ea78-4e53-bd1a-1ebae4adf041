import { create } from "zustand";
import { persist } from "zustand/middleware";
import { Node } from "reactflow";
import { WorkflowNodeData } from "@/types";

interface InspectorState {
  // UI state
  activeTab: "settings" | "info" | "advanced";
  showValidation: boolean;
  validationErrors: Record<string, { isValid: boolean; message: string }>;
  
  // Preferences (persisted)
  preferences: {
    defaultTab: "settings" | "info" | "advanced";
    expandedSections: string[];
  };
  
  // Actions
  setActiveTab: (tab: "settings" | "info" | "advanced") => void;
  setShowValidation: (show: boolean) => void;
  setValidationError: (inputName: string, error: { isValid: boolean; message: string }) => void;
  clearValidationErrors: () => void;
  setPreference: <K extends keyof InspectorState["preferences"]>(
    key: K,
    value: InspectorState["preferences"][K]
  ) => void;
  toggleExpandedSection: (sectionId: string) => void;
}

export const useInspectorStore = create<InspectorState>()(
  persist(
    (set) => ({
      // Default UI state
      activeTab: "settings",
      showValidation: false,
      validationErrors: {},
      
      // Default preferences
      preferences: {
        defaultTab: "settings",
        expandedSections: [],
      },
      
      // Actions
      setActiveTab: (tab) => set({ activeTab: tab }),
      setShowValidation: (show) => set({ showValidation: show }),
      setValidationError: (inputName, error) => 
        set((state) => ({
          validationErrors: {
            ...state.validationErrors,
            [inputName]: error,
          },
        })),
      clearValidationErrors: () => set({ validationErrors: {}, showValidation: false }),
      setPreference: (key, value) => 
        set((state) => ({
          preferences: {
            ...state.preferences,
            [key]: value,
          },
        })),
      toggleExpandedSection: (sectionId) =>
        set((state) => {
          const expandedSections = [...state.preferences.expandedSections];
          const index = expandedSections.indexOf(sectionId);
          
          if (index === -1) {
            expandedSections.push(sectionId);
          } else {
            expandedSections.splice(index, 1);
          }
          
          return {
            preferences: {
              ...state.preferences,
              expandedSections,
            },
          };
        }),
    }),
    {
      name: "inspector-preferences",
      partialize: (state) => ({ preferences: state.preferences }),
    }
  )
);
