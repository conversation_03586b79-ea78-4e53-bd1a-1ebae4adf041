import logging

logger = logging.getLogger(__name__)


def get_workflow_for_generate_script_audio_images(params):
    logger.debug("get workflow executed with params: %s", params)
    return {
        "nodes": [
            {
                "id": "generate-script",
                "server_script_path": "https://b44f-2401-4900-8fcb-e018-1c0b-f3fc-f558-ed3c.ngrok-free.app/sse",
                "server_tools": [
                    {
                        "tool_id": 1,
                        "tool_name": "script_using_topic",
                        "endpoint": "/script",
                        "input_schema": {
                            "predefined_fields": [
                                {
                                    "field_name": "session_id",
                                    "data_type": {
                                        "type": "string",
                                        "description": "Session ID for the request",
                                    },
                                    "required": True,
                                },
                                {
                                    "field_name": "topic",
                                    "data_type": {
                                        "type": "string",
                                        "description": "The topic of the video to be covered",
                                    },
                                    "required": True,
                                },
                                {
                                    "field_name": "video_type",
                                    "data_type": {
                                        "type": "string",
                                        "description": "The type of video",
                                    },
                                    "required": True,
                                },
                                {
                                    "field_name": "keywords",
                                    "data_type": {
                                        "type": "string",
                                        "description": "The keywords to be used in the script",
                                    },
                                    "required": True,
                                },
                            ]
                        },
                        "output_schema": {
                            "predefined_fields": [
                                {
                                    "field_name": "generated_script",
                                    "data_type": {
                                        "type": "object",
                                        "description": "prints the script generated",
                                    },
                                }
                            ]
                        },
                    }
                ],
            },
            {
                "id": "generate-audio",
                "server_script_path": "https://generate-audio.loca.lt/sse",
                "server_tools": [
                    {
                        "tool_id": 2,
                        "tool_name": "create_voices",
                        "endpoint": "/audio",
                        "input_schema": {
                            "predefined_fields": [
                                {
                                    "field_name": "session_id",
                                    "data_type": {
                                        "type": "string",
                                        "description": "Session ID for the request",
                                    },
                                    "required": True,
                                },
                                {
                                    "field_name": "script",
                                    "data_type": {
                                        "type": "string",
                                        "description": "the script to generate voice",
                                    },
                                    "required": True,
                                },
                                {
                                    "field_name": "voice_id",
                                    "data_type": {
                                        "type": "string",
                                        "description": "the play ht voice id to generate voice",
                                    },
                                    "required": True,
                                },
                            ]
                        },
                        "output_schema": {
                            "predefined_fields": [
                                {
                                    "field_name": "generated_audio_link",
                                    "data_type": {
                                        "type": "object",
                                        "description": "generated audio link object",
                                    },
                                }
                            ]
                        },
                    }
                ],
            },
            {
                "id": "generate-images",
                "server_script_path": "https://generate-images.loca.lt/sse",
                "server_tools": [
                    {
                        "tool_id": 3,
                        "tool_name": "generate_image_from_script",
                        "endpoint": "/image",
                        "input_schema": {
                            "predefined_fields": [
                                {
                                    "field_name": "session_id",
                                    "data_type": {
                                        "type": "string",
                                        "description": "Session ID for the request",
                                    },
                                    "required": True,
                                },
                                {
                                    "field_name": "script",
                                    "data_type": {
                                        "type": "string",
                                        "description": "the script to generate voice",
                                    },
                                    "required": True,
                                },
                            ]
                        },
                        "output_schema": {
                            "predefined_fields": [
                                {
                                    "field_name": "generated_images",
                                    "data_type": {
                                        "type": "object",
                                        "description": "generated images info",
                                    },
                                }
                            ]
                        },
                    }
                ],
            },
        ],
        "transitions": [
            {
                "id": "generate_script_transition",
                "sequence": 1,
                "transition_type": "initial",
                "node_info": {
                    "node_id": "generate-script",
                    "tools_to_use": [
                        {
                            "tool_id": 1,
                            "tool_name": "script_using_topic",
                            "tool_params": {
                                "items": [
                                    {
                                        "field_name": "session_id",
                                        "data_type": "string",
                                        "field_value": params.session_id,
                                    },
                                    {
                                        "field_name": "topic",
                                        "data_type": "string",
                                        "field_value": params.topic,
                                    },
                                    {
                                        "field_name": "keywords",
                                        "data_type": "string",
                                        "field_value": params.keywords,
                                    },
                                ]
                            },
                        }
                    ],
                    "input_data": [
                        {
                            "from_transition_id": "Start",
                            "target_node_id": "Start",
                            "data_type": "object",
                        }
                    ],
                    "output_data": [
                        {
                            "to_transition_id": "generate_audio_transition",
                            "target_node_id": "generate-audio",
                            "data_type": "object",
                        }
                    ],
                },
                "end": False,
            },
            {
                "id": "generate_audio_transition",
                "sequence": 2,
                "transition_type": "standard",
                "node_info": {
                    "node_id": "generate-audio",
                    "tools_to_use": [
                        {
                            "tool_id": 2,
                            "tool_name": "create_voices",
                            "tool_params": {
                                "items": [
                                    {
                                        "field_name": "session_id",
                                        "data_type": "string",
                                        "field_value": params.session_id,
                                    },
                                    {
                                        "field_name": "script",
                                        "data_type": "string",
                                        "field_value": "${script}",
                                        # "field_value": params.topic
                                    },
                                    {
                                        "field_name": "voice_id",
                                        "data_type": "string",
                                        "field_value": params.keywords,
                                    },
                                ]
                            },
                        }
                    ],
                    "input_data": [
                        {
                            "from_transition_id": "generate_script_transition",
                            "target_node_id": "generate-script",
                            "data_type": "object",
                        }
                    ],
                    "output_data": [
                        {
                            "to_transition_id": "generate_images_trasition",
                            "target_node_id": "generate_images",
                            "data_type": "object",
                        }
                    ],
                },
                "end": False,
            },
            {
                "id": "generate_images_trasition",
                "sequence": 3,
                "transition_type": "standard",
                "node_info": {
                    "node_id": "generate-images",
                    "tools_to_use": [
                        {
                            "tool_id": 3,
                            "tool_name": "generate_image_from_script",
                            "tool_params": {
                                "items": [
                                    {
                                        "field_name": "session_id",
                                        "data_type": "string",
                                        "field_value": params.session_id,
                                    },
                                    {
                                        "field_name": "script",
                                        "data_type": "string",
                                        "field_value": "${script}",
                                    },
                                ]
                            },
                        }
                    ],
                    "input_data": [
                        {
                            "from_transition_id": "generate_audio_transition",
                            "target_node_id": "generate-audio",
                            "data_type": "object",
                        }
                    ],
                    "output_data": [],
                },
                "end": True,
            },
        ],
    }
