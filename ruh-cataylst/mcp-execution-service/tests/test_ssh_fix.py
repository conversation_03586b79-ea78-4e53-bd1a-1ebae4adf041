#!/usr/bin/env python3
"""
Test script to verify SSH connection fixes for MCP execution service.
This script tests the SSH connection with the new options to ensure they work properly.
"""

import asyncio
import subprocess
import sys
import os
import logging

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.ssh_manager import get_global_ssh_manager
from app.config import settings

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_ssh_connection_basic():
    """Test basic SSH connection with new options."""
    logger.info("=== Testing Basic SSH Connection ===")
    
    ssh_manager = get_global_ssh_manager()
    ssh_key_path = ssh_manager.get_ssh_key_path()
    
    if not ssh_key_path:
        logger.error("SSH key not available. Please ensure SSH key is initialized.")
        return False
    
    # Test basic SSH connection
    ssh_cmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=30",
        "-o", "ServerAliveInterval=30",
        "-o", "ServerAliveCountMax=3",
        "-o", "IdentitiesOnly=yes",
        "-p", str(settings.ssh_port),
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        "echo 'SSH connection successful'"
    ]
    
    logger.info(f"Testing SSH command: {' '.join(ssh_cmd)}")
    
    try:
        result = subprocess.run(ssh_cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            logger.info("✅ Basic SSH connection successful!")
            logger.info(f"Output: {result.stdout.strip()}")
            return True
        else:
            logger.error("❌ Basic SSH connection failed")
            logger.error(f"STDERR: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ SSH connection timed out")
        return False
    except Exception as e:
        logger.error(f"❌ SSH connection error: {e}")
        return False


def test_docker_container_access():
    """Test Docker container access."""
    logger.info("=== Testing Docker Container Access ===")
    
    ssh_manager = get_global_ssh_manager()
    ssh_key_path = ssh_manager.get_ssh_key_path()
    
    if not ssh_key_path:
        logger.error("SSH key not available.")
        return False
    
    # List running containers
    list_cmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=30",
        "-o", "ServerAliveInterval=30",
        "-o", "ServerAliveCountMax=3",
        "-o", "IdentitiesOnly=yes",
        "-p", str(settings.ssh_port),
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        "docker ps --format 'table {{.Names}}\\t{{.Status}}\\t{{.Image}}'"
    ]
    
    logger.info("Listing running containers...")
    
    try:
        result = subprocess.run(list_cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            logger.info("✅ Docker container listing successful!")
            logger.info(f"Running containers:\n{result.stdout}")
            return True
        else:
            logger.error("❌ Docker container listing failed")
            logger.error(f"STDERR: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ Docker listing timed out")
        return False
    except Exception as e:
        logger.error(f"❌ Docker listing error: {e}")
        return False


def test_specific_container_exec(container_id: str):
    """Test executing a command in a specific container."""
    logger.info(f"=== Testing Container Exec: {container_id} ===")
    
    ssh_manager = get_global_ssh_manager()
    ssh_key_path = ssh_manager.get_ssh_key_path()
    
    if not ssh_key_path:
        logger.error("SSH key not available.")
        return False
    
    # Test container exec
    exec_cmd = [
        "ssh",
        "-o", "StrictHostKeyChecking=no",
        "-o", "UserKnownHostsFile=/dev/null",
        "-o", "ConnectTimeout=30",
        "-o", "ServerAliveInterval=30",
        "-o", "ServerAliveCountMax=3",
        "-o", "IdentitiesOnly=yes",
        "-p", str(settings.ssh_port),
        "-i", ssh_key_path,
        f"{settings.ssh_user}@{settings.ssh_host}",
        f"docker exec -i {container_id} echo 'Container exec successful'"
    ]
    
    logger.info(f"Testing container exec: {container_id}")
    
    try:
        result = subprocess.run(exec_cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            logger.info("✅ Container exec successful!")
            logger.info(f"Output: {result.stdout.strip()}")
            return True
        else:
            logger.error("❌ Container exec failed")
            logger.error(f"STDERR: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ Container exec timed out")
        return False
    except Exception as e:
        logger.error(f"❌ Container exec error: {e}")
        return False


async def test_mcp_client_connection():
    """Test the actual MCP client connection with fixes."""
    logger.info("=== Testing MCP Client Connection ===")
    
    try:
        from app.core_.client import MCPClient
        from app.schemas.client import ConnectionConfig
        
        # Use a test container ID (you'll need to replace this with an actual one)
        test_container_id = "035a8924-5153-4133-940e-ac0be0dbd32a_91a237fd-0225-4e02-9e9f-805eff073b07"
        
        config = ConnectionConfig(
            timeout=30.0,
            max_retries=2,
            retry_delay=2.0
        )
        
        client = MCPClient(
            connection_type="ssh_docker",
            ssh_host=settings.ssh_host,
            ssh_user=settings.ssh_user,
            ssh_port=settings.ssh_port,
            ssh_key_content=settings.ssh_key_content,
            docker_image=test_container_id,  # This becomes container_name
            container_command="python server.py",
            connection_config=config
        )
        
        logger.info("Attempting MCP client connection...")
        
        async with client:
            logger.info("✅ MCP client connection successful!")
            
            # Try to list tools
            tools = await client.list_tools()
            logger.info(f"Available tools: {[tool.name for tool in tools]}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ MCP client connection failed: {e}")
        return False


def main():
    """Main test function."""
    logger.info("Starting SSH connection fix tests...")
    
    # Initialize SSH key if needed
    if settings.ssh_key_content:
        ssh_manager = get_global_ssh_manager()
        ssh_manager.initialize_ssh_key(settings.ssh_key_content)
        logger.info("SSH key initialized")
    else:
        logger.error("No SSH key content available in settings")
        return
    
    # Run tests
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Basic SSH connection
    total_tests += 1
    if test_ssh_connection_basic():
        tests_passed += 1
    
    # Test 2: Docker container access
    total_tests += 1
    if test_docker_container_access():
        tests_passed += 1
    
    # Test 3: Specific container exec (replace with actual container ID)
    container_id = "035a8924-5153-4133-940e-ac0be0dbd32a_91a237fd-0225-4e02-9e9f-805eff073b07"
    total_tests += 1
    if test_specific_container_exec(container_id):
        tests_passed += 1
    
    # Test 4: MCP client connection
    total_tests += 1
    if asyncio.run(test_mcp_client_connection()):
        tests_passed += 1
    
    # Summary
    logger.info(f"\n=== Test Summary ===")
    logger.info(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        logger.info("🎉 All tests passed! SSH connection fixes are working.")
    else:
        logger.warning(f"⚠️  {total_tests - tests_passed} test(s) failed. Check the logs above.")


if __name__ == "__main__":
    main()
