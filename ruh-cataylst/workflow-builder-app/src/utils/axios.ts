import axios, { AxiosError, AxiosRequestConfig, AxiosInstance } from "axios";
import { getAccessToken, clearAuthCookies } from "./authCookies";
import { getClientAccessToken, clearClientAuthCookies } from "@/lib/clientCookies";
import { loginRoute } from "@/shared/routes";
import { useUserStore } from "@/hooks/use-user";

// Helper function to clear user store
const clearUserStore = () => {
  useUserStore.getState().clearUser();
};

// Configuration interface for axios instances
export interface AxiosInstanceConfig {
  baseURL?: string;
  withCredentials?: boolean;
  enableTokenRefresh?: boolean;
  enableClientSideToken?: boolean;
  customHeaders?: Record<string, string>;
}

// Helper function to get appropriate token based on environment
const getAppropriateToken = async (enableClientSide: boolean = false): Promise<string | null> => {
  if (enableClientSide && typeof window !== "undefined") {
    // Client-side
    return getClientAccessToken();
  } else {
    // Server-side or fallback
    return await getAccessToken();
  }
};

// Create request interceptor
const createRequestInterceptor = (config: AxiosInstanceConfig = {}) => {
  return async (requestConfig: any) => {
    // Ensure headers object exists
    if (!requestConfig.headers) {
      requestConfig.headers = {};
    }

    const token = await getAppropriateToken(config.enableClientSideToken);
    if (token) {
      requestConfig.headers.Authorization = `Bearer ${token}`;
      console.log(`[DEBUG] Added Authorization header with token (length: ${token.length})`);
    } else {
      console.log(`[DEBUG] No token available for request to ${requestConfig.url}`);
    }

    // Add common headers
    requestConfig.headers["ngrok-skip-browser-warning"] = "true";

    // Add custom headers if provided
    if (config.customHeaders) {
      Object.assign(requestConfig.headers, config.customHeaders);
    }

    return requestConfig;
  };
};

// Create response interceptor
const createResponseInterceptor = (instance: AxiosInstance, config: AxiosInstanceConfig = {}) => {
  return async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & {
      _retry?: boolean;
    };

    // Only handle token refresh if enabled
    if (!config.enableTokenRefresh) {
      return Promise.reject(error);
    }

    // Check if the error is due to an expired token (401 Unauthorized or 403 Forbidden)
    if (
      (error.response?.status === 401 || error.response?.status === 403) &&
      !originalRequest._retry
    ) {
      // Mark this request as retried to prevent infinite loops
      originalRequest._retry = true;

      try {
        // Call the refresh token endpoint using a fresh axios instance to avoid infinite loops
        // This endpoint will use the HTTP-only refresh token cookie automatically
        const refreshInstance = axios.create();
        const response = await refreshInstance.post("/api/auth/refresh");

        if (response.data.success && response.data.accessToken) {
          // Update the authorization header with the new token
          originalRequest.headers = {
            ...originalRequest.headers,
            Authorization: `Bearer ${response.data.accessToken}`,
          };

          // Retry the original request with the new token
          return instance(originalRequest);
        } else {
          // Token refresh failed
          await clearAuthCookies();
          clearUserStore();
          if (typeof window !== "undefined") {
            window.location.href = loginRoute;
          }
          return Promise.reject(new Error("Token refresh failed"));
        }
      } catch (refreshError) {
        // Clear cookies and redirect to login on refresh error
        await clearAuthCookies();
        clearUserStore();
        if (typeof window !== "undefined") {
          window.location.href = loginRoute;
        }
        return Promise.reject(refreshError);
      }
    }

    // For other errors, just reject the promise
    return Promise.reject(error);
  };
};

// Create a centralized axios instance factory
export const createAxiosInstance = (config: AxiosInstanceConfig = {}): AxiosInstance => {
  const instance = axios.create({
    baseURL: config.baseURL || process.env.NEXT_PUBLIC_API_URL,
    withCredentials: config.withCredentials ?? false,
  });

  // Add request interceptor
  instance.interceptors.request.use(createRequestInterceptor(config), (error: any) => {
    return Promise.reject(
      new Error(`Request interceptor error: ${error.message || "Unknown error"}`),
    );
  });

  // Add response interceptor
  instance.interceptors.response.use(
    (response) => response,
    createResponseInterceptor(instance, config),
  );

  return instance;
};

// Main API instance with full token refresh capabilities
const api = createAxiosInstance({
  enableTokenRefresh: true,
  enableClientSideToken: true,
});

// Workflow API instance
export const workflowApi = createAxiosInstance({
  enableTokenRefresh: false,
  enableClientSideToken: true,
});

// Auth API instance
export const authApi = createAxiosInstance({
  enableTokenRefresh: true,
  enableClientSideToken: true,
  withCredentials: true,
});

// External API instance (for external URLs without auth)
export const externalApi = axios.create();

// Centralized logout function
export const logout = async (): Promise<void> => {
  try {
    // Clear all auth cookies (both access and refresh tokens)
    await clearAuthCookies();

    // Clear user store
    clearUserStore();

    // Redirect to login page
    if (typeof window !== "undefined") {
      window.location.href = loginRoute;
    }
  } catch (error) {
    console.error("Error during logout:", error);

    // Even if there's an error, try to clear everything
    await clearAuthCookies();
    clearUserStore();

    if (typeof window !== "undefined") {
      window.location.href = loginRoute;
    }
  }
};

export default api;
