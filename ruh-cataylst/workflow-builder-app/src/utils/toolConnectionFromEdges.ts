/**
 * Utility functions for generating tool connections from visual edges
 * This ensures tool nodes remain visible and editable on the canvas
 */

import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";

export interface ToolConnection {
  node_id: string;
  node_type: string;
  node_label: string;
  component_id: string;
  component_type: string;
  component_name: string;
  component_definition: any;
  component_config: any;
  mcp_metadata?: any;
}

/**
 * Generate simplified tool connections for an AgenticAI node from visual edges
 * @param agenticAINode The AgenticAI node to generate tool connections for
 * @param nodes All nodes in the workflow
 * @param edges All edges in the workflow
 * @returns Array of tool connections (simplified structure)
 */
export function generateToolConnectionsFromEdges(
  agenticAINode: Node<WorkflowNodeData>,
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): ToolConnection[] {
  // Find all edges connecting to this AgenticAI node's tool handles
  const toolEdges = edges.filter(edge =>
    edge.target === agenticAINode.id &&
    edge.targetHandle &&
    (edge.targetHandle.includes("tool") || edge.targetHandle === "tools")
  );

  if (toolEdges.length === 0) {
    return [];
  }

  console.log(`[TOOL CONNECTIONS] Found ${toolEdges.length} tool edges for AgenticAI node ${agenticAINode.id}`);

  // Create simplified tool connections array (no handle grouping needed)
  const toolConnections: ToolConnection[] = [];

  toolEdges.forEach(edge => {
    const sourceNode = nodes.find(node => node.id === edge.source);
    if (sourceNode) {
      // Create tool connection data from the source node
      const toolConnection: ToolConnection = {
        node_id: sourceNode.id,
        node_type: sourceNode.data.originalType || sourceNode.data.type || "Unknown",
        node_label: sourceNode.data.label || sourceNode.data.definition?.display_name || "Unknown Tool",
        component_id: sourceNode.id,
        component_type: sourceNode.data.originalType || sourceNode.data.type || "Unknown",
        component_name: sourceNode.data.label || sourceNode.data.definition?.display_name || "Unknown Tool",
        component_definition: sourceNode.data.definition || {},
        component_config: sourceNode.data.config || {},
        // Add MCP metadata if available
        ...(sourceNode.data.definition?.mcp_info && {
          mcp_metadata: sourceNode.data.definition.mcp_info
        })
      };

      toolConnections.push(toolConnection);
      console.log(`[TOOL CONNECTIONS] Added tool connection: ${sourceNode.data.label} -> ${agenticAINode.data.label}`);
    }
  });

  return toolConnections;
}

/**
 * Generate tool connections for all AgenticAI nodes in a workflow
 * @param nodes All nodes in the workflow
 * @param edges All edges in the workflow
 * @returns Map of AgenticAI node IDs to their tool connections
 */
export function generateAllToolConnectionsFromEdges(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): Map<string, ToolConnection[]> {
  const allToolConnections = new Map<string, ToolConnection[]>();

  // Find all AgenticAI nodes
  const agenticAINodes = nodes.filter(node => node.data.originalType === "AgenticAI");

  agenticAINodes.forEach(agenticNode => {
    const toolConnections = generateToolConnectionsFromEdges(agenticNode, nodes, edges);
    if (toolConnections.length > 0) {
      allToolConnections.set(agenticNode.id, toolConnections);
    }
  });

  return allToolConnections;
}

/**
 * Update AgenticAI nodes with tool connections generated from edges
 * This is used during save/execution to ensure tool connections are preserved
 * @param nodes All nodes in the workflow
 * @param edges All edges in the workflow
 * @returns Updated nodes with simplified tool connections in their config
 */
export function updateNodesWithToolConnectionsFromEdges(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): Node<WorkflowNodeData>[] {
  const allToolConnections = generateAllToolConnectionsFromEdges(nodes, edges);

  return nodes.map(node => {
    if (node.data.originalType === "AgenticAI" && allToolConnections.has(node.id)) {
      const toolConnections = allToolConnections.get(node.id)!;

      // Update the node's config with simplified tool connections structure
      return {
        ...node,
        data: {
          ...node.data,
          config: {
            ...node.data.config,
            tools: toolConnections  // Simplified: config.tools instead of config.tool_connections.tools
          }
        }
      };
    }
    return node;
  });
}

/**
 * Check if a handle is a tool handle
 * @param handleId The handle ID to check
 * @returns True if it's a tool handle
 */
export function isToolHandle(handleId: string): boolean {
  return handleId.includes("tool") || handleId === "tools";
}

/**
 * Convert tool connections back to visual nodes and edges
 * This is the reverse operation of generateToolConnectionsFromEdges
 * Uses only the new simplified config.tools format
 * @param nodes All nodes in the workflow
 * @param edges All edges in the workflow
 * @returns Object containing additional nodes and edges to add to the workflow
 */
export function convertToolConnectionsToVisualComponents(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): { additionalNodes: Node<WorkflowNodeData>[]; additionalEdges: Edge[] } {
  const additionalNodes: Node<WorkflowNodeData>[] = [];
  const additionalEdges: Edge[] = [];

  console.log("[TOOL CONVERSION] Converting tool connections to visual components...");

  // Find all AgenticAI nodes with tool connections (simplified format only)
  const agenticAINodes = nodes.filter(node =>
    node.data.originalType === "AgenticAI" &&
    node.data.config?.tools &&
    Array.isArray(node.data.config.tools)
  );

  agenticAINodes.forEach(agenticNode => {
    const toolConnectionsArray: ToolConnection[] = agenticNode.data.config.tools;
    console.log(`[TOOL CONVERSION] Processing AgenticAI node ${agenticNode.id} with ${toolConnectionsArray.length} tools`);

    // Process the tool connections array
    toolConnectionsArray.forEach((toolData: ToolConnection, index: number) => {
      // Check if this tool node already exists in the nodes array
      const existingNode = nodes.find(n => n.id === toolData.node_id);
      const existingAdditionalNode = additionalNodes.find(n => n.id === toolData.node_id);

      if (!existingNode && !existingAdditionalNode) {
        // Create a new tool node from the stored tool connection data
        const toolNode: Node<WorkflowNodeData> = {
          id: toolData.node_id,
          type: "WorkflowNode",
          position: {
            // Position tool nodes to the left of the AgenticAI node
            x: agenticNode.position.x - 300,
            y: agenticNode.position.y + (index * 150) - 100
          },
          data: {
            label: toolData.node_label || toolData.component_name || "Tool",
            type: toolData.component_type === "MCP" ? "mcp" : "component",
            originalType: toolData.node_type || toolData.component_type,
            definition: toolData.component_definition || {},
            config: toolData.component_config || {},
            // Mark as tool connection for visual differentiation
            isToolConnection: true
          },
          width: 208,
          height: 122,
          selected: false,
          dragging: false
        };

        additionalNodes.push(toolNode);
        console.log(`[TOOL CONVERSION] Created tool node: ${toolNode.id} (${toolNode.data.label})`);
      }

      // Check if edge already exists
      const edgeId = `${toolData.node_id}-${agenticNode.id}`;
      const existingEdge = edges.find(e => e.id === edgeId);
      const existingAdditionalEdge = additionalEdges.find(e => e.id === edgeId);

      if (!existingEdge && !existingAdditionalEdge) {
        // Determine the correct source handle from the component definition
        let sourceHandle = "output"; // Default
        if (toolData.component_definition?.outputs && Array.isArray(toolData.component_definition.outputs)) {
          const firstOutput = toolData.component_definition.outputs[0];
          if (firstOutput?.name) {
            sourceHandle = firstOutput.name;
          }
        }

        // Create edge connecting tool to AgenticAI node (always use "tools" handle)
        const toolEdge: Edge = {
          id: edgeId,
          source: toolData.node_id,
          sourceHandle: sourceHandle,
          target: agenticNode.id,
          targetHandle: "tools", // Always use "tools" handle
          type: "default",
          animated: true
        };

        additionalEdges.push(toolEdge);
        console.log(`[TOOL CONVERSION] Created tool edge: ${toolData.node_id} -> ${agenticNode.id} (tools)`);
      }
    });
  });

  console.log(`[TOOL CONVERSION] Created ${additionalNodes.length} tool nodes and ${additionalEdges.length} tool edges`);

  return { additionalNodes, additionalEdges };
}



/**
 * Test function to verify simplified tool connection generation
 * @param nodes Test nodes
 * @param edges Test edges
 */
export function testToolConnectionGeneration(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[]
): void {
  console.log("=== Testing Simplified Tool Connection Generation ===");

  const agenticAINodes = nodes.filter(node => node.data.originalType === "AgenticAI");
  console.log(`Found ${agenticAINodes.length} AgenticAI nodes`);

  agenticAINodes.forEach(agenticNode => {
    const toolConnections = generateToolConnectionsFromEdges(agenticNode, nodes, edges);
    console.log(`AgenticAI node ${agenticNode.id} tools:`, toolConnections);
  });

  const allToolConnections = generateAllToolConnectionsFromEdges(nodes, edges);
  console.log("All tool connections:", allToolConnections);

  const updatedNodes = updateNodesWithToolConnectionsFromEdges(nodes, edges);
  const updatedAgenticNodes = updatedNodes.filter(node => node.data.originalType === "AgenticAI");
  updatedAgenticNodes.forEach(node => {
    console.log(`Updated AgenticAI node ${node.id} config.tools:`, node.data.config.tools);
  });

  console.log("=== Test Complete ===");
}
