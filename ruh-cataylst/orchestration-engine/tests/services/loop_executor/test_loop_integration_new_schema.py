"""
Integration tests for the new loop node architecture.

This module tests the integration of all loop executor components with the new schema.
"""

import pytest
from unittest.mock import Mock, AsyncMock
from app.services.loop_executor.loop_executor import LoopExecutor
from app.services.loop_executor.loop_state_manager import Loop<PERSON><PERSON><PERSON>anager
from app.services.loop_executor.loop_aggregator import LoopAggregator
from app.services.loop_executor.loop_error_handler import <PERSON><PERSON>rrorHandler
from app.services.loop_executor.loop_exit_condition_evaluator import LoopExitConditionEvaluator


class TestLoopIntegrationNewSchema:
    """Test integration of all loop executor components with new schema."""

    @pytest.fixture
    def loop_executor(self):
        """Create a LoopExecutor instance for integration testing."""
        state_manager = Mock()
        state_manager.workflow_id = "test_workflow"
        state_manager.store_loop_state = AsyncMock()
        state_manager.save_workflow_state = AsyncMock()
        
        workflow_utils = Mock()
        result_callback = AsyncMock()
        transitions_by_id = {}
        nodes = {}
        transition_handler = Mock()
        
        executor = LoopExecutor(
            state_manager=state_manager,
            workflow_utils=workflow_utils,
            result_callback=result_callback,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            transition_handler=transition_handler,
        )
        
        # Mock the error handler to avoid attribute errors
        executor.error_handler = Mock()
        executor.error_handler.get_error_report.return_value = {"total_errors": 0}
        executor.error_handler.configure_error_handling = Mock()
        executor.error_handler.should_continue_on_error.return_value = True
        executor.error_handler.get_errors_for_results.return_value = []
        
        return executor

    def test_new_schema_component_initialization(self, loop_executor):
        """Test that all new schema components are properly initialized."""
        # Test that all required components exist
        assert hasattr(loop_executor, 'aggregator')
        assert hasattr(loop_executor, 'error_handler')
        assert hasattr(loop_executor, 'exit_condition_evaluator')
        
        # Test component types
        assert isinstance(loop_executor.aggregator, LoopAggregator)
        assert isinstance(loop_executor.exit_condition_evaluator, LoopExitConditionEvaluator)

    def test_new_schema_config_parsing(self, loop_executor):
        """Test parsing of new schema configuration."""
        new_schema_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "source_type": "static_list",
                "static_items": [1, 2, 3, 4, 5]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "iteration_settings": {
                "parallel_execution": False,
                "max_concurrent": 1,
                "preserve_order": True,
                "iteration_timeout": 30
            },
            "result_aggregation": {
                "aggregation_type": "collect_successful",
                "include_metadata": True
            },
            "error_handling": {
                "on_iteration_error": "retry_once",
                "include_errors": True,
                "error_format": "summary"
            }
        }
        
        # Parse the configuration
        parsed_config = loop_executor.parse_loop_config(new_schema_config)
        
        # Verify parsed configuration structure
        assert "iteration_source" in parsed_config
        assert "exit_condition" in parsed_config
        assert "iteration_settings" in parsed_config
        assert "result_aggregation" in parsed_config
        assert "error_handling" in parsed_config
        
        # Verify specific values
        assert parsed_config["iteration_source"]["source_type"] == "static_list"
        assert len(parsed_config["iteration_source"]["static_items"]) == 5
        assert parsed_config["exit_condition"]["condition_type"] == "all_items_processed"
        assert parsed_config["result_aggregation"]["aggregation_type"] == "collect_successful"

    def test_exit_condition_evaluator_integration(self, loop_executor):
        """Test integration with exit condition evaluator."""
        # Configure exit condition
        exit_config = {
            "condition_type": "max_iterations",
            "max_iterations": 3
        }
        
        loop_executor.exit_condition_evaluator.initialize_evaluation(exit_config)
        
        # Test evaluation
        assert not loop_executor.exit_condition_evaluator.should_exit_loop(0, 10, {}, {})
        assert not loop_executor.exit_condition_evaluator.should_exit_loop(1, 10, {}, {})
        assert loop_executor.exit_condition_evaluator.should_exit_loop(2, 10, {}, {})

    def test_aggregator_integration(self, loop_executor):
        """Test integration with loop aggregator."""
        # Test new schema aggregation types
        results = {
            0: {"status": "success", "result": "data1"},
            1: {"status": "failed", "error": "error1"},
            2: {"status": "success", "result": "data2"},
        }
        
        # Test collect_successful aggregation
        config = {"type": "collect_successful", "include_metadata": True}
        aggregated = loop_executor.aggregator.aggregate_results(results, config)
        
        assert isinstance(aggregated, dict)
        assert "results" in aggregated
        assert "metadata" in aggregated
        assert len(aggregated["results"]) == 2  # Only successful results
        assert aggregated["metadata"]["successful_count"] == 2

    def test_error_handler_integration(self, loop_executor):
        """Test integration with error handler."""
        # Configure error handling
        error_config = {
            "on_iteration_error": "retry_once",
            "include_errors": True,
            "error_format": "summary"
        }
        
        # Create a real error handler for this test
        from app.services.loop_executor.loop_error_handler import LoopErrorHandler
        loop_executor.error_handler = LoopErrorHandler()
        loop_executor.error_handler.configure_error_handling(error_config)
        
        # Test error handling configuration
        assert loop_executor.error_handler.should_include_errors_in_results()
        assert loop_executor.error_handler.get_error_inclusion_format() == "summary"
        
        # Test error action determination
        test_error = ValueError("Test error")
        action = loop_executor.error_handler.get_iteration_error_action(test_error, 0, {})
        assert action in ["retry_once", "skip_iteration", "fail_loop"]

    def test_state_manager_integration(self, loop_executor):
        """Test integration with state manager."""
        # Create a real state manager for this test
        from app.services.loop_executor.loop_state_manager import LoopStateManager
        state_manager = LoopStateManager("test_loop", "test_transition", "test_workflow")
        
        # Test new architecture state methods
        state_manager.set_output_routing_state("iteration_active")
        assert state_manager.get_output_routing_state() == "iteration_active"
        
        # Test iteration output storage
        output_data = {"current_item": "test_data", "iteration_index": 0}
        state_manager.store_iteration_output(0, output_data)
        outputs = state_manager.get_iteration_outputs()
        assert len(outputs) == 1
        assert outputs[0]["iteration_index"] == 0
        
        # Test exit output storage
        exit_data = {"aggregated_results": [1, 2, 3], "total_count": 3}
        state_manager.store_exit_output(exit_data)
        exit_output = state_manager.get_exit_output()
        assert exit_output is not None
        assert exit_output["exit_data"]["total_count"] == 3

    def test_dual_output_creation(self, loop_executor):
        """Test dual output creation for new architecture."""
        # Set up test data
        loop_executor.current_iteration_data = [{"id": 1}, {"id": 2}, {"id": 3}]
        loop_executor.iteration_results = {
            0: {"status": "success", "result": "result1"},
            1: {"status": "success", "result": "result2"},
            2: {"status": "success", "result": "result3"},
        }
        
        # Test iteration output creation
        iteration_output = loop_executor.create_iteration_output(1)
        assert "current_iteration_item" in iteration_output
        assert iteration_output["current_iteration_item"]["id"] == 2
        assert iteration_output["iteration_index"] == 1
        
        # Test exit output creation
        exit_output = loop_executor.create_exit_output()
        assert "aggregated_results" in exit_output
        assert "loop_metadata" in exit_output
        assert isinstance(exit_output["aggregated_results"], list)

    def test_full_workflow_integration(self, loop_executor):
        """Test full workflow integration with new schema."""
        # Complete new schema configuration
        full_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "source_type": "static_list",
                "static_items": ["item1", "item2", "item3"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "iteration_settings": {
                "parallel_execution": False,
                "max_concurrent": 1,
                "preserve_order": True,
                "iteration_timeout": 30
            },
            "result_aggregation": {
                "aggregation_type": "collect_all",
                "include_metadata": True
            },
            "error_handling": {
                "on_iteration_error": "skip_iteration",
                "include_errors": False
            }
        }
        
        # Parse configuration
        parsed_config = loop_executor.parse_loop_config(full_config)
        
        # Verify all components can work with the parsed configuration
        assert parsed_config is not None
        assert "iteration_source" in parsed_config
        assert "exit_condition" in parsed_config
        assert "result_aggregation" in parsed_config
        assert "error_handling" in parsed_config
        
        # Test that validation passes
        try:
            loop_executor._validate_new_schema_raw(full_config)
        except Exception as e:
            pytest.fail(f"Validation failed for valid config: {e}")

    def test_backward_compatibility(self, loop_executor):
        """Test that new architecture maintains backward compatibility."""
        # Legacy configuration format
        legacy_config = {
            "loop_behavior": "sequential",
            "iteration_source": ["item1", "item2", "item3"],
            "max_iterations": 10,
            "aggregation_type": "list"
        }
        
        # Should parse without errors
        parsed_config = loop_executor.parse_loop_config(legacy_config)
        assert parsed_config is not None
        
        # Should contain converted values
        assert "iteration_source" in parsed_config
        assert "exit_condition" in parsed_config
