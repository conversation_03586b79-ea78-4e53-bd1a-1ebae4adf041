"""
Example of using the KnowledgeManager to add knowledge to an AutoGen agent.
"""

import asyncio
import os
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_agentchat.agents import AssistantAgent
from knowledge_manager import KnowledgeManager


async def main():
    # Get API key from environment
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("Please set the OPENAI_API_KEY environment variable")

    # Method 1: Create a knowledgeable agent directly
    agent, knowledge_manager = await KnowledgeManager.create_knowledgeable_agent(
        name="knowledgeable_assistant",
        model="gpt-4o",
        api_key=api_key,
        system_message="You are a helpful AI assistant with access to a knowledge base. Use this knowledge to answer questions accurately.",
        collection_name="example_knowledge",
    )

    # Add knowledge from different sources
    print("Adding knowledge from text...")
    chunks = await knowledge_manager.add_text(
        "AutoGen is a framework for building LLM applications using multiple agents that can converse with each other to solve tasks.",
        source_name="autogen_definition",
    )
    print(f"Added {chunks} chunks from direct text input")

    # Add knowledge from a website
    print("Adding knowledge from website...")
    try:
        chunks = await knowledge_manager.add_website(
            "https://microsoft.github.io/autogen/"
        )
        print(f"Added {chunks} chunks from website")
    except Exception as e:
        print(f"Error adding website: {e}")

    # Add knowledge from a local document (if available)
    document_path = "README.md"  # Change to an actual document path
    if os.path.exists(document_path):
        print(f"Adding knowledge from document {document_path}...")
        try:
            chunks = await knowledge_manager.add_document(document_path)
            print(f"Added {chunks} chunks from document")
        except Exception as e:
            print(f"Error adding document: {e}")

    # Method 2: Enhance an existing agent with knowledge
    print("Creating a regular agent and enhancing it with knowledge...")
    regular_agent = AssistantAgent(
        name="regular_assistant",
        model_client=OpenAIChatCompletionClient(
            model="gpt-4o",
            api_key=api_key,
        ),
        system_message="You are a helpful AI assistant.",
    )

    # Create a new knowledge manager
    custom_knowledge = KnowledgeManager(collection_name="custom_knowledge")

    # Add some specific knowledge
    await custom_knowledge.add_text(
        "The company was founded in 2023 and specializes in AI solutions for healthcare.",
        source_name="company_info",
    )

    # Enhance the regular agent with this knowledge
    enhanced_agent = await custom_knowledge.enhance_agent_with_knowledge(regular_agent)

    # Test the knowledgeable agent
    print("\nTesting the knowledgeable agent...")
    stream = agent.run_stream(task="What is AutoGen and how can it be used?")
    await Console(stream)

    # Clean up resources
    await knowledge_manager.close()
    await custom_knowledge.close()


if __name__ == "__main__":
    asyncio.run(main())
