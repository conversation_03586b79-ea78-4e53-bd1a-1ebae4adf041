# agents/head_agent.py
import asyncio
import json
import uuid
import logging
from typing import List, Tuple, Dict, Optional, Any, Set, Union
from pydantic import BaseModel, Field
from autogen_core.tools import FunctionTool
from ..schemas.models import UserTask, AgentResponse
from autogen_core import (
    AgentId,
    AgentType,
    FunctionCall,
    MessageContext,
    RoutedAgent,
    TopicId,
    message_handler,
)
from autogen_core.models import (
    AssistantMessage,
    ChatCompletionClient,
    FunctionExecutionResult,
    FunctionExecutionResultMessage,
    SystemMessage,
    UserMessage,
)

logger = logging.getLogger(__name__)

# --- Request Queue Management ---
# WARNING: This simple dictionary is not inherently thread-safe for highly concurrent scenarios.
# For production, consider using a more robust concurrent dictionary or locking.
# Also, implement proper cleanup for timed-out or abandoned requests.
request_queues: Dict[str, asyncio.Queue] = {}
active_tasks: Set[str] = set()  # Track active task IDs
# -----------------------------


class HeadAgent(RoutedAgent):
    """
    The central agent responsible for receiving user tasks and delegating them
    to the appropriate specialized agent based on LLM routing.
    It also handles responses coming back from delegated agents and forwards
    them (potentially streaming) back towards the originating request.
    """

    def __init__(
        self,
        description: str,
        system_message: SystemMessage,
        model_client: ChatCompletionClient,
        registered_agent_types: Dict[str, AgentType] = None,
        agent_topic_type: str = "HeadAgent",
        user_topic_type: str = "User",
        delegate_tools: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(description)
        self._system_message = system_message
        self._model_client = model_client
        self.registered_agent_types = registered_agent_types or {}
        self._agent_topic_type = agent_topic_type
        self._user_topic_type = user_topic_type
        self._delegate_tools: Dict[str, FunctionTool] = {}
        self._delegate_tool_schema: List[Dict[str, Any]] = []
        self._update_delegation_tools()  # Initial update

    def _update_delegation_tools(self) -> None:
        """Updates the delegation tools based on currently registered agents."""
        self._delegate_tools = {}
        self._delegate_tool_schema = []

        # Exclude the current HeadAgent using a case‑insensitive comparison.
        delegate_candidates = {
            agent.type: agent
            for _, agent in self.registered_agent_types.items()
            if agent.type.lower() != self.id.type.lower()
        }

        logger.info(
            f"Updating delegation tools. Candidates: {list(delegate_candidates.keys())}"
        )

        for agent_type_str in delegate_candidates.keys():

            tool_name = f"delegate_to_{agent_type_str}"
            tool_description = f"Delegate task to {agent_type_str} agent for tasks related to {agent_type_str}"

            def create_delegation_func(target_agent_type: str):
                async def _delegate(user_message: str) -> str:
                    logger.info(f"Delegation tool called for {target_agent_type} ")
                    return target_agent_type

                return _delegate

            tool = FunctionTool(
                func=create_delegation_func(agent_type_str),
                name=tool_name,
                description=tool_description,
            )
            self._delegate_tools[tool_name] = tool
            self._delegate_tool_schema.append(tool.schema)

        logger.info(f"Updated delegation tool schema: {self._delegate_tool_schema}")

    @message_handler
    async def handle_user_task(self, message: UserTask, ctx: MessageContext) -> None:
        """Handles incoming tasks from the user (via API endpoint)."""
        logger.info(f"HeadAgent received UserTask with task_id: {message.task_id}")
        if not message.task_id:
            logger.error("HeadAgent received UserTask without task_id. Ignoring.")
            return
        if message.task_id in active_tasks:
            logger.warning(
                f"Task {message.task_id} is already active. Ignoring duplicate request."
            )
            # Optionally notify the corresponding queue about the duplicate?
            return

        # Ensure delegation tools are up-to-date *before* the LLM call
        # This handles dynamically registered agents between requests
        if message.registered_agent_types:
            self.registered_agent_types = message.registered_agent_types
            self._update_delegation_tools()

        # Prepare for LLM call
        messages_for_llm = [self._system_message] + message.context
        logger.info(
            f"LLM API Payload for HeadAgent: { {'messages': messages_for_llm, 'tools': self._delegate_tool_schema} }"
        )

        try:
            active_tasks.add(message.task_id)  # Mark task as active
            llm_result = await self._model_client.create(
                messages=messages_for_llm,
                tools=self._delegate_tool_schema,
                cancellation_token=ctx.cancellation_token,
            )
            logger.info(f"HeadAgent LLM result: {llm_result.content}")

            if isinstance(llm_result.content, list) and all(
                isinstance(m, FunctionCall) for m in llm_result.content
            ):
                for call in llm_result.content:
                    if call.name in self._delegate_tools:
                        arguments = json.loads(call.arguments)
                        # The tool execution just returns the target agent type string
                        target_agent_type = await self._delegate_tools[
                            call.name
                        ].run_json(arguments, ctx.cancellation_token)

                        # Create the context for the delegate agent.
                        # Include the original user message and the delegation steps.
                        delegate_messages = list(message.context) + [
                            AssistantMessage(
                                content=[call], source=self.id.type
                            ),  # HeadAgent's decision
                            FunctionExecutionResultMessage(
                                content=[
                                    FunctionExecutionResult(
                                        call_id=call.id,
                                        name=call.name,
                                        content=f"Delegating to {target_agent_type}.",  # Result of the delegation tool call
                                        is_error=False,
                                    )
                                ]
                            ),
                        ]
                        delegated_task = UserTask(
                            context=delegate_messages,
                            task_id=message.task_id,  # Pass the original task_id
                            registered_agent_types=self.registered_agent_types,  # Pass along registered types
                        )

                        logger.info(
                            f"HeadAgent Delegating task {message.task_id} to {target_agent_type}"
                        )
                        await self.publish_message(
                            delegated_task,
                            topic_id=TopicId(target_agent_type, source=self.id.key),
                        )
                    else:
                        logger.error(
                            f"HeadAgent received unknown tool call: {call.name}"
                        )
                        # How to handle error? Send back to user?
                        await self._send_error_to_queue(
                            message.task_id,
                            f"HeadAgent Error: Unknown tool call '{call.name}'",
                        )

            elif isinstance(llm_result.content, str):
                # LLM responded directly without delegating (shouldn't happen based on prompt)
                logger.warning(
                    f"HeadAgent LLM responded directly: {llm_result.content}"
                )
                # Send this direct response back to the user
                await self._send_to_queue(
                    message.task_id, llm_result.content, is_final=True
                )
            else:
                logger.error(
                    f"HeadAgent received unexpected LLM response format: {llm_result.content}"
                )
                await self._send_error_to_queue(
                    message.task_id,
                    "HeadAgent Error: Unexpected response format from LLM.",
                )

        except Exception as e:
            logger.exception(f"Error handling UserTask {message.task_id} in HeadAgent:")
            await self._send_error_to_queue(
                message.task_id, f"HeadAgent Internal Error: {e}"
            )
        # Do not remove from active_tasks here, wait for the final response or error handling

    @message_handler
    async def handle_agent_response(
        self, message: AgentResponse, ctx: MessageContext
    ) -> None:
        """Handles responses coming back from delegated agents."""
        task_id = message.task_id
        logger.info(
            f"HeadAgent received AgentResponse for task_id: {task_id}, is_final: {message.is_final}"
        )

        if task_id not in request_queues:
            logger.warning(
                f"Received AgentResponse for unknown or completed task_id: {task_id}. Ignoring."
            )
            return

        try:
            # Extract the primary content (assuming it's in the last AssistantMessage)
            content_to_send = None
            if message.context and isinstance(message.context[-1], AssistantMessage):
                content_to_send = message.context[-1].content
            else:
                # Fallback or handle cases where context might be different
                logger.warning(
                    f"Could not extract standard content from AgentResponse for task {task_id}. Sending raw context."
                )
                # Serialize the whole context or relevant parts if direct content isn't found
                content_to_send = json.dumps(
                    [msg.model_dump() for msg in message.context]
                )

            if content_to_send:
                # Attempt to parse if it looks like JSON, otherwise send as string
                try:
                    # Check if it's a string that represents a JSON object/array
                    if isinstance(
                        content_to_send, str
                    ) and content_to_send.strip().startswith(("{", "[")):
                        parsed_content = json.loads(content_to_send)
                        await self._send_to_queue(
                            task_id, parsed_content, is_final=message.is_final
                        )
                    else:
                        # Send as is (likely plain text or already processed)
                        await self._send_to_queue(
                            task_id, content_to_send, is_final=message.is_final
                        )
                except json.JSONDecodeError:
                    # If JSON parsing fails, send the raw string content
                    logger.warning(
                        f"Content for task {task_id} looked like JSON but failed to parse. Sending raw string."
                    )
                    await self._send_to_queue(
                        task_id, content_to_send, is_final=message.is_final
                    )
            else:
                logger.warning(
                    f"AgentResponse for task {task_id} had no content to send."
                )
                # If it's the final message but has no content, still need to signal end
                if message.is_final:
                    await self._send_to_queue(
                        task_id, None, is_final=True
                    )  # Send end signal

        except Exception as e:
            logger.exception(
                f"Error processing AgentResponse for task {task_id} in HeadAgent:"
            )
            await self._send_error_to_queue(
                task_id, f"HeadAgent Internal Error processing response: {e}"
            )

    async def _send_to_queue(
        self, task_id: str, content: Optional[Any], is_final: bool
    ):
        """Helper to put content and potentially the end signal onto the request queue."""
        if task_id in request_queues:
            queue = request_queues[task_id]
            if content is not None:
                await queue.put(content)
                logger.debug(f"Put content onto queue for task {task_id}")
            if is_final:
                await queue.put(None)  # Signal end of stream
                logger.info(f"Sent END signal to queue for task {task_id}")
                # Task is considered finished, remove from active set
                active_tasks.discard(task_id)
                # Queue cleanup happens in the endpoint's finally block
        else:
            logger.warning(
                f"_send_to_queue called for inactive/unknown task_id: {task_id}"
            )

    async def _send_error_to_queue(self, task_id: str, error_message: str):
        """Sends a formatted error message and the end signal to the queue."""
        logger.error(f"Sending error for task {task_id}: {error_message}")
        await self._send_to_queue(task_id, {"error": error_message}, is_final=True)
