/**
 * Template Variable Utilities for Frontend
 * 
 * This module provides utilities for detecting, validating, and processing
 * template variables in the frontend input system.
 */

export interface TemplateVariable {
  name: string;
  syntax: '{variable_name}' | '${variable_name}';
  startPos: number;
  endPos: number;
  fullMatch: string;
}

export interface TemplateVariableValidation {
  isValid: boolean;
  message: string;
  variables: TemplateVariable[];
  warnings: string[];
}

/**
 * Regular expression patterns for template variable detection
 */
export const TEMPLATE_VARIABLE_PATTERNS = {
  // Matches {variable_name} but not ${variable_name} or \{variable_name}
  // Using * to allow empty braces for validation
  NEW_SYNTAX: /(?<![\$\\]){([^}]*)}/g,
  // Matches ${variable_name}
  EXISTING_SYNTAX: /\${([^}]*)}/g,
  // Combined pattern for both syntaxes
  COMBINED: /(?<!\\)(?:\${([^}]*)}|(?<!\$){([^}]*)})/g,
} as const;

/**
 * Reserved keywords that cannot be used as template variable names
 */
export const RESERVED_KEYWORDS = [
  'undefined', 'null', 'true', 'false', 'this', 'window', 'document',
  'console', 'process', 'global', 'require', 'module', 'exports',
  'function', 'class', 'const', 'let', 'var', 'if', 'else', 'for',
  'while', 'do', 'switch', 'case', 'default', 'break', 'continue',
  'return', 'try', 'catch', 'finally', 'throw', 'new', 'delete',
  'typeof', 'instanceof', 'in', 'of', 'with', 'debugger', 'yield',
  'async', 'await', 'import', 'export', 'from', 'as', 'default'
] as const;

/**
 * Detect template variables in a string value
 */
export function detectTemplateVariables(value: string): TemplateVariable[] {
  if (typeof value !== 'string') {
    return [];
  }

  const variables: TemplateVariable[] = [];

  // Find {variable_name} patterns (not preceded by $ or \)
  const newSyntaxMatches = Array.from(value.matchAll(TEMPLATE_VARIABLE_PATTERNS.NEW_SYNTAX));
  for (const match of newSyntaxMatches) {
    if (match.index !== undefined && match[1] !== undefined) {
      variables.push({
        name: match[1].trim(),
        syntax: '{variable_name}',
        startPos: match.index,
        endPos: match.index + match[0].length,
        fullMatch: match[0]
      });
    }
  }

  // Find ${variable_name} patterns
  const existingSyntaxMatches = Array.from(value.matchAll(TEMPLATE_VARIABLE_PATTERNS.EXISTING_SYNTAX));
  for (const match of existingSyntaxMatches) {
    if (match.index !== undefined && match[1] !== undefined) {
      variables.push({
        name: match[1].trim(),
        syntax: '${variable_name}',
        startPos: match.index,
        endPos: match.index + match[0].length,
        fullMatch: match[0]
      });
    }
  }

  // Sort by position
  return variables.sort((a, b) => a.startPos - b.startPos);
}

/**
 * Validate template variable names and syntax
 */
export function validateTemplateVariables(value: string): TemplateVariableValidation {
  const variables = detectTemplateVariables(value);
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check for mixed syntax usage
  const hasNewSyntax = variables.some(v => v.syntax === '{variable_name}');
  const hasExistingSyntax = variables.some(v => v.syntax === '${variable_name}');
  
  if (hasNewSyntax && hasExistingSyntax) {
    warnings.push('Mixed template variable syntax detected. Consider using consistent syntax throughout.');
  }

  // Validate each variable
  for (const variable of variables) {
    const name = variable.name;

    // Check for empty names (including whitespace-only names)
    if (!name || name.length === 0) {
      errors.push('Empty template variable name detected');
      continue;
    }

    // Check for reserved keywords
    if (RESERVED_KEYWORDS.includes(name.toLowerCase() as any)) {
      errors.push(`Template variable name '${name}' is a reserved keyword`);
    }

    // Check for valid variable name format (alphanumeric, underscore, hyphen)
    if (!/^[a-zA-Z_][a-zA-Z0-9_-]*$/.test(name)) {
      errors.push(`Template variable name '${name}' contains invalid characters. Use only letters, numbers, underscores, and hyphens.`);
    }

    // Check for excessively long names
    if (name.length > 50) {
      warnings.push(`Template variable name '${name}' is very long (${name.length} characters). Consider using shorter names.`);
    }
  }

  return {
    isValid: errors.length === 0,
    message: errors.length > 0 ? errors[0] : warnings.length > 0 ? warnings[0] : 'Valid template variables',
    variables,
    warnings
  };
}

/**
 * Check if a string contains template variables
 */
export function hasTemplateVariables(value: string): boolean {
  if (typeof value !== 'string') {
    return false;
  }
  // Test for both patterns separately since the combined pattern might not work as expected
  return TEMPLATE_VARIABLE_PATTERNS.NEW_SYNTAX.test(value) || TEMPLATE_VARIABLE_PATTERNS.EXISTING_SYNTAX.test(value);
}

/**
 * Extract unique template variable names from a string
 */
export function extractVariableNames(value: string): string[] {
  const variables = detectTemplateVariables(value);
  const uniqueNames = new Set(variables.map(v => v.name));
  return Array.from(uniqueNames);
}

/**
 * Highlight template variables in a string for display purposes
 * Returns an array of text segments with highlighting information
 */
export interface TextSegment {
  text: string;
  isVariable: boolean;
  variableInfo?: TemplateVariable;
}

export function highlightTemplateVariables(value: string): TextSegment[] {
  if (typeof value !== 'string') {
    return [{ text: String(value), isVariable: false }];
  }

  const variables = detectTemplateVariables(value);
  if (variables.length === 0) {
    return [{ text: value, isVariable: false }];
  }

  const segments: TextSegment[] = [];
  let lastIndex = 0;

  for (const variable of variables) {
    // Add text before the variable
    if (variable.startPos > lastIndex) {
      segments.push({
        text: value.slice(lastIndex, variable.startPos),
        isVariable: false
      });
    }

    // Add the variable
    segments.push({
      text: variable.fullMatch,
      isVariable: true,
      variableInfo: variable
    });

    lastIndex = variable.endPos;
  }

  // Add remaining text
  if (lastIndex < value.length) {
    segments.push({
      text: value.slice(lastIndex),
      isVariable: false
    });
  }

  return segments;
}

/**
 * Get template variable suggestions based on available variables
 * This would typically be connected to the workflow context
 */
export function getVariableSuggestions(
  currentInput: string,
  availableVariables: string[] = []
): string[] {
  // Extract the current variable being typed
  const cursorPos = currentInput.length;
  const beforeCursor = currentInput.slice(0, cursorPos);
  
  // Find if we're currently typing a variable
  const match = beforeCursor.match(/\{([^}]*)$/);
  if (!match) {
    return [];
  }

  const partialName = match[1].toLowerCase();
  
  // Filter available variables based on partial match
  return availableVariables
    .filter(name => name.toLowerCase().includes(partialName))
    .sort((a, b) => {
      // Prioritize exact prefix matches
      const aStartsWith = a.toLowerCase().startsWith(partialName);
      const bStartsWith = b.toLowerCase().startsWith(partialName);
      
      if (aStartsWith && !bStartsWith) return -1;
      if (!aStartsWith && bStartsWith) return 1;
      
      return a.localeCompare(b);
    })
    .slice(0, 10); // Limit to 10 suggestions
}

/**
 * Insert a template variable at the current cursor position
 */
export function insertTemplateVariable(
  currentValue: string,
  variableName: string,
  cursorPosition: number,
  useDollarSyntax: boolean = false
): { newValue: string; newCursorPosition: number } {
  const syntax = useDollarSyntax ? `\${${variableName}}` : `{${variableName}}`;
  
  const before = currentValue.slice(0, cursorPosition);
  const after = currentValue.slice(cursorPosition);
  
  const newValue = before + syntax + after;
  const newCursorPosition = cursorPosition + syntax.length;
  
  return { newValue, newCursorPosition };
}
