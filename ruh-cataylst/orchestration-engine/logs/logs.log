2025-07-01 11:56:45 - Main - INFO - Starting Server
2025-07-01 11:56:45 - Main - INFO - Connection at: **************:9092
2025-07-01 11:56:45 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 11:56:45 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-01 11:56:45 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 11:56:45 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 11:56:45 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 11:56:46 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 11:56:46 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 11:56:48 - <PERSON><PERSON><PERSON><PERSON>ger - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 11:56:50 - PostgresManager - INFO - PostgreSQL connection pool created
2025-07-01 11:56:50 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-07-01 11:56:53 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-01 11:56:53 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-07-01 11:56:53 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 11:56:55 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 11:56:55 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 11:56:57 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 11:56:57 - RedisEventListener - INFO - Starting Redis event listener thread
2025-07-01 11:56:57 - RedisEventListener - INFO - Redis event listener started
2025-07-01 11:56:57 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-07-01 11:56:57 - StateManager - DEBUG - Using provided database connections
2025-07-01 11:56:57 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 11:56:57 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 11:56:57 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 11:56:57 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-07-01 11:56:58 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 11:56:58 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 11:56:58 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-07-01 11:56:58 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-07-01 11:56:58 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-07-01 11:56:58 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-07-01 11:57:01 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-07-01 11:57:01 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-07-01 11:57:01 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-07-01 11:57:04 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-07-01 11:57:10 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-07-01 11:57:10 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-07-01 11:57:10 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-07-01 11:57:16 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-07-01 11:57:16 - NodeExecutor - INFO - Background result consumer loop started.
2025-07-01 11:57:16 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-07-01 11:57:22 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-07-01 11:57:22 - AgentExecutor - INFO - Background result consumer loop started.
2025-07-01 11:57:53 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 11:57:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 11:57:54 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 11:57:54 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 11:58:46 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-07-01 11:58:46 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-07-01 11:58:46 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-07-01 11:58:46 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-07-01 11:58:46 - Main - ERROR - Shutting down due to keyboard interrupt...
