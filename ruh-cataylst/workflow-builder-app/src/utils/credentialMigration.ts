/**
 * Credential Migration Utility
 * Handles migration from localStorage mock system to API-based system
 */

import { createCredential, fetchCredentials } from '@/lib/api';
import type { Credential, CredentialCreate } from '@/types/credentials';
import { getErrorMessage } from './credentialErrorHandler';

export interface MigrationResult {
  success: boolean;
  migratedCount: number;
  skippedCount: number;
  errorCount: number;
  errors: Array<{
    credential: any;
    error: string;
  }>;
  details: string[];
}

export interface MigrationProgress {
  total: number;
  processed: number;
  current: string;
  status: 'preparing' | 'migrating' | 'completed' | 'error';
}

/**
 * Get credentials from localStorage (mock system)
 */
export function getLocalStorageCredentials(): any[] {
  try {
    const stored = localStorage.getItem('credentials');
    if (!stored) return [];
    
    const credentials = JSON.parse(stored);
    return Array.isArray(credentials) ? credentials : [];
  } catch (error) {
    console.error('Error reading localStorage credentials:', error);
    return [];
  }
}

/**
 * Check if migration is needed
 */
export async function isMigrationNeeded(): Promise<{
  needed: boolean;
  localCount: number;
  apiCount: number;
  reason: string;
}> {
  try {
    const localCredentials = getLocalStorageCredentials();
    const localCount = localCredentials.length;

    if (localCount === 0) {
      return {
        needed: false,
        localCount: 0,
        apiCount: 0,
        reason: 'No credentials in localStorage to migrate',
      };
    }

    // Check API credentials
    const apiResponse = await fetchCredentials();
    const apiCount = apiResponse.credentials.length;

    if (apiCount > 0) {
      return {
        needed: false,
        localCount,
        apiCount,
        reason: 'API already has credentials - migration may have been completed',
      };
    }

    return {
      needed: true,
      localCount,
      apiCount,
      reason: `Found ${localCount} credentials in localStorage that can be migrated`,
    };
  } catch (error) {
    console.error('Error checking migration status:', error);
    return {
      needed: false,
      localCount: 0,
      apiCount: 0,
      reason: 'Unable to check migration status due to API error',
    };
  }
}

/**
 * Transform localStorage credential to API format
 */
function transformLocalCredentialToAPI(localCred: any): CredentialCreate | null {
  try {
    // Handle different localStorage formats
    if (!localCred || typeof localCred !== 'object') {
      return null;
    }

    // Extract name - try different possible field names
    const name = localCred.name || localCred.key || localCred.id || 'Migrated Credential';
    
    // Extract value - try different possible field names
    const value = localCred.value || localCred.secret || localCred.token || '';
    
    if (!value) {
      return null; // Skip credentials without values
    }

    // Extract description - try different possible field names
    const description = localCred.description || 
                       localCred.desc || 
                       localCred.type || 
                       'Migrated from localStorage';

    return {
      name: String(name).slice(0, 20), // Ensure name length limit
      description: String(description).slice(0, 200), // Ensure description length limit
      value: String(value),
    };
  } catch (error) {
    console.error('Error transforming credential:', error);
    return null;
  }
}

/**
 * Migrate credentials from localStorage to API
 */
export async function migrateCredentials(
  onProgress?: (progress: MigrationProgress) => void
): Promise<MigrationResult> {
  const result: MigrationResult = {
    success: false,
    migratedCount: 0,
    skippedCount: 0,
    errorCount: 0,
    errors: [],
    details: [],
  };

  try {
    // Get localStorage credentials
    const localCredentials = getLocalStorageCredentials();
    
    if (localCredentials.length === 0) {
      result.success = true;
      result.details.push('No credentials found in localStorage');
      onProgress?.({
        total: 0,
        processed: 0,
        current: '',
        status: 'completed',
      });
      return result;
    }

    result.details.push(`Found ${localCredentials.length} credentials in localStorage`);

    // Update progress
    onProgress?.({
      total: localCredentials.length,
      processed: 0,
      current: 'Preparing migration...',
      status: 'preparing',
    });

    // Process each credential
    for (let i = 0; i < localCredentials.length; i++) {
      const localCred = localCredentials[i];
      const credentialName = localCred?.name || localCred?.key || `Credential ${i + 1}`;

      onProgress?.({
        total: localCredentials.length,
        processed: i,
        current: `Migrating: ${credentialName}`,
        status: 'migrating',
      });

      try {
        // Transform to API format
        const apiCredential = transformLocalCredentialToAPI(localCred);
        
        if (!apiCredential) {
          result.skippedCount++;
          result.details.push(`Skipped invalid credential: ${credentialName}`);
          continue;
        }

        // Create via API
        await createCredential(apiCredential);
        result.migratedCount++;
        result.details.push(`Successfully migrated: ${apiCredential.name}`);

      } catch (error) {
        result.errorCount++;
        const errorMessage = error && typeof error === 'object' && 'message' in error
          ? getErrorMessage(error as any)
          : error instanceof Error
          ? error.message
          : 'Unknown error';

        result.errors.push({
          credential: localCred,
          error: errorMessage,
        });
        result.details.push(`Failed to migrate ${credentialName}: ${errorMessage}`);
      }
    }

    // Final progress update
    onProgress?.({
      total: localCredentials.length,
      processed: localCredentials.length,
      current: 'Migration completed',
      status: 'completed',
    });

    result.success = result.errorCount === 0;
    result.details.push(
      `Migration completed: ${result.migratedCount} migrated, ${result.skippedCount} skipped, ${result.errorCount} errors`
    );

    return result;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    result.details.push(`Migration failed: ${errorMessage}`);
    
    onProgress?.({
      total: 0,
      processed: 0,
      current: `Error: ${errorMessage}`,
      status: 'error',
    });

    return result;
  }
}

/**
 * Clear localStorage credentials after successful migration
 */
export function clearLocalStorageCredentials(): boolean {
  try {
    localStorage.removeItem('credentials');
    return true;
  } catch (error) {
    console.error('Error clearing localStorage credentials:', error);
    return false;
  }
}

/**
 * Create backup of localStorage credentials before migration
 */
export function backupLocalStorageCredentials(): string | null {
  try {
    const credentials = getLocalStorageCredentials();
    if (credentials.length === 0) return null;

    const backup = {
      timestamp: new Date().toISOString(),
      credentials,
      version: '1.0',
    };

    return JSON.stringify(backup, null, 2);
  } catch (error) {
    console.error('Error creating backup:', error);
    return null;
  }
}

/**
 * Restore credentials from backup
 */
export function restoreFromBackup(backupData: string): boolean {
  try {
    const backup = JSON.parse(backupData);
    
    if (!backup.credentials || !Array.isArray(backup.credentials)) {
      throw new Error('Invalid backup format');
    }

    localStorage.setItem('credentials', JSON.stringify(backup.credentials));
    return true;
  } catch (error) {
    console.error('Error restoring from backup:', error);
    return false;
  }
}

/**
 * Get migration statistics
 */
export async function getMigrationStats(): Promise<{
  localCredentials: number;
  apiCredentials: number;
  migrationRecommended: boolean;
  lastMigrationDate?: string;
}> {
  try {
    const localCredentials = getLocalStorageCredentials().length;
    const apiResponse = await fetchCredentials();
    const apiCredentials = apiResponse.credentials.length;

    // Check if migration was already done
    const lastMigrationDate = localStorage.getItem('credential_migration_date');

    return {
      localCredentials,
      apiCredentials,
      migrationRecommended: localCredentials > 0 && apiCredentials === 0,
      lastMigrationDate: lastMigrationDate || undefined,
    };
  } catch (error) {
    console.error('Error getting migration stats:', error);
    return {
      localCredentials: 0,
      apiCredentials: 0,
      migrationRecommended: false,
    };
  }
}

/**
 * Mark migration as completed
 */
export function markMigrationCompleted(): void {
  try {
    localStorage.setItem('credential_migration_date', new Date().toISOString());
  } catch (error) {
    console.error('Error marking migration as completed:', error);
  }
}
