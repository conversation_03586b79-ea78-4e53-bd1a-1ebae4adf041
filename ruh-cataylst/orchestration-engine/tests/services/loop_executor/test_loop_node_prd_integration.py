"""
Test Loop Node Integration with Orchestration Engine according to PRD.

This module tests integration between loop executor and orchestration engine:
1. Proper transition coordination
2. Loop body chain execution through orchestration
3. Exit transition handling
4. State synchronization
5. Event handling and notifications
6. Workflow context management
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, call
from app.services.loop_executor.loop_executor import <PERSON><PERSON>xecutor
from app.core_.executor_core import EnhancedWorkflowEngine


class TestLoopNodeIntegration:
    """Test loop node integration with orchestration engine according to PRD."""

    @pytest.fixture
    def mock_orchestration_engine(self):
        """Create a mock orchestration engine."""
        mock = Mock(spec=EnhancedWorkflowEngine)
        mock.execute_transition = AsyncMock()
        mock.handle_transition_completion = AsyncMock()
        mock.get_workflow_context = Mock()
        mock.update_workflow_state = Mock()
        return mock

    @pytest.fixture
    def mock_state_manager(self):
        """Create a mock state manager."""
        mock = Mock()
        mock.store_result = Mock()
        mock.get_result = Mock(return_value={"test": "data"})
        mock.mark_transition_started = Mock()
        mock.mark_transition_completed = Mock()
        mock.store_loop_state = Mock()
        mock.save_workflow_state = AsyncMock()
        return mock

    @pytest.fixture
    def loop_executor(self, mock_state_manager, mock_orchestration_engine):
        """Create a loop executor instance for testing."""
        mock_transition_handler = AsyncMock()
        mock_workflow_utils = Mock()
        mock_result_callback = Mock()
        mock_nodes = {
            "loop_node": {
                "id": "loop_node",
                "type": "loop"
            }
        }
        mock_transitions_by_id = {
            "loop_transition": {
                "id": "loop_transition",
                "execution_type": "loop"
            },
            "process_step": {
                "id": "process_step",
                "execution_type": "MCP"
            },
            "final_step": {
                "id": "final_step",
                "execution_type": "MCP"
            },
            "transition-process-step-1": {
                "id": "transition-process-step-1",
                "execution_type": "MCP"
            },
            "transition-process-step-2": {
                "id": "transition-process-step-2",
                "execution_type": "MCP"
            }
        }

        executor = LoopExecutor(
            state_manager=mock_state_manager,
            workflow_utils=mock_workflow_utils,
            result_callback=mock_result_callback,
            transitions_by_id=mock_transitions_by_id,
            nodes=mock_nodes,
            transition_handler=mock_transition_handler,
            user_id="test_user"
        )
        
        # Inject orchestration engine
        executor.orchestration_engine = mock_orchestration_engine
        return executor

    # ========================================
    # TRANSITION COORDINATION TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_loop_body_transition_coordination(self, loop_executor, mock_orchestration_engine):
        """Test coordination of loop body transitions with orchestration engine."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "loop_body_transitions": ["transition-process-step-1", "transition-process-step-2"]
        }

        # Mock orchestration engine responses
        mock_orchestration_engine.execute_transition.return_value = {"result": "processed"}

        with patch.object(loop_executor, '_coordinate_with_orchestration_engine', new_callable=AsyncMock) as mock_coordinate:
            mock_coordinate.return_value = {"result": "coordinated"}
            
            await loop_executor.execute_tool(
                loop_config=loop_config,
                input_data={},
                output_routing={"iteration_output": "process_step", "exit_output": "final_step"},
                transition_id="loop_transition"
            )

            # Verify coordination was called
            mock_coordinate.assert_called()

    @pytest.mark.asyncio
    async def test_exit_transition_execution_through_orchestration(self, loop_executor, mock_orchestration_engine):
        """Test that exit transitions are executed through orchestration engine."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        # Mock successful loop execution
        with patch.object(loop_executor, 'execute_loop_with_outputs', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = ["result1"]
            
            await loop_executor.execute_tool(
                loop_config=loop_config,
                input_data={},
                output_routing={"iteration_output": "process_step", "exit_output": "final_step"},
                transition_id="loop_transition"
            )

            # Verify orchestration engine was used for exit transition
            mock_orchestration_engine.execute_transition.assert_called()

    @pytest.mark.asyncio
    async def test_transition_completion_notification(self, loop_executor, mock_orchestration_engine):
        """Test notification of transition completion to orchestration engine."""
        transition_id = "process_step"
        result = {"processed": "data"}

        # Simulate transition completion
        await loop_executor._notify_orchestration_engine_completion(transition_id, result)

        # Verify orchestration engine was notified
        mock_orchestration_engine.handle_transition_completion.assert_called_once_with(
            transition_id, result
        )

    # ========================================
    # WORKFLOW CONTEXT TESTS
    # ========================================

    def test_workflow_context_retrieval(self, loop_executor, mock_orchestration_engine):
        """Test retrieval of workflow context from orchestration engine."""
        # Mock workflow context
        mock_context = {
            "workflow_id": "test_workflow",
            "execution_id": "test_execution",
            "user_id": "test_user"
        }
        mock_orchestration_engine.get_workflow_context.return_value = mock_context

        context = loop_executor._get_workflow_context()

        # Verify context was retrieved correctly
        assert context == mock_context
        mock_orchestration_engine.get_workflow_context.assert_called_once()

    def test_workflow_state_updates(self, loop_executor, mock_orchestration_engine):
        """Test updates to workflow state through orchestration engine."""
        state_update = {
            "loop_transition_id": "loop_transition",
            "current_iteration": 2,
            "total_iterations": 5,
            "loop_state": "running"
        }

        loop_executor._update_workflow_state(state_update)

        # Verify state was updated
        mock_orchestration_engine.update_workflow_state.assert_called_once_with(state_update)

    # ========================================
    # CHAIN EXECUTION INTEGRATION TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_loop_body_chain_execution_integration(self, loop_executor, mock_orchestration_engine):
        """Test integration of loop body chain execution with orchestration."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        # Mock chain execution
        chain_result = {"chain_completed": True, "result": "processed_item1"}
        mock_orchestration_engine.execute_transition.return_value = chain_result

        with patch.object(loop_executor, '_execute_loop_body_chain_with_orchestration', new_callable=AsyncMock) as mock_chain:
            mock_chain.return_value = chain_result
            
            await loop_executor.execute_tool(
                loop_config=loop_config,
                input_data={},
                output_routing={"iteration_output": "process_step", "exit_output": "final_step"},
                transition_id="loop_transition"
            )

            # Verify chain execution was coordinated with orchestration
            mock_chain.assert_called()

    @pytest.mark.asyncio
    async def test_chain_state_synchronization(self, loop_executor, mock_orchestration_engine):
        """Test synchronization of chain state with orchestration engine."""
        chain_id = "test_chain_1"
        chain_state = {
            "chain_id": chain_id,
            "loop_transition_id": "loop_transition",
            "iteration_index": 0,
            "chain_state": "running",
            "pending_transitions": ["process_step"],
            "completed_transitions": []
        }

        # Synchronize state
        await loop_executor._synchronize_chain_state_with_orchestration(chain_id, chain_state)

        # Verify orchestration engine was updated
        mock_orchestration_engine.update_workflow_state.assert_called()

    # ========================================
    # EVENT HANDLING TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_loop_start_event_handling(self, loop_executor, mock_orchestration_engine):
        """Test handling of loop start events."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        # Mock event emission
        with patch.object(loop_executor, '_emit_loop_event') as mock_emit:
            
            await loop_executor.execute_tool(
                loop_config=loop_config,
                input_data={},
                output_routing={"iteration_output": "process_step", "exit_output": "final_step"},
                transition_id="loop_transition"
            )

            # Verify loop start event was emitted
            mock_emit.assert_any_call("loop_started", {
                "loop_transition_id": "loop_transition",
                "total_iterations": 2
            })

    @pytest.mark.asyncio
    async def test_iteration_event_handling(self, loop_executor, mock_orchestration_engine):
        """Test handling of iteration events."""
        # Mock iteration execution
        with patch.object(loop_executor, '_execute_single_iteration', new_callable=AsyncMock) as mock_iteration:
            mock_iteration.return_value = "result"
            
            with patch.object(loop_executor, '_emit_loop_event') as mock_emit:
                
                await loop_executor._execute_single_iteration(0, "item1")

                # Verify iteration events were emitted
                mock_emit.assert_any_call("iteration_started", {
                    "iteration_index": 0,
                    "iteration_item": "item1"
                })

    @pytest.mark.asyncio
    async def test_loop_completion_event_handling(self, loop_executor, mock_orchestration_engine):
        """Test handling of loop completion events."""
        final_results = ["result1", "result2"]

        with patch.object(loop_executor, '_emit_loop_event') as mock_emit:
            
            await loop_executor._finalize_loop_execution(final_results)

            # Verify loop completion event was emitted
            mock_emit.assert_called_with("loop_completed", {
                "final_results": final_results,
                "total_iterations": len(final_results)
            })

    # ========================================
    # ERROR HANDLING INTEGRATION TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_error_propagation_to_orchestration(self, loop_executor, mock_orchestration_engine):
        """Test propagation of errors to orchestration engine."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            },
            "error_handling": {
                "on_iteration_error": "exit_loop"
            }
        }

        # Mock iteration failure
        with patch.object(loop_executor, '_execute_single_iteration', side_effect=Exception("Iteration failed")):
            with patch.object(loop_executor, '_handle_error_with_orchestration', new_callable=AsyncMock) as mock_error:
                
                await loop_executor.execute_tool(
                    loop_config=loop_config,
                    input_data={},
                    output_routing={"iteration_output": "process_step", "exit_output": "final_step"},
                    transition_id="loop_transition"
                )

                # Verify error was handled with orchestration
                mock_error.assert_called()

    @pytest.mark.asyncio
    async def test_recovery_coordination_with_orchestration(self, loop_executor, mock_orchestration_engine):
        """Test coordination of recovery actions with orchestration engine."""
        error = Exception("Test error")
        recovery_action = "retry_iteration"

        # Mock recovery coordination
        with patch.object(loop_executor, '_coordinate_recovery_with_orchestration', new_callable=AsyncMock) as mock_recovery:
            mock_recovery.return_value = {"action": "retry", "delay": 1}
            
            result = await loop_executor._coordinate_recovery_with_orchestration(error, recovery_action)

            # Verify recovery was coordinated
            assert result["action"] == "retry"
            mock_recovery.assert_called_once_with(error, recovery_action)

    # ========================================
    # STATE PERSISTENCE TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_loop_state_persistence(self, loop_executor, mock_orchestration_engine):
        """Test persistence of loop state through orchestration engine."""
        loop_state = {
            "loop_transition_id": "loop_transition",
            "current_iteration": 1,
            "total_iterations": 3,
            "iteration_results": ["result1"],
            "loop_config": {"iteration_behavior": "independent"}
        }

        # Persist state
        await loop_executor._persist_loop_state(loop_state)

        # Verify state was persisted through orchestration
        mock_orchestration_engine.update_workflow_state.assert_called()

    @pytest.mark.asyncio
    async def test_loop_state_restoration(self, loop_executor, mock_orchestration_engine):
        """Test restoration of loop state from orchestration engine."""
        # Mock persisted state
        persisted_state = {
            "loop_transition_id": "loop_transition",
            "current_iteration": 2,
            "total_iterations": 5,
            "iteration_results": ["result1", "result2"]
        }
        mock_orchestration_engine.get_workflow_context.return_value = {
            "loop_state": persisted_state
        }

        # Restore state
        restored_state = await loop_executor._restore_loop_state("loop_transition")

        # Verify state was restored correctly
        assert restored_state["current_iteration"] == 2
        assert len(restored_state["iteration_results"]) == 2

    # ========================================
    # PERFORMANCE INTEGRATION TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_orchestration_performance_monitoring(self, loop_executor, mock_orchestration_engine):
        """Test performance monitoring integration with orchestration engine."""
        loop_config = {
            "iteration_behavior": "independent",
            "iteration_source": {
                "iteration_list": ["item1", "item2", "item3"]
            },
            "exit_condition": {
                "condition_type": "all_items_processed"
            }
        }

        # Mock performance tracking
        with patch.object(loop_executor, '_track_performance_metrics') as mock_metrics:
            
            await loop_executor.execute_tool(
                loop_config=loop_config,
                input_data={},
                output_routing={"iteration_output": "process_step", "exit_output": "final_step"},
                transition_id="loop_transition"
            )

            # Verify performance metrics were tracked
            mock_metrics.assert_called()

    @pytest.mark.asyncio
    async def test_resource_usage_reporting(self, loop_executor, mock_orchestration_engine):
        """Test reporting of resource usage to orchestration engine."""
        resource_usage = {
            "memory_usage": "50MB",
            "cpu_usage": "25%",
            "execution_time": "2.5s",
            "iterations_completed": 10
        }

        # Report resource usage
        await loop_executor._report_resource_usage(resource_usage)

        # Verify usage was reported to orchestration
        mock_orchestration_engine.update_workflow_state.assert_called()
