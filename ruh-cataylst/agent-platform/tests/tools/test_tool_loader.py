import pytest
from app.tools.tool_loader import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_tool_loader_initialization(tool_loader):
    assert isinstance(tool_loader, ToolLoader)
    assert len(tool_loader.available_tools) > 0

def test_load_tool(tool_loader):
    tool_name = "example_tool"
    tool = tool_loader.load_tool(tool_name)
    assert tool is not None
    assert tool.name == tool_name

def test_invalid_tool(tool_loader):
    with pytest.raises(ValueError):
        tool_loader.load_tool("nonexistent_tool")

def test_tool_execution(tool_loader):
    tool = tool_loader.load_tool("example_tool")
    result = tool.execute({"param": "test"})
    assert result is not None