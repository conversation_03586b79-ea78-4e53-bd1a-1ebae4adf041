{"workflow_id": "test_loop_workflow", "workflow_name": "Test Loop Workflow", "description": "Test the 6-step loop workflow implementation", "transitions": [{"transition_id": "start_node", "transition_name": "Start Node", "transition_type": "node_executor", "tool_name": "data_preparation", "tool_parameters": {"operation": "prepare_test_data"}, "next_transitions": ["loop_node"], "input_mapping": {}, "output_mapping": {}}, {"transition_id": "loop_node", "transition_name": "Test Loop Node", "transition_type": "loop_executor", "tool_name": "loop_processor", "tool_parameters": {"loop_type": "context_preserving", "iteration_source": {"type": "list", "data": [{"user_id": "user1", "action": "process", "data": "test_data_1"}, {"user_id": "user2", "action": "validate", "data": "test_data_2"}, {"user_id": "user3", "action": "transform", "data": "test_data_3"}]}, "loop_body_configuration": {"entry_transitions": ["dummy_node_1"], "exit_transitions": ["dummy_node_2"], "chain_completion_detection": "explicit_exit_transitions"}, "result_aggregation": {"strategy": "collect_all"}, "error_handling": {"on_iteration_error": "continue"}}, "next_transitions": ["final_node"], "input_mapping": {}, "output_mapping": {}}, {"transition_id": "dummy_node_1", "transition_name": "<PERSON><PERSON> 1 - Entry", "transition_type": "node_executor", "tool_name": "dummy_processor_1", "tool_parameters": {"operation": "process_entry", "simulate_processing": true}, "next_transitions": ["dummy_node_2"], "input_mapping": {"current_item": "{{current_iteration}}"}, "output_mapping": {}}, {"transition_id": "dummy_node_2", "transition_name": "<PERSON><PERSON> 2 - Exit", "transition_type": "node_executor", "tool_name": "dummy_processor_2", "tool_parameters": {"operation": "process_exit", "simulate_processing": true}, "next_transitions": [], "input_mapping": {"processed_data": "{{dummy_node_1.output}}"}, "output_mapping": {}}, {"transition_id": "final_node", "transition_name": "Final Node", "transition_type": "node_executor", "tool_name": "final_processor", "tool_parameters": {"operation": "finalize_results"}, "next_transitions": [], "input_mapping": {"loop_results": "{{loop_node.output}}"}, "output_mapping": {}}]}