import os
import re

import aiohttp
import aiofiles
from pathlib import Path
from typing import List, Any, Optional
from urllib.parse import urlparse

from autogen_core.memory import MemoryContent, MemoryMimeType

from autogen_ext.memory.chromadb import (
    ChromaDBVectorMemory,
    PersistentChromaDBVectorMemoryConfig,
)
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient


class KnowledgeManager:
    """
    Manages knowledge sources for AutoGen agents, including document uploads,
    website links, and direct text input.
    """

    def __init__(
        self,
        collection_name: str = "agent_knowledge",
        persistence_path: Optional[str] = None,
        chunk_size: int = 1500,
        k: int = 3,
        score_threshold: float = 0.4,
    ):
        """
        Initialize the knowledge manager.

        Args:
            collection_name: Name of the ChromaDB collection
            persistence_path: Path to store the ChromaDB data (default: ~/.chromadb_autogen)
            chunk_size: Size of text chunks for indexing
            k: Number of top results to return in queries
            score_threshold: Minimum similarity score for retrieval
        """
        self.chunk_size = chunk_size

        # Set default persistence path if not provided
        if persistence_path is None:
            persistence_path = os.path.join(str(Path.home()), ".chromadb_autogen")

        # Initialize ChromaDB vector memory
        self.memory = ChromaDBVectorMemory(
            config=PersistentChromaDBVectorMemoryConfig(
                collection_name=collection_name,
                persistence_path=persistence_path,
                k=k,
                score_threshold=score_threshold,
            )
        )

        self.indexed_sources = set()

    async def add_text(self, text: str, source_name: str = "direct_input") -> int:
        """
        Add direct text input to the knowledge base.

        Args:
            text: The text content to add
            source_name: A name to identify this text source

        Returns:
            Number of chunks added
        """
        chunks = self._split_text(text)
        added = 0

        for i, chunk in enumerate(chunks):
            await self.memory.add(
                MemoryContent(
                    content=chunk,
                    mime_type=MemoryMimeType.TEXT,
                    metadata={"source": source_name, "chunk_index": i},
                )
            )
            added += 1

        self.indexed_sources.add(source_name)
        return added

    async def add_document(self, file_path: str) -> int:
        """
        Add a document file to the knowledge base.

        Args:
            file_path: Path to the document file

        Returns:
            Number of chunks added
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Document not found: {file_path}")

        try:
            content = await self._read_file(file_path)
            source_name = os.path.basename(file_path)

            # Skip if already indexed
            if source_name in self.indexed_sources:
                return 0

            return await self.add_text(content, source_name)

        except Exception as e:
            raise ValueError(f"Error processing document {file_path}: {str(e)}")

    async def add_website(self, url: str) -> int:
        """
        Add content from a website URL to the knowledge base.

        Args:
            url: The website URL to scrape and add

        Returns:
            Number of chunks added
        """
        # Validate URL
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            raise ValueError(f"Invalid URL: {url}")

        # Skip if already indexed
        if url in self.indexed_sources:
            return 0

        try:
            content = await self._fetch_url(url)
            # Strip HTML if content appears to be HTML
            if "<" in content and ">" in content:
                content = self._strip_html(content)

            return await self.add_text(content, url)

        except Exception as e:
            raise ValueError(f"Error processing URL {url}: {str(e)}")

    async def clear_knowledge(self) -> None:
        """Clear all knowledge from the memory."""
        await self.memory.clear()
        self.indexed_sources = set()

    async def close(self) -> None:
        """Close the memory resources."""
        await self.memory.close()

    def _split_text(self, text: str) -> List[str]:
        """Split text into fixed-size chunks."""
        chunks = []
        # Split text into fixed-size chunks
        for i in range(0, len(text), self.chunk_size):
            chunk = text[i : i + self.chunk_size]
            chunks.append(chunk.strip())
        return chunks

    def _strip_html(self, text: str) -> str:
        """Remove HTML tags and normalize whitespace."""
        text = re.sub(r"<[^>]*>", " ", text)
        text = re.sub(r"\s+", " ", text)
        return text.strip()

    async def _read_file(self, file_path: str) -> str:
        """Read content from a file."""
        async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
            return await f.read()

    async def _fetch_url(self, url: str) -> str:
        """Fetch content from a URL."""
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status != 200:
                    raise ValueError(
                        f"Failed to fetch URL: {url}, status: {response.status}"
                    )
                return await response.text()

    async def enhance_agent_with_knowledge(
        self,
        agent: AssistantAgent,
        system_message_prefix: str = "You have access to a knowledge base with the following information:\n\n",
    ) -> AssistantAgent:
        """
        Enhance an existing agent with the knowledge base.

        Args:
            agent: The AssistantAgent to enhance
            system_message_prefix: Prefix for the system message

        Returns:
            The enhanced agent
        """
        # Add the memory to the agent's memory list
        if not hasattr(agent, "memory") or agent.memory is None:
            agent.memory = [self.memory]
        else:
            agent.memory.append(self.memory)

        return agent

    @classmethod
    async def create_knowledgeable_agent(
        cls,
        name: str,
        model: str,
        api_key: Optional[str] = None,
        system_message: str = "You are a helpful AI assistant with access to a knowledge base.",
        collection_name: str = "agent_knowledge",
        tools: List[Any] = None,
    ) -> tuple[AssistantAgent, "KnowledgeManager"]:
        """
        Create a new agent with knowledge retrieval capabilities.

        Args:
            name: Name of the agent
            model: Model name (e.g., "gpt-4o")
            api_key: OpenAI API key (optional if set in environment)
            system_message: System message for the agent
            collection_name: Name for the knowledge collection
            tools: List of tools for the agent

        Returns:
            Tuple of (agent, knowledge_manager)
        """
        # Create knowledge manager
        knowledge_manager = cls(collection_name=collection_name)

        # Create model client
        model_client = OpenAIChatCompletionClient(
            model=model,
            api_key=api_key,
        )

        # Create agent with memory
        agent = AssistantAgent(
            name=name,
            model_client=model_client,
            system_message=system_message,
            tools=tools or [],
            memory=[knowledge_manager.memory],
        )

        return agent, knowledge_manager
