import asyncio
import uuid
from typing import Dict, List

from autogen_agentchat.agents import Assistant<PERSON><PERSON>
from autogen_agentchat.messages import TextMessage
from autogen_core import CancellationToken
from autogen_core.memory import ListMemory, MemoryContent, MemoryMimeType


from ..autogen_service.model_factory import ModelFactory
from ..schemas.agent_config import AgentConfig, ChatSession
from ..tools.tool_loader import Too<PERSON><PERSON>oader


class AgentChat:
    def __init__(self):
        self.sessions: Dict[str, ChatSession] = {}
        self.agents: Dict[str, AssistantAgent] = {}
        self.cancellation_tokens: Dict[str, CancellationToken] = {}
        self.tool_loader = ToolLoader()
        self._lock = asyncio.Lock()  # Add lock for thread safety

    async def register_agent(self, config: AgentConfig) -> AssistantAgent:
        """Register a new agent with optional memory support"""
        try:
            print("config", config)

            # Create model configuration from agent config
            model_config = {
                "provider": "OpenAIChatCompletionClient",
                "model": config.ai_model_config["model"],
                "api_key": config.ai_model_config["api_key"],
            }

            # Add optional parameters if present
            for param in ["temperature", "max_tokens"]:
                if param in config.ai_model_config:
                    model_config[param] = config.ai_model_config[param]

            # Use ModelFactory to create the model client
            model_client = ModelFactory.create_model_client(model_config)

            if not model_client:
                raise ValueError("Failed to create model client")

            # Initialize memory if enabled
            memory = None
            if config.memory_enabled:
                memory = ListMemory()
                if config.memory_config and "initial_entries" in config.memory_config:
                    for entry in config.memory_config["initial_entries"]:
                        await memory.add(
                            MemoryContent(
                                content=entry,
                                mime_type=MemoryMimeType.TEXT,
                            )
                        )

            # Create the agent
            agent = AssistantAgent(
                name=config.name,
                model_client=model_client,
                system_message=config.system_message,
                tools=[],  # You can add tools loading here
                memory=[memory] if memory else None,
            )

            async with self._lock:
                self.agents[config.name] = agent
            return agent
        except Exception as e:
            raise ValueError(f"Failed to register agent: {str(e)}")

    async def start_chat_session(self, agent_name: str) -> str:
        """Start a new chat session with an agent"""
        if agent_name not in self.agents:
            raise ValueError(f"Agent {agent_name} not found")

        session_id = str(uuid.uuid4())
        agent = self.agents[agent_name]

        session = ChatSession(
            session_id=session_id,
            agent_id=agent_name,
            memory=ListMemory() if getattr(agent, "_memory", None) else None,
        )

        async with self._lock:
            self.sessions[session_id] = session
            self.cancellation_tokens[session_id] = CancellationToken()

        return session_id

    async def send_message(
        self, session_id: str, message: str, add_to_memory: bool = False
    ) -> TextMessage:
        """Send a message to the agent in a specific session"""
        if session_id not in self.sessions:
            raise ValueError(f"Session {session_id} not found")

        session = self.sessions[session_id]
        agent = self.agents[session.agent_id]

        # Add message to memory if enabled
        if add_to_memory and session.memory:
            await session.memory.add(
                MemoryContent(
                    content=message,
                    mime_type=MemoryMimeType.TEXT,
                )
            )

        # Get cancellation token for this session
        cancellation_token = self.cancellation_tokens.get(session_id)

        response = await agent.on_messages(
            [TextMessage(content=message, source="user")],
            cancellation_token=cancellation_token,
        )
        return response.chat_message

    async def stream_message(
        self, session_id: str, message: str, add_to_memory: bool = False
    ):
        """Stream chat message response using server-sent events"""
        if session_id not in self.sessions:
            raise ValueError(f"Session {session_id} not found")

        session = self.sessions[session_id]
        agent = self.agents[session.agent_id]

        if add_to_memory and session.memory:
            await session.memory.add(
                MemoryContent(
                    content=message,
                    mime_type=MemoryMimeType.TEXT,
                )
            )

        # Get cancellation token for this session
        cancellation_token = self.cancellation_tokens.get(session_id)

        # Return the asynchronous generator from the agent's run_stream()
        return agent.run_stream(task=message, cancellation_token=cancellation_token)

    async def end_chat_session(self, session_id: str):
        """End a chat session and cleanup resources"""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            if session.memory:
                await session.memory.close()

            async with self._lock:
                if session_id in self.cancellation_tokens:
                    del self.cancellation_tokens[session_id]
                if session_id in self.sessions:
                    del self.sessions[session_id]

    def _load_tools(self, tool_names: List[str]) -> List:
        """Load tools based on configuration"""
        # Implement your tool loading logic here
        return []
